package com.ge.med.ct.laf2;

import javax.swing.*;

/**
 * 主菜单构建器
 * 负责创建和配置应用程序主菜单
 */
public class MainMenuBuilder {
    
    private final JFrame mainFrame;
    
    /**
     * 构造函数
     * @param mainFrame 主窗口
     */
    public MainMenuBuilder(JFrame mainFrame) {
        this.mainFrame = mainFrame;
    }
    
    /**
     * 创建菜单栏
     * @return 菜单栏
     */
    public JMenuBar createMenuBar() {
        JMenuBar menuBar = new JMenuBar();
        
        // 文件菜单
        JMenu fileMenu = new JMenu("文件");
        JMenuItem exitItem = new JMenuItem("退出");
        exitItem.addActionListener(e -> System.exit(0));
        fileMenu.add(exitItem);
        
        // 帮助菜单
        JMenu helpMenu = new JMenu("帮助");
        JMenuItem aboutItem = new JMenuItem("关于");
        aboutItem.addActionListener(e -> showAboutDialog());
        helpMenu.add(aboutItem);
        
        // 添加菜单到菜单栏
        menuBar.add(fileMenu);
        menuBar.add(helpMenu);
        
        return menuBar;
    }
    
    /**
     * 显示关于对话框
     */
    private void showAboutDialog() {
        JOptionPane.showMessageDialog(
                mainFrame,
                "CT质量保证工具\n版本: 1.0\n\n用于CT设备质量保证的DICOM分析工具",
                "关于",
                JOptionPane.INFORMATION_MESSAGE
        );
    }
} 