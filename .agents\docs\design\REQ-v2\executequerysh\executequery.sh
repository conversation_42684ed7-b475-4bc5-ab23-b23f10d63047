#!/bin/bash
PG_DIR=$NUEVO_HOME/resources/dbx;
CERT_PATH="$PG_DIR/certs";
PSQL="psql"
PGPORT=5432
re=^[0-9]+$
if [ ! -z "$CSE_DBPORT" ]; then
 if [[ "$CSE_DBPORT" =~ $re ]] ; then
  PGPORT=${CSE_DBPORT//[[:blank:]]/}
 fi
fi
export PGPORT=$PGPORT
nuevo_image_check=`cat $PG_DIR/.data.properties | grep nuevo_image_check | cut -d '=' -f 2-`
nuevo_ssl_check=`cat $PG_DIR/.data.properties | grep nuevo_ssl_check | cut -d '=' -f 2-`
check_encryption(){
#Run the openssl command
if [ "X$nuevo_image_check" = "Xtrue" ]; then
  openssl_path=$NUEVO_HOME/scripts/util
  PASSWDOUT=`$openssl_path/.openssl.sh`
  echo "`cat $PG_DIR/.data.properties | grep ownerin | cut -d '=' -f 2-`"
  echo "${PASSWDOUT[0]}" 

else
  echo $SDCUSER
fi

}

# This is added to handle the other options like load or update_stat
# without much complexity of connectivity param
if [ "X$nuevo_image_check" = "Xtrue" ] && [ "X$nuevo_ssl_check" = "Xtrue" ];  	
then
	if [ -z "$PGSSLCERT" ];then
		export PGSSLCERT="$CERT_PATH/client.crt"
	fi
	if [ -z "$PGSSLKEY" ];then
		export PGSSLKEY="$CERT_PATH/client.key"
	fi
	if [ -z "$PGSSLROOTCERT" ];then
		export PGSSLROOTCERT="$CERT_PATH/rootCA.crt"
	fi
	if [ -z "$PGSSLMODE" ];then
		export PGSSLMODE=verify-full
	fi
	if [ -z "$PGHOST" ];then
		export PGHOST=localhost
	fi	
fi

usage(){
        echo ""
        echo "usage : -c < to enter commands for different queries> "
        echo "example command: ./executequery.sh -c \"select count(*) from patient;\""
}

if [ "$1" == "-c" ] && [ "$2" != "" ]; then
   commandinput=$(echo "$2" | wc -w)
        if [ $commandinput -eq 1 ]; then
                usage
                exit 1
        else
                var=($(check_encryption))
                ownerin=${var[0]}
                OWNEROUT=${var[1]}
                query=$2
                PGPASSWORD=$OWNEROUT $PSQL -d dbexpress -U $ownerin -c "$query"
        fi
else
        usage
        exit 1
fi