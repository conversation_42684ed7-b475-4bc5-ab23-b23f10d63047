{ctuser@bj36}executequery.sh -c "select * from patient;"
warning: iv not used by this cipher
warning: iv not used by this cipher
 patient_id |    patient_name_unicode     |     patient_name_native      |   dcm_patient_id_unicode   |   dcm_patient_id_native    | birth_date
------------+-----------------------------+------------------------------+----------------------------+----------------------------+------------
          1 | Patient_2025_05_28_08_54_57 | Patient_2025_05_28_08_54_57  | Trauma_2025_05_28_08_54_57 | Trauma_2025_05_28_08_54_57 |
          2 | Patient_2025_05_28_11_34_17 | Patient_2025_05_28_11_34_17  | Trauma_2025_05_28_11_34_17 | Trauma_2025_05_28_11_34_17 |
          3 |                             |                              | jgjg                       | jgjg                   |
          4 | Patient_2025_05_28_14_18_03 | Patient_2025_05_28_14_18_03  | Trauma_2025_05_28_14_18_03 | Trauma_2025_05_28_14_18_03 |
          5 | Patient_2025_05_28_14_34_20 | Patient_2025_05_28_14_34_20  | Trauma_2025_05_28_14_34_20 | Trauma_2025_05_28_14_34_20 |
          6 |                             |                              | ter                        | ter                   |
          7 | 3                           | 3                            | Constraint Test            | Constraint Test            |
          8 | 3                           | 3                            | SQPID3                     | SQPID3                   |
          9 | Zhang^Shuangshuang          | Zhang^Shuangshuang           | **********                 | **********                 |
         10 | John^Smith                  | John^Smith                   | 30000100010                | 30000100010                |
         11 | Lee                         | Lee                          | 30000100015                | 30000100015                |
         12 | Zhang^Shuangshuang          | Zhang^Shuangshuang           | 10000100012                | 10000100012                |
         13 | Lee                         | Lee                          | 10000100014                | 10000100014                |
         14 | john^^joe                   | john^^joe                    | 2000010007                 | 2000010007                 |
         15 | shunagshunag^zhang          | shunagshunag^zhang           | 2000010009                 | 2000010009                 |
         16 | lee                         | lee                          | 2000010020                 | 2000010020                 |
         17 | joe^john                    | joe^john                     | 4000010008                 | 4000010008                 |
         18 | john^smith                  | john^smith                   | 50000100011                | 50000100011                |
         19 | wang5                       | wang5                        | 60000100015                | 60000100015                |
