package com.ge.med.ct.dicom2.service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Logger;

import org.dcm4che3.data.ElementDictionary;
import org.dcm4che3.data.VR;

import com.ge.med.ct.dicom2.tag.DicomTag;
import com.ge.med.ct.dicom2.tag.DicomTagConstants;
import com.ge.med.ct.dicom2.tag.DicomTagValueConverter;

// 静态DICOM标签服务
public final class DicomTagService {
    private static final Logger LOG = Logger.getLogger(DicomTagService.class.getName());

    private static final ElementDictionary DICT = ElementDictionary.getStandardElementDictionary();

    // 核心静态映射
    private static final Map<String, String> TAG_MAPPINGS = new HashMap<>();
    private static final Map<String, String> GE_CT_TAG_MAPPINGS = new HashMap<>();
    private static final Map<String, VR> TAG_VR_MAPPINGS = new HashMap<>();
    private static final Map<String, String> TAG_NAME_TO_ID = new HashMap<>();

    static {
        initializeTagMappings();
        LOG.info("Static DicomTagService initialized successfully");
    }

    private DicomTagService() {
        throw new UnsupportedOperationException("This is a utility class and cannot be instantiated");
    }

    private static void initializeTagMappings() {
        // 患者相关标签
        registerTag(DicomTagConstants.Patient.PATIENT_NAME, "Patient's Name");
        registerTag(DicomTagConstants.Patient.PATIENT_ID, "Patient ID");
        registerTag(DicomTagConstants.Patient.PATIENT_BIRTH_DATE, "Patient's Birth Date");
        registerTag(DicomTagConstants.Patient.PATIENT_SEX, "Patient's Sex");
        registerTag(DicomTagConstants.Patient.PATIENT_AGE, "Patient's Age");
        registerTag(DicomTagConstants.Patient.PATIENT_WEIGHT, "Patient's Weight");
        registerTag(DicomTagConstants.Patient.PATIENT_SIZE, "Patient's Size");
        registerTag(DicomTagConstants.Patient.OTHER_PATIENT_IDS, "Other Patient IDs");
        registerTag(DicomTagConstants.Patient.ADDITIONAL_PATIENT_HISTORY, "Additional Patient History");

        // 研究相关标签
        registerTag(DicomTagConstants.Study.STUDY_INSTANCE_UID, "Study Instance UID");
        registerTag(DicomTagConstants.Study.STUDY_DATE, "Study Date");
        registerTag(DicomTagConstants.Study.STUDY_TIME, "Study Time");
        registerTag(DicomTagConstants.Study.STUDY_ID, "Study ID");
        registerTag(DicomTagConstants.Study.STUDY_DESCRIPTION, "Study Description");
        registerTag(DicomTagConstants.Study.ACCESSION_NUMBER, "Accession Number");
        registerTag(DicomTagConstants.Study.REFERRING_PHYSICIAN_NAME, "Referring Physician's Name");
        registerTag(DicomTagConstants.Study.PROTOCOL_NAME, "Protocol Name");

        // 序列相关标签
        registerTag(DicomTagConstants.Series.SERIES_INSTANCE_UID, "Series Instance UID");
        registerTag(DicomTagConstants.Series.SERIES_NUMBER, "Series Number");
        registerTag(DicomTagConstants.Series.SERIES_DATE, "Series Date");
        registerTag(DicomTagConstants.Series.SERIES_TIME, "Series Time");
        registerTag(DicomTagConstants.Series.SERIES_DESCRIPTION, "Series Description");
        registerTag(DicomTagConstants.Series.MODALITY, "Modality");
        registerTag(DicomTagConstants.Series.BODY_PART_EXAMINED, "Body Part Examined");
        registerTag(DicomTagConstants.Series.SERIES_STATUS, "Series Status");

        // 图像相关标签
        registerTag(DicomTagConstants.Image.SOP_INSTANCE_UID, "SOP Instance UID");
        registerTag(DicomTagConstants.Image.INSTANCE_NUMBER, "Instance Number");
        registerTag(DicomTagConstants.Image.IMAGE_TYPE, "Image Type");
        registerTag(DicomTagConstants.Image.ROWS, "Rows");
        registerTag(DicomTagConstants.Image.COLUMNS, "Columns");
        registerTag(DicomTagConstants.Image.PIXEL_SPACING, "Pixel Spacing");
        registerTag(DicomTagConstants.Image.SLICE_THICKNESS, "Slice Thickness");
        registerTag(DicomTagConstants.Image.SLICE_LOCATION, "Slice Location");
        registerTag(DicomTagConstants.Image.IMAGE_POSITION_PATIENT, "Image Position (Patient)");
        registerTag(DicomTagConstants.Image.IMAGE_ORIENTATION_PATIENT, "Image Orientation (Patient)");
        registerTag(DicomTagConstants.Image.WINDOW_CENTER, "Window Center");
        registerTag(DicomTagConstants.Image.WINDOW_WIDTH, "Window Width");
        registerTag(DicomTagConstants.Image.PIXEL_DATA, "Pixel Data");
        registerTag(DicomTagConstants.Image.SPACING_BETWEEN_SLICES, "Spacing Between Slices");

        // 设备相关标签
        registerTag(DicomTagConstants.Equipment.MANUFACTURER, "Manufacturer");
        registerTag(DicomTagConstants.Equipment.MANUFACTURER_MODEL_NAME, "Manufacturer's Model Name");
        registerTag(DicomTagConstants.Equipment.DEVICE_SERIAL_NUMBER, "Device Serial Number");
        registerTag(DicomTagConstants.Equipment.SOFTWARE_VERSIONS, "Software Versions");
        registerTag(DicomTagConstants.Equipment.STATION_NAME, "Station Name");

        // CT相关标签
        registerTag(DicomTagConstants.CT.KVP, "KVP");
        registerTag(DicomTagConstants.CT.XRAY_TUBE_CURRENT, "X-Ray Tube Current");
        registerTag(DicomTagConstants.CT.EXPOSURE_TIME, "Exposure Time");
        registerTag(DicomTagConstants.CT.FIELD_OF_VIEW, "Field of View");
        registerTag(DicomTagConstants.CT.GANTRY_TILT, "Gantry/Detector Tilt");
        registerTag(DicomTagConstants.CT.CONVOLUTION_KERNEL, "Convolution Kernel");
        registerTag(DicomTagConstants.CT.PATIENT_POSITION, "Patient Position");
        registerTag(DicomTagConstants.CT.RECONSTRUCTION_DIAMETER, "Reconstruction Diameter");

        // 添加配置文件中使用的列名映射
        registerTag(DicomTagConstants.Equipment.STATION_NAME, "StationName");
        registerTag(DicomTagConstants.CT.GANTRY_TILT, "GantryTilt");
        registerTag(DicomTagConstants.CT.FIELD_OF_VIEW, "FieldOfView");
        registerTag(DicomTagConstants.CT.RECONSTRUCTION_DIAMETER, "ReconstructionDiameter");
        registerTag(DicomTagConstants.CT.CONVOLUTION_KERNEL, "ConvolutionKernel");
        registerTag(DicomTagConstants.Image.ROWS, "Rows");
        registerTag(DicomTagConstants.Image.IMAGE_COMMENTS, "ImageComments");
        
        // 添加缺失的标签映射
        registerTag(DicomTagConstants.Patient.PATIENT_ID, "PatientID");
        registerTag(DicomTagConstants.Study.STUDY_ID, "StudyID");
        registerTag(DicomTagConstants.Patient.PATIENT_NAME, "PatientName");
        registerTag(DicomTagConstants.Study.STUDY_DATE, "StudyDate");
        registerTag(DicomTagConstants.Study.STUDY_DESCRIPTION, "StudyDescription");
        registerTag(DicomTagConstants.Series.SERIES_NUMBER, "SeriesNumber");
        registerTag(DicomTagConstants.Series.SERIES_DESCRIPTION, "SeriesDescription");
        registerTag(DicomTagConstants.Image.INSTANCE_NUMBER, "InstanceNumber");
        registerTag(DicomTagConstants.Image.SLICE_THICKNESS, "SliceThickness");
        
        // 添加特殊列映射
        registerTag(DicomTagConstants.Series.SERIES_INSTANCE_UID, "ImageCount"); // ImageCount是特殊计算列
        registerTag(DicomTagConstants.Image.IMAGE_POSITION_PATIENT, "ImagePosition"); // ImagePosition映射到图像位置

        registerGECTTag("(0043,1027)", "GE CT Dose");
        registerGECTTag("(0043,1028)", "GE CT Protocol");
        registerGECTTag("(0043,1029)", "GE CT Technique");
        registerGECTTag("(0043,102A)", "GE CT Parameters");
        registerGECTTag("(0043,102F)", "GE CT Image Type");

        // 构建反向映射
        buildReverseMapping();
    }

    private static void registerTag(String tagId, String tagName) {
        TAG_MAPPINGS.put(tagId, tagName);
        try {
            int tag = DicomTagValueConverter.parseTagId(tagId);
            VR vr = DICT.vrOf(tag);
            if (vr != null) {
                TAG_VR_MAPPINGS.put(tagId, vr);
            }
        } catch (Exception e) {
            // 忽略VR获取失败
        }
    }

    private static void registerGECTTag(String tagId, String tagName) {
        GE_CT_TAG_MAPPINGS.put(tagId, tagName);
        TAG_VR_MAPPINGS.put(tagId, VR.LO);
    }

    private static void buildReverseMapping() {
        // 标准DICOM标签
        for (Map.Entry<String, String> entry : TAG_MAPPINGS.entrySet()) {
            TAG_NAME_TO_ID.put(entry.getValue(), entry.getKey());
        }

        // GE CT特有标签
        for (Map.Entry<String, String> entry : GE_CT_TAG_MAPPINGS.entrySet()) {
            TAG_NAME_TO_ID.put(entry.getValue(), entry.getKey());
        }
    }

    // 核心静态方法

    public static String getTagName(String tagId) {
        if (tagId == null || tagId.isEmpty()) {
            return "Unknown";
        }

        String tagName = TAG_MAPPINGS.get(tagId);
        if (tagName != null) {
            return tagName;
        }

        tagName = GE_CT_TAG_MAPPINGS.get(tagId);
        if (tagName != null) {
            return tagName;
        }

        try {
            int tag = DicomTagValueConverter.parseTagId(tagId);
            String keyword = DICT.keywordOf(tag);
            if (keyword != null && !keyword.isEmpty()) {
                return keyword;
            }
        } catch (Exception e) {
            // 忽略解析错误
        }

        return "Tag-" + tagId;
    }

    public static VR getTagVR(String tagId) {
        if (tagId == null || tagId.isEmpty()) {
            return VR.UN;
        }

        VR vr = TAG_VR_MAPPINGS.get(tagId);
        if (vr != null) {
            return vr;
        }

        try {
            int tag = DicomTagValueConverter.parseTagId(tagId);
            vr = DICT.vrOf(tag);
            if (vr != null) {
                return vr;
            }
        } catch (Exception e) {
            // 忽略解析错误
        }

        return VR.UN;
    }

    public static boolean isValidTag(String tagId) {
        if (tagId == null || tagId.isEmpty()) {
            return false;
        }

        if (TAG_MAPPINGS.containsKey(tagId)) {
            return true;
        }

        if (GE_CT_TAG_MAPPINGS.containsKey(tagId)) {
            return true;
        }

        try {
            int tag = stringToTag(tagId);
            return DICT.keywordOf(tag) != null;
        } catch (Exception e) {
            return false;
        }
    }

    public static String getTagIdByName(String tagName) {
        if (tagName == null || tagName.isEmpty()) {
            return "";
        }

        String tagId = TAG_NAME_TO_ID.get(tagName);
        if (tagId != null) {
            return tagId;
        }

        for (Map.Entry<String, String> entry : TAG_MAPPINGS.entrySet()) {
            if (tagName.equals(entry.getValue())) {
                return entry.getKey();
            }
        }

        for (Map.Entry<String, String> entry : GE_CT_TAG_MAPPINGS.entrySet()) {
            if (tagName.equals(entry.getValue())) {
                return entry.getKey();
            }
        }

        return "";
    }

    public static boolean containsTagName(String tagName) {
        if (tagName == null || tagName.isEmpty()) {
            return false;
        }

        if (TAG_MAPPINGS.containsValue(tagName)) {
            return true;
        }

        if (GE_CT_TAG_MAPPINGS.containsValue(tagName)) {
            return true;
        }

        return TAG_NAME_TO_ID.containsKey(tagName);
    }

    public static String normalizeTagId(String tagId) {
        if (tagId == null) {
            return null;
        }

        if (tagId.matches("\\([0-9A-Fa-f]{4},[0-9A-Fa-f]{4}\\)")) {
            return tagId.toUpperCase();
        }

        String hexOnly = tagId.replaceAll("[^0-9A-Fa-f]", "");

        if (hexOnly.length() == 8) {
            return "(" + hexOnly.substring(0, 4).toUpperCase() + "," +
                    hexOnly.substring(4, 8).toUpperCase() + ")";
        }

        return tagId;
    }

    public static boolean isPrivateTag(String tagId) {
        if (tagId == null || tagId.isEmpty()) {
            return false;
        }

        try {
            String cleanTagId = tagId.replaceAll("[^0-9A-Fa-f]", "");
            if (cleanTagId.length() >= 4) {
                int group = Integer.parseInt(cleanTagId.substring(0, 4), 16);
                return (group & 1) == 1;
            }
        } catch (NumberFormatException e) {
            // 解析失败
        }

        return false;
    }

    public static DicomTagConstants.TagCategory getTagCategory(String tagId) {
        if (tagId == null || tagId.isEmpty()) {
            return DicomTagConstants.TagCategory.OTHER;
        }

        if (GE_CT_TAG_MAPPINGS.containsKey(tagId)) {
            return DicomTagConstants.TagCategory.GE_CT;
        }

        if (TAG_MAPPINGS.containsKey(tagId)) {
            return DicomTagConstants.TagCategory.STANDARD;
        }

        if (isPrivateTag(tagId)) {
            return DicomTagConstants.TagCategory.PRIVATE;
        }

        return DicomTagConstants.TagCategory.OTHER;
    }

    // 工具方法

    public static int stringToTag(String tagId) {
        return DicomTagValueConverter.parseTagId(tagId);
    }

    public static String tagToString(int tag) {
        return DicomTagValueConverter.tagToString(tag);
    }

    public static List<DicomTag> parseTags(org.dcm4che3.data.Attributes attrs) {
        List<DicomTag> tags = new ArrayList<>();
        if (attrs == null) {
            return tags;
        }

        for (int tag : attrs.tags()) {
            try {
                String tagId = tagToString(tag);
                String tagName = getTagName(tagId);
                VR vr = getTagVR(tagId);
                String value = attrs.getString(tag, "");
                boolean isPrivate = isPrivateTag(tagId);

                DicomTag dicomTag = new DicomTag(tagId, tagName, value, vr, null, isPrivate);
                tags.add(dicomTag);
            } catch (Exception e) {
                // Skip invalid tags
            }
        }

        return tags;
    }
}