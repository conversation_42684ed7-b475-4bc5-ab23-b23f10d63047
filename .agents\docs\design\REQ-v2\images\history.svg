<svg xmlns="http://www.w3.org/2000/svg" xmlns:p="http://www.evolus.vn/Namespace/Pencil" xmlns:pencil="http://www.evolus.vn/Namespace/Pencil" xmlns:html="http://www.w3.org/1999/xhtml" xmlns:xhtml="http://www.w3.org/1999/xhtml" xmlns:office="urn:oasis:names:tc:opendocument:xmlns:office:1.0" xmlns:style="urn:oasis:names:tc:opendocument:xmlns:style:1.0" xmlns:text="urn:oasis:names:tc:opendocument:xmlns:text:1.0" xmlns:table="urn:oasis:names:tc:opendocument:xmlns:table:1.0" xmlns:draw="urn:oasis:names:tc:opendocument:xmlns:drawing:1.0" xmlns:fo="urn:oasis:names:tc:opendocument:xmlns:xsl-fo-compatible:1.0" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:meta="urn:oasis:names:tc:opendocument:xmlns:meta:1.0" xmlns:number="urn:oasis:names:tc:opendocument:xmlns:datastyle:1.0" xmlns:svg="urn:oasis:names:tc:opendocument:xmlns:svg-compatible:1.0" xmlns:chart="urn:oasis:names:tc:opendocument:xmlns:chart:1.0" xmlns:dr3d="urn:oasis:names:tc:opendocument:xmlns:dr3d:1.0" xmlns:math="http://www.w3.org/1998/Math/MathML" xmlns:form="urn:oasis:names:tc:opendocument:xmlns:form:1.0" xmlns:script="urn:oasis:names:tc:opendocument:xmlns:script:1.0" xmlns:ooo="http://openoffice.org/2004/office" xmlns:ooow="http://openoffice.org/2004/writer" xmlns:oooc="http://openoffice.org/2004/calc" xmlns:dom="http://www.w3.org/2001/xml-events" xmlns:xforms="http://www.w3.org/2002/xforms" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:rpt="http://openoffice.org/2005/report" xmlns:of="urn:oasis:names:tc:opendocument:xmlns:of:1.2" xmlns:rdfa="http://docs.oasis-open.org/opendocument/meta/rdfa#" xmlns:field="urn:openoffice:names:experimental:ooo-ms-interop:xmlns:field:1.0" xmlns:regexp="http://exslt.org/regular-expressions" xmlns:em="http://exslt.org/math" xmlns:sodipodi="http://sodipodi.sourceforge.net/DTD/sodipodi-0.dtd" xmlns:inkscape="http://www.inkscape.org/namespaces/inkscape" width="900" height="639" id="exportedSVG" version="1.1" pencil:version="1.2.2" sodipodi:docname="qat"><g inkscape:label="Home" inkscape:groupmode="layer" id="layer_home"><g><rect x="0" y="0" width="900" height="639" fill="none"/><g p:type="Shape" p:def="Evolus.Prototype.GUI:frame" id="16dd3fe80c774e9daf3275e3e1036ae9" transform="matrix(1,0,0,1,0,0)"><p:metadata><p:property name="box">900,639</p:property><p:property name="disabled">false</p:property><p:property name="fillColor">#FAFAFAFF</p:property><p:property name="titleColor">#DEDEDEFF</p:property><p:property name="strokeColor">#666666FF</p:property><p:property name="strokeStyle">1|</p:property><p:property name="textContent">Quality Assurance Tool</p:property><p:property name="textFont">'Arial'|Bold|normal|15px|none|0</p:property><p:property name="textColor">#000000FF</p:property><p:property name="textAlign">0,1</p:property></p:metadata>
            <path style="fill: rgb(250, 250, 250); stroke: rgb(102, 102, 102); stroke-opacity: 1; stroke-width: 1; fill-opacity: 1;" p:name="body" id="156989098fa84db58192be5505270894" d="M 0 32 L 0 0 L 900 0 L 900 639 L 0 639 L 0 32 L 900 32"/>
            <path p:name="menu" id="2385f86ee1f9499d8459d66ade571623" d="M 0.5 0.5 L 899.5 0.5 L 899.5 31 L 0.5 31 z" style="fill: rgb(222, 222, 222); fill-opacity: 1;"/>
            <text p:name="text" id="12c514430ef64aa3bf70c5d4f75f8a3c" transform="translate(10,21)" style="font-family: Arial; font-size: 15px; font-weight: bold; font-style: normal; text-decoration: none; fill: rgb(0, 0, 0); fill-opacity: 1;"><tspan x="0" y="0">Quality Assurance Tool</tspan></text>
        </g><g p:type="Shape" p:def="Evolus.Prototype.GUI:frameControl" id="bd28b958c1c14a369af1fb45120fc838" transform="matrix(1,0,0,1,896.0000133514404,1.0000000298023224)"><p:metadata><p:property name="box">90,30</p:property><p:property name="withMinButton">true</p:property><p:property name="withRestoreButton">true</p:property><p:property name="withCloseButton">true</p:property><p:property name="disabled">false</p:property><p:property name="fillColor">#FAFAFAFF</p:property><p:property name="strokeColor">#666666FF</p:property><p:property name="strokeStyle">2|</p:property></p:metadata>
            <path style="fill: none; stroke: rgb(102, 102, 102); stroke-opacity: 1; stroke-width: 2;" p:name="button" id="64d598d582fb42339385213db6c49927" d="M 66 8 L 82 24 M 82 8 L 66 24 M 39 9 L 53 9 L 53 23 L 39 23 z M 11 23 L 25 23" transform="translate(2.5,-1)"/>
            <path p:name="box" id="97831e430839474f8214fc74c1b9be94" d="M 0 0 L 90 0 L 90 30 L 0 30 z" style="fill: rgb(250, 250, 250); fill-opacity: 0;"/>
        </g><g p:type="Shape" p:def="Evolus.BasicWebElements:pane-v2" id="152868585414434d9fc71d0e428dcb3f" transform="matrix(1,0,0,1,1,32.00000047683716)"><p:metadata><p:property name="radius">10,0</p:property><p:property name="textPadding">0,1.6666666666666667</p:property><p:property name="fixedRadius">false</p:property><p:property name="fillColor">#EEEEEEFF</p:property><p:property name="shadowStyle">0|0|3|1|#000000</p:property><p:property name="shadowColor">#00000000</p:property><p:property name="strokeColor">#CCCCCCFF</p:property><p:property name="strokeStyle">1|</p:property><p:property name="rightBorder">true</p:property><p:property name="bottomBorder">true</p:property><p:property name="textContent"/><p:property name="textFont">PencilUI|normal|normal|13px|none|0</p:property><p:property name="textColor">#000000FF</p:property><p:property name="textAlign">1,1</p:property><p:property name="box">898,50</p:property><p:property name="cornerStyle">none</p:property><p:property name="customStyle"/></p:metadata>
            <foreignObject x="-3" y="-3" width="904" height="56" p:name="htmlObject" id="61f601ed2468463e9dfb23b3a6c2498d" style="padding-top: 3px;">
                <div xmlns="http://www.w3.org/1999/xhtml" style="box-sizing: border-box; overflow: hidden; box-shadow: none; width: 898px; height: 50px; padding: 0px; background-color: rgb(238, 238, 238); x: 10px; border-radius: 0px; border: 1px solid rgb(204, 204, 204); margin-left: 3px;" p:name="div" id="dab7f0da253e4c17bac5b7b5662d2ed3">
                </div>
            </foreignObject>
            <foreignObject x="0" y="0" width="898" height="50" p:name="htmlObject2" id="e00596769cd942d292ff3792898eb272" style="font-family: PencilUI; font-size: 13px; font-weight: normal; font-style: normal; text-decoration: none; fill: rgb(0, 0, 0); fill-opacity: 1; color: rgb(0, 0, 0);">
                <div xmlns="http://www.w3.org/1999/xhtml" style="box-sizing: border-box; overflow: hidden; display: table-cell; width: 898px; height: 50px; padding: 0px; text-align: center; vertical-align: middle;" p:name="div2" id="207fae95520841dea344419726e425e8"><div></div></div>
            </foreignObject>
        </g><g p:type="Shape" p:def="Evolus.WindowsXP.Widgets:TabHeader" id="581a735ea86c4eb2bc93b4d1449ab903" transform="matrix(1,0,0,1,24.00000035762787,50.0000007301569)"><p:metadata><p:property name="box">80,30</p:property><p:property name="disabled">false</p:property><p:property name="tabTitle">Home</p:property><p:property name="textColor">#000000FF</p:property><p:property name="textFont">PencilUI|500|normal|13px|none|0</p:property><p:property name="textAlign">1,1</p:property></p:metadata>
            <g transform="translate(0.5, 2.5)">
                <path style="fill: rgb(245, 244, 234); stroke-width: 1px; fill-opacity: 1; stroke: rgb(145, 167, 180); stroke-opacity: 1;" p:name="rect" id="fdb35231be7647229ef7c6ee482d692e" d="M 0 29.5 L 0 5 C 0 0 0 0 5 0 L 73 0 C 78 0 78 0 78 5 L 78 29.5"/>
                <text x="33.415905" y="14.214675" style="text-align: left; text-anchor: start; dominant-baseline: central; fill: rgb(0, 0, 0); fill-opacity: 1; font-family: PencilUI; font-size: 13px; font-weight: 500; font-style: normal; text-decoration: none;" transform="translate(-11,1)" xml:space="preserve" p:name="text" id="1e89442621e244c6b2d2137c64a09089">Home</text>
            </g>
        </g><g p:type="Shape" p:def="Evolus.WindowsXP.Widgets:TabHeader" id="85b0b85e475b4b23b7bd04202d374da7" transform="matrix(1,0,0,1,117.00000174343586,50.0000007301569)"><p:metadata><p:property name="box">80,30</p:property><p:property name="disabled">false</p:property><p:property name="tabTitle">Report</p:property><p:property name="textColor">#000000FF</p:property><p:property name="textFont">PencilUI|500|normal|13px|none|0</p:property><p:property name="textAlign">1,1</p:property></p:metadata>
            <g transform="translate(0.5, 2.5)">
                <path style="fill: rgb(245, 244, 234); stroke-width: 1px; fill-opacity: 1; stroke: rgb(145, 167, 180); stroke-opacity: 1;" p:name="rect" id="8cf871c850f0403abca63bd821c65501" d="M 0 29.5 L 0 5 C 0 0 0 0 5 0 L 73 0 C 78 0 78 0 78 5 L 78 29.5"/>
                <text x="33.415905" y="14.214675" style="text-align: left; text-anchor: start; dominant-baseline: central; fill: rgb(0, 0, 0); fill-opacity: 1; font-family: PencilUI; font-size: 13px; font-weight: 500; font-style: normal; text-decoration: none;" transform="translate(-13,1)" xml:space="preserve" p:name="text" id="1083072a4b9b484fac082b3223ecb515">Report</text>
            </g>
        </g><g p:type="Shape" p:def="Evolus.WindowsXP.Widgets:TabHeader" id="f49fb79e65f749559d1e11db7ddd0a2d" transform="matrix(1,0,0,1,210.00000312924385,50.0000007301569)"><p:metadata><p:property name="box">100,30</p:property><p:property name="disabled">false</p:property><p:property name="tabTitle">Configuration</p:property><p:property name="textColor">#000000FF</p:property><p:property name="textFont">PencilUI|500|normal|13px|none|0</p:property><p:property name="textAlign">1,1</p:property></p:metadata>
            <g transform="translate(0.5, 2.5)">
                <path style="fill: rgb(245, 244, 234); stroke-width: 1px; fill-opacity: 1; stroke: rgb(145, 167, 180); stroke-opacity: 1;" p:name="rect" id="ce1ba676aea14ce991bb0fe465076d3e" d="M 0 29.5 L 0 5 C 0 0 0 0 5 0 L 93 0 C 98 0 98 0 98 5 L 98 29.5"/>
                <text x="33.415905" y="14.214675" style="text-align: left; text-anchor: start; dominant-baseline: central; fill: rgb(0, 0, 0); fill-opacity: 1; font-family: PencilUI; font-size: 13px; font-weight: 500; font-style: normal; text-decoration: none;" transform="translate(-23,1)" xml:space="preserve" p:name="text" id="d8f718038f634e3796f607be1227bcd8">Configuration</text>
            </g>
        </g><g p:type="Shape" p:def="Evolus.WindowsXP.Widgets:TabHeader" id="98d32bfc2af149f2beebbd7da2076cdb" transform="matrix(1,0,0,1,323.00000481307507,50.0000007301569)"><p:metadata><p:property name="box">80,30</p:property><p:property name="disabled">false</p:property><p:property name="tabTitle">History</p:property><p:property name="textColor">#000000FF</p:property><p:property name="textFont">PencilUI|500|normal|13px|none|0</p:property><p:property name="textAlign">1,1</p:property></p:metadata>
            <g transform="translate(0.5, 2.5)">
                <path style="fill: rgb(245, 244, 234); stroke-width: 1px; fill-opacity: 1; stroke: rgb(145, 167, 180); stroke-opacity: 1;" p:name="rect" id="6f496a713d9a43bbbf169b06329f109a" d="M 0 29.5 L 0 5 C 0 0 0 0 5 0 L 73 0 C 78 0 78 0 78 5 L 78 29.5"/>
                <text x="33.415905" y="14.214675" style="text-align: left; text-anchor: start; dominant-baseline: central; fill: rgb(0, 0, 0); fill-opacity: 1; font-family: PencilUI; font-size: 13px; font-weight: 500; font-style: normal; text-decoration: none;" transform="translate(-14,1)" xml:space="preserve" p:name="text" id="3ccb4dd33fb848eb9e1ee04f487989b3">History</text>
            </g>
        </g><g p:type="Shape" p:def="Evolus.BasicWebElements:pane-v2" id="6871bebd63864fedbf0ae992ce22ea7f" transform="matrix(1,0,0,1,1.0000000149011612,608.0000090450048)"><p:metadata><p:property name="radius">10,0</p:property><p:property name="textPadding">0,0.9999999999999999</p:property><p:property name="fixedRadius">false</p:property><p:property name="fillColor">#EEEEEEFF</p:property><p:property name="shadowStyle">0|0|3|1|#000000</p:property><p:property name="shadowColor">#00000000</p:property><p:property name="strokeColor">#CCCCCCFF</p:property><p:property name="strokeStyle">1|</p:property><p:property name="rightBorder">true</p:property><p:property name="bottomBorder">true</p:property><p:property name="textContent"/><p:property name="textFont">PencilUI|normal|normal|13px|none|0</p:property><p:property name="textColor">#000000FF</p:property><p:property name="textAlign">1,1</p:property><p:property name="box">898,30</p:property><p:property name="cornerStyle">none</p:property><p:property name="customStyle"/></p:metadata>
            <foreignObject x="-3" y="-3" width="904" height="36" p:name="htmlObject" id="23c09e5624034944bdb78591e2e1d2ee" style="padding-top: 3px;">
                <div xmlns="http://www.w3.org/1999/xhtml" style="box-sizing: border-box; overflow: hidden; box-shadow: none; width: 898px; height: 30px; padding: 0px; background-color: rgb(238, 238, 238); x: 10px; border-radius: 0px; border: 1px solid rgb(204, 204, 204); margin-left: 3px;" p:name="div" id="18f5dc3d3b164a1ea12ae143190b82ef">
                </div>
            </foreignObject>
            <foreignObject x="0" y="0" width="898" height="30" p:name="htmlObject2" id="333c00261060401687af47f6120931bd" style="font-family: PencilUI; font-size: 13px; font-weight: normal; font-style: normal; text-decoration: none; fill: rgb(0, 0, 0); fill-opacity: 1; color: rgb(0, 0, 0);">
                <div xmlns="http://www.w3.org/1999/xhtml" style="box-sizing: border-box; overflow: hidden; display: table-cell; width: 898px; height: 30px; padding: 0px; text-align: center; vertical-align: middle;" p:name="div2" id="5e148734bbc34be4a5c3df2e22d62812"><div></div></div>
            </foreignObject>
        </g><g p:type="Shape" p:def="Evolus.Prototype.GUI:frameControl" id="e5d72faaf0644a79a45f5a37854693c2" transform="matrix(1,0,0,1,790.0000000149012,0)"><p:metadata><p:property name="box">100,32</p:property><p:property name="withMinButton">true</p:property><p:property name="withRestoreButton">true</p:property><p:property name="withCloseButton">true</p:property><p:property name="disabled">false</p:property><p:property name="fillColor">#FAFAFAFF</p:property><p:property name="strokeColor">#666666FF</p:property><p:property name="strokeStyle">2|</p:property></p:metadata>
            <path style="fill: none; stroke: rgb(102, 102, 102); stroke-opacity: 1; stroke-width: 2;" p:name="button" id="aae6de62d9c147eab7ab63f99e4510ab" d="M 76 8 L 92 24 M 92 8 L 76 24 M 49 9 L 63 9 L 63 23 L 49 23 z M 21 23 L 35 23" transform="translate(2.5,0)"/>
            <path p:name="box" id="2ba4f2af80864552bba82a4c9278ba90" d="M 0 0 L 100 0 L 100 32 L 0 32 z" style="fill: rgb(250, 250, 250); fill-opacity: 0;"/>
        </g></g></g><g inkscape:label="History Page" inkscape:groupmode="layer" id="layer_history_page"><g><rect x="0" y="0" width="900" height="639" fill="none"/><g p:type="Shape" p:def="Evolus.BasicWebElements:table" id="2fa62be1af9c4910a357f99a179b66f6" transform="matrix(1,0,0,1,175.0000025779009,138.00000205636024)"><p:metadata><p:property name="box">480,295</p:property><p:property name="useHtmlContent">false</p:property><p:property name="fixedHeaderHeight">true</p:property><p:property name="padding">0,4</p:property><p:property name="headerHeight">0,30</p:property><p:property name="h0">41.142857142857146,0</p:property><p:property name="h1">164.57142857142858,0</p:property><p:property name="h2">301.7142857142857,0</p:property><p:property name="h3">356.57142857142856,0</p:property><p:property name="h4">370.2857142857143,0</p:property><p:property name="h5">384.00000000000006,0</p:property><p:property name="h6">397.7142857142858,0</p:property><p:property name="h7">397.7142857142858,0</p:property><p:property name="h8">397.7142857142858,0</p:property><p:property name="h9">397.7142857142858,0</p:property><p:property name="h10">397.7142857142858,0</p:property><p:property name="h11">397.7142857142858,0</p:property><p:property name="content">ID | Report Name | Report Time | Action
1 | Cell Content 1 | 2025.05.25 10:00 | Display
2 | Cell content 2 | 2025.05.25 10:00 | Display
3 | Cell Content 1 | 2025.05.25 10:00 | Display
4 | Cell content 2 | 2025.05.25 10:00 | Display
5 | Cell Content 1 | 2025.05.25 10:00 | Display
6 | Cell content 2 | 2025.05.25 10:00 | Display</p:property><p:property name="textFont">"Liberation Sans",Arial,sans-serif|normal|normal|13px|none|0</p:property><p:property name="textAlign">0,1</p:property><p:property name="customStyle"/><p:property name="textColor">"Liberation Sans",Arial,sans-serif|normal|normal|13px|none|0</p:property><p:property name="fillColor">#00000000</p:property><p:property name="headerTextColor">#000000FF</p:property><p:property name="headerBackground">#CCCCCCFF</p:property><p:property name="strokeColor">#00000055</p:property><p:property name="strokeStyle">1|</p:property></p:metadata>
            <foreignObject x="0" y="0" width="481" height="296" p:name="htmlObject" id="ce794d250af747f085faeaf357990928">
                <div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; width: 480px; height: 295px; color: rgb(0, 0, 0); font-family: &quot;Liberation Sans&quot;, Arial, sans-serif; font-size: 13px; font-weight: normal; font-style: normal; text-decoration: none;" p:name="textDiv" id="665755ea95454a33ba75776302171450"><table style="border-collapse: collapse; background-color: rgba(0, 0, 0, 0); width: 479px; height: 294px; border: solid 1px rgba(0, 0, 0, 0.33); border-width: 0px 1px 1px 0px"><thead><tr style="height: 30px; min-height: 0px"><th style="border: solid 1px rgba(0, 0, 0, 0.33); border-width: 1px 0px 0px 1px; padding: 0px; background-color: rgba(204, 204, 204, 1); color: rgba(0, 0, 0, 1); text-align: left; vertical-align: middle; min-width: 0px; min-height: 0px; height: 30px; overflow: hidden; -moz-box-sizing: border-box; box-sizing: border-box; width: 39.142857142857146px"><div style="overflow: hidden; min-width: 0px; -moz-box-sizing: border-box; box-sizing: border-box; padding: 4px; width: 39.142857142857146px">ID </div></th><th style="border: solid 1px rgba(0, 0, 0, 0.33); border-width: 1px 0px 0px 1px; padding: 0px; background-color: rgba(204, 204, 204, 1); color: rgba(0, 0, 0, 1); text-align: left; vertical-align: middle; min-width: 0px; min-height: 0px; height: 30px; overflow: hidden; -moz-box-sizing: border-box; box-sizing: border-box; width: 121.42857142857144px"><div style="overflow: hidden; min-width: 0px; -moz-box-sizing: border-box; box-sizing: border-box; padding: 4px; width: 121.42857142857144px"> Report Name </div></th><th style="border: solid 1px rgba(0, 0, 0, 0.33); border-width: 1px 0px 0px 1px; padding: 0px; background-color: rgba(204, 204, 204, 1); color: rgba(0, 0, 0, 1); text-align: left; vertical-align: middle; min-width: 0px; min-height: 0px; height: 30px; overflow: hidden; -moz-box-sizing: border-box; box-sizing: border-box; width: 135.14285714285714px"><div style="overflow: hidden; min-width: 0px; -moz-box-sizing: border-box; box-sizing: border-box; padding: 4px; width: 135.14285714285714px"> Report Time </div></th><th style="border: solid 1px rgba(0, 0, 0, 0.33); border-width: 1px 0px 0px 1px; padding: 0px; background-color: rgba(204, 204, 204, 1); color: rgba(0, 0, 0, 1); text-align: left; vertical-align: middle; min-width: 0px; min-height: 0px; height: 30px; overflow: hidden; -moz-box-sizing: border-box; box-sizing: border-box; width: 175.28571428571428px"><div style="overflow: hidden; min-width: 0px; -moz-box-sizing: border-box; box-sizing: border-box; padding: 4px; width: 175.28571428571428px"> Action</div></th></tr></thead><tbody><tr style="height: 20px; min-height: 0px"><td style="border: solid 1px rgba(0, 0, 0, 0.33); border-width: 1px 0px 0px 1px; padding: 0px; text-align: left; vertical-align: middle; min-width: 0px; min-height: 0px; height: undefinedpx; overflow: hidden; -moz-box-sizing: border-box; box-sizing: border-box; width: 39.142857142857146px"><div style="overflow: hidden; min-width: 0px; -moz-box-sizing: border-box; box-sizing: border-box; padding: 4px; width: 39.142857142857146px">1 </div></td><td style="border: solid 1px rgba(0, 0, 0, 0.33); border-width: 1px 0px 0px 1px; padding: 0px; text-align: left; vertical-align: middle; min-width: 0px; min-height: 0px; height: undefinedpx; overflow: hidden; -moz-box-sizing: border-box; box-sizing: border-box; width: 121.42857142857144px"><div style="overflow: hidden; min-width: 0px; -moz-box-sizing: border-box; box-sizing: border-box; padding: 4px; width: 121.42857142857144px"> Cell Content 1 </div></td><td style="border: solid 1px rgba(0, 0, 0, 0.33); border-width: 1px 0px 0px 1px; padding: 0px; text-align: left; vertical-align: middle; min-width: 0px; min-height: 0px; height: undefinedpx; overflow: hidden; -moz-box-sizing: border-box; box-sizing: border-box; width: 135.14285714285714px"><div style="overflow: hidden; min-width: 0px; -moz-box-sizing: border-box; box-sizing: border-box; padding: 4px; width: 135.14285714285714px"> 2025.05.25 10:00 </div></td><td style="border: solid 1px rgba(0, 0, 0, 0.33); border-width: 1px 0px 0px 1px; padding: 0px; text-align: left; vertical-align: middle; min-width: 0px; min-height: 0px; height: undefinedpx; overflow: hidden; -moz-box-sizing: border-box; box-sizing: border-box; width: 175.28571428571428px"><div style="overflow: hidden; min-width: 0px; -moz-box-sizing: border-box; box-sizing: border-box; padding: 4px; width: 175.28571428571428px"> Display</div></td></tr><tr style="height: 20px; min-height: 0px"><td style="border: solid 1px rgba(0, 0, 0, 0.33); border-width: 1px 0px 0px 1px; padding: 0px; text-align: left; vertical-align: middle; min-width: 0px; min-height: 0px; height: undefinedpx; overflow: hidden; -moz-box-sizing: border-box; box-sizing: border-box; width: 39.142857142857146px"><div style="overflow: hidden; min-width: 0px; -moz-box-sizing: border-box; box-sizing: border-box; padding: 4px; width: 39.142857142857146px">2 </div></td><td style="border: solid 1px rgba(0, 0, 0, 0.33); border-width: 1px 0px 0px 1px; padding: 0px; text-align: left; vertical-align: middle; min-width: 0px; min-height: 0px; height: undefinedpx; overflow: hidden; -moz-box-sizing: border-box; box-sizing: border-box; width: 121.42857142857144px"><div style="overflow: hidden; min-width: 0px; -moz-box-sizing: border-box; box-sizing: border-box; padding: 4px; width: 121.42857142857144px"> Cell content 2 </div></td><td style="border: solid 1px rgba(0, 0, 0, 0.33); border-width: 1px 0px 0px 1px; padding: 0px; text-align: left; vertical-align: middle; min-width: 0px; min-height: 0px; height: undefinedpx; overflow: hidden; -moz-box-sizing: border-box; box-sizing: border-box; width: 135.14285714285714px"><div style="overflow: hidden; min-width: 0px; -moz-box-sizing: border-box; box-sizing: border-box; padding: 4px; width: 135.14285714285714px"> 2025.05.25 10:00 </div></td><td style="border: solid 1px rgba(0, 0, 0, 0.33); border-width: 1px 0px 0px 1px; padding: 0px; text-align: left; vertical-align: middle; min-width: 0px; min-height: 0px; height: undefinedpx; overflow: hidden; -moz-box-sizing: border-box; box-sizing: border-box; width: 175.28571428571428px"><div style="overflow: hidden; min-width: 0px; -moz-box-sizing: border-box; box-sizing: border-box; padding: 4px; width: 175.28571428571428px"> Display</div></td></tr><tr style="height: 20px; min-height: 0px"><td style="border: solid 1px rgba(0, 0, 0, 0.33); border-width: 1px 0px 0px 1px; padding: 0px; text-align: left; vertical-align: middle; min-width: 0px; min-height: 0px; height: undefinedpx; overflow: hidden; -moz-box-sizing: border-box; box-sizing: border-box; width: 39.142857142857146px"><div style="overflow: hidden; min-width: 0px; -moz-box-sizing: border-box; box-sizing: border-box; padding: 4px; width: 39.142857142857146px">3 </div></td><td style="border: solid 1px rgba(0, 0, 0, 0.33); border-width: 1px 0px 0px 1px; padding: 0px; text-align: left; vertical-align: middle; min-width: 0px; min-height: 0px; height: undefinedpx; overflow: hidden; -moz-box-sizing: border-box; box-sizing: border-box; width: 121.42857142857144px"><div style="overflow: hidden; min-width: 0px; -moz-box-sizing: border-box; box-sizing: border-box; padding: 4px; width: 121.42857142857144px"> Cell Content 1 </div></td><td style="border: solid 1px rgba(0, 0, 0, 0.33); border-width: 1px 0px 0px 1px; padding: 0px; text-align: left; vertical-align: middle; min-width: 0px; min-height: 0px; height: undefinedpx; overflow: hidden; -moz-box-sizing: border-box; box-sizing: border-box; width: 135.14285714285714px"><div style="overflow: hidden; min-width: 0px; -moz-box-sizing: border-box; box-sizing: border-box; padding: 4px; width: 135.14285714285714px"> 2025.05.25 10:00 </div></td><td style="border: solid 1px rgba(0, 0, 0, 0.33); border-width: 1px 0px 0px 1px; padding: 0px; text-align: left; vertical-align: middle; min-width: 0px; min-height: 0px; height: undefinedpx; overflow: hidden; -moz-box-sizing: border-box; box-sizing: border-box; width: 175.28571428571428px"><div style="overflow: hidden; min-width: 0px; -moz-box-sizing: border-box; box-sizing: border-box; padding: 4px; width: 175.28571428571428px"> Display</div></td></tr><tr style="height: 20px; min-height: 0px"><td style="border: solid 1px rgba(0, 0, 0, 0.33); border-width: 1px 0px 0px 1px; padding: 0px; text-align: left; vertical-align: middle; min-width: 0px; min-height: 0px; height: undefinedpx; overflow: hidden; -moz-box-sizing: border-box; box-sizing: border-box; width: 39.142857142857146px"><div style="overflow: hidden; min-width: 0px; -moz-box-sizing: border-box; box-sizing: border-box; padding: 4px; width: 39.142857142857146px">4 </div></td><td style="border: solid 1px rgba(0, 0, 0, 0.33); border-width: 1px 0px 0px 1px; padding: 0px; text-align: left; vertical-align: middle; min-width: 0px; min-height: 0px; height: undefinedpx; overflow: hidden; -moz-box-sizing: border-box; box-sizing: border-box; width: 121.42857142857144px"><div style="overflow: hidden; min-width: 0px; -moz-box-sizing: border-box; box-sizing: border-box; padding: 4px; width: 121.42857142857144px"> Cell content 2 </div></td><td style="border: solid 1px rgba(0, 0, 0, 0.33); border-width: 1px 0px 0px 1px; padding: 0px; text-align: left; vertical-align: middle; min-width: 0px; min-height: 0px; height: undefinedpx; overflow: hidden; -moz-box-sizing: border-box; box-sizing: border-box; width: 135.14285714285714px"><div style="overflow: hidden; min-width: 0px; -moz-box-sizing: border-box; box-sizing: border-box; padding: 4px; width: 135.14285714285714px"> 2025.05.25 10:00 </div></td><td style="border: solid 1px rgba(0, 0, 0, 0.33); border-width: 1px 0px 0px 1px; padding: 0px; text-align: left; vertical-align: middle; min-width: 0px; min-height: 0px; height: undefinedpx; overflow: hidden; -moz-box-sizing: border-box; box-sizing: border-box; width: 175.28571428571428px"><div style="overflow: hidden; min-width: 0px; -moz-box-sizing: border-box; box-sizing: border-box; padding: 4px; width: 175.28571428571428px"> Display</div></td></tr><tr style="height: 20px; min-height: 0px"><td style="border: solid 1px rgba(0, 0, 0, 0.33); border-width: 1px 0px 0px 1px; padding: 0px; text-align: left; vertical-align: middle; min-width: 0px; min-height: 0px; height: undefinedpx; overflow: hidden; -moz-box-sizing: border-box; box-sizing: border-box; width: 39.142857142857146px"><div style="overflow: hidden; min-width: 0px; -moz-box-sizing: border-box; box-sizing: border-box; padding: 4px; width: 39.142857142857146px">5 </div></td><td style="border: solid 1px rgba(0, 0, 0, 0.33); border-width: 1px 0px 0px 1px; padding: 0px; text-align: left; vertical-align: middle; min-width: 0px; min-height: 0px; height: undefinedpx; overflow: hidden; -moz-box-sizing: border-box; box-sizing: border-box; width: 121.42857142857144px"><div style="overflow: hidden; min-width: 0px; -moz-box-sizing: border-box; box-sizing: border-box; padding: 4px; width: 121.42857142857144px"> Cell Content 1 </div></td><td style="border: solid 1px rgba(0, 0, 0, 0.33); border-width: 1px 0px 0px 1px; padding: 0px; text-align: left; vertical-align: middle; min-width: 0px; min-height: 0px; height: undefinedpx; overflow: hidden; -moz-box-sizing: border-box; box-sizing: border-box; width: 135.14285714285714px"><div style="overflow: hidden; min-width: 0px; -moz-box-sizing: border-box; box-sizing: border-box; padding: 4px; width: 135.14285714285714px"> 2025.05.25 10:00 </div></td><td style="border: solid 1px rgba(0, 0, 0, 0.33); border-width: 1px 0px 0px 1px; padding: 0px; text-align: left; vertical-align: middle; min-width: 0px; min-height: 0px; height: undefinedpx; overflow: hidden; -moz-box-sizing: border-box; box-sizing: border-box; width: 175.28571428571428px"><div style="overflow: hidden; min-width: 0px; -moz-box-sizing: border-box; box-sizing: border-box; padding: 4px; width: 175.28571428571428px"> Display</div></td></tr><tr style="height: 20px; min-height: 0px"><td style="border: solid 1px rgba(0, 0, 0, 0.33); border-width: 1px 0px 0px 1px; padding: 0px; text-align: left; vertical-align: middle; min-width: 0px; min-height: 0px; height: undefinedpx; overflow: hidden; -moz-box-sizing: border-box; box-sizing: border-box; width: 39.142857142857146px"><div style="overflow: hidden; min-width: 0px; -moz-box-sizing: border-box; box-sizing: border-box; padding: 4px; width: 39.142857142857146px">6 </div></td><td style="border: solid 1px rgba(0, 0, 0, 0.33); border-width: 1px 0px 0px 1px; padding: 0px; text-align: left; vertical-align: middle; min-width: 0px; min-height: 0px; height: undefinedpx; overflow: hidden; -moz-box-sizing: border-box; box-sizing: border-box; width: 121.42857142857144px"><div style="overflow: hidden; min-width: 0px; -moz-box-sizing: border-box; box-sizing: border-box; padding: 4px; width: 121.42857142857144px"> Cell content 2 </div></td><td style="border: solid 1px rgba(0, 0, 0, 0.33); border-width: 1px 0px 0px 1px; padding: 0px; text-align: left; vertical-align: middle; min-width: 0px; min-height: 0px; height: undefinedpx; overflow: hidden; -moz-box-sizing: border-box; box-sizing: border-box; width: 135.14285714285714px"><div style="overflow: hidden; min-width: 0px; -moz-box-sizing: border-box; box-sizing: border-box; padding: 4px; width: 135.14285714285714px"> 2025.05.25 10:00 </div></td><td style="border: solid 1px rgba(0, 0, 0, 0.33); border-width: 1px 0px 0px 1px; padding: 0px; text-align: left; vertical-align: middle; min-width: 0px; min-height: 0px; height: undefinedpx; overflow: hidden; -moz-box-sizing: border-box; box-sizing: border-box; width: 175.28571428571428px"><div style="overflow: hidden; min-width: 0px; -moz-box-sizing: border-box; box-sizing: border-box; padding: 4px; width: 175.28571428571428px"> Display</div></td></tr></tbody></table></div>
		<!-- <div xmlns="http://www.w3.org/1999/xhtml" style="position: absolute; top: 0px; left: 0px; width: 100%; height: 100%;"/> -->
            </foreignObject>
        </g><g p:type="Shape" p:def="Evolus.WindowsXP.Widgets:Button" id="75ddf9bf9c174370be2404548abe7c4c" transform="matrix(1,0,0,1,695.0000103414059,137.00000204145908)"><p:metadata><p:property name="box">120,30</p:property><p:property name="buttonText">Report Directory</p:property><p:property name="textColor">#000000FF</p:property><p:property name="textFont">Tahoma|normal|normal|11px|none|0</p:property><p:property name="textAlign">1,1</p:property><p:property name="disabled">false</p:property><p:property name="defaultButton">false</p:property></p:metadata>
            <defs p:name="defs2177" id="68c153bc67434284be5b901d4be2df19">
                <linearGradient x1="0%" y1="0%" x2="0%" y2="100%" p:name="linearGradient4109" id="e77108a0c53a4decb04c32ee4c4b4129">
                  <stop style="stop-color:#ffffff;stop-opacity:1" offset="0%" p:name="stop1" id="a8176ce7abc74a6c982bbcab8f010c53"/>
                  <stop style="stop-color:#f0f0ea;stop-opacity:1" offset="72%" p:name="stop2" id="4ca771e2e00d4e918fc78876ececbbbe"/>
                  <stop style="stop-color:#d6d0c5;stop-opacity:1" offset="100%" p:name="stop3" id="fb9a23546b20403690c325977a4d1151"/>
                </linearGradient>
            </defs>
            <rect width="119" height="29" rx="3" ry="3" x="0.5" y="0.5" style="opacity:1;color:#000000;fill:url(#e77108a0c53a4decb04c32ee4c4b4129);fill-opacity:1;fill-rule:nonzero;stroke:#003c74;stroke-width:1;stroke-linecap:square;stroke-linejoin:miter;marker:none;marker-start:none;marker-mid:none;marker-end:none;stroke-miterlimit:4;stroke-dasharray:none;stroke-dashoffset:0;stroke-opacity:1;visibility:visible;display:inline;overflow:visible;enable-background:accumulate" p:name="rect" id="0b199449a2f34578abb37b9bc47a131e"/>
            <rect width="116" height="26" rx="2" ry="2" x="2" y="2" style="opacity: 1; color: rgb(0, 0, 0); fill: none; fill-opacity: 1; fill-rule: nonzero; stroke: rgb(125, 164, 228); stroke-width: 2; stroke-linecap: square; stroke-linejoin: miter; marker: none; stroke-miterlimit: 4; stroke-dasharray: none; stroke-dashoffset: 0; stroke-opacity: 1; visibility: hidden; display: none; overflow: visible;" p:name="defaultMarker" id="8051fb5812b34f27a7c9969ea56f89d1"/>
            <text x="33.415905" y="14.214675" style="text-align: left; text-anchor: start; dominant-baseline: central; fill: rgb(0, 0, 0); fill-opacity: 1; font-family: Tahoma; font-size: 11px; font-weight: normal; font-style: normal; text-decoration: none;" xml:space="preserve" p:name="text" id="6978abcecadf4d808708bc7232236cf3" transform="translate(-14,1)">Report Directory</text>
            <path style="fill: none; stroke-width: 1px; visibility: hidden;" p:name="accelIndicator" id="d5d140fd94fa414ba2633daec749a3b2"/>
        </g><g p:type="Shape" p:def="Evolus.Sketchy.GUI:lineV2" p:sc="Horizontal Line" id="b2709ee4116b4cf184eeac12b76169fd" transform="matrix(1,0,0,1,325,55)"><p:metadata><p:property name="startLine">0,0</p:property><p:property name="endLine">76,0</p:property><p:property name="mode">horizontal</p:property><p:property name="strokeColor">#FF0000FF</p:property><p:property name="strokeStyle">2|</p:property></p:metadata>
            <g p:name="rect" id="0bf609648d794feea865c5ab7d17b447" transform="translate(0,0)" style="stroke: rgb(255, 0, 0); stroke-opacity: 1; stroke-width: 2;">
    			<path style="stroke: #FFFFFF; stroke-opacity: 0; stroke-width: 8px; fill: none;" p:name="bg" id="4556fb30762941f5b7ebf372005238cb" transform="translate(0,0)" d="M 0 0 C 25 1 51 1 76 0"/>
    			<path style="stroke-linejoin: round; fill: none;" p:name="line1" id="198f930fe163491e9d77fde4b1886618" transform="translate(0,0)" d="M 0 0 C 25 0 51 0 76 0"/>
    		</g>
        </g><g p:type="Shape" p:def="Evolus.Sketchy.GUI:lineV2" id="d53456ce84dc450b8a42e2fab953b648" transform="matrix(1,0,0,1,477.0000071078539,202.00000299513337)"><p:metadata><p:property name="startLine">0,0</p:property><p:property name="endLine">50,0</p:property><p:property name="mode">free</p:property><p:property name="strokeColor">#9999FFFF</p:property><p:property name="strokeStyle">2|</p:property></p:metadata>
            <g p:name="rect" id="b829fdccb2f740d48348cc22a6d73e83" transform="translate(0,0)" style="stroke: rgb(153, 153, 255); stroke-opacity: 1; stroke-width: 2;">
    			<path style="stroke: #FFFFFF; stroke-opacity: 0; stroke-width: 8px; fill: none;" p:name="bg" id="3502445131074bf785d39b5fe5450429" transform="translate(0,0)" d="M 0 0 C 17 0 33 0 50 0"/>
    			<path style="stroke-linejoin: round; fill: none;" p:name="line1" id="8f43cb10a79c4afc9d57eb0d5f1e678a" transform="translate(0,0)" d="M 0 0 C 17 0 33 0 50 0"/>
    		</g>
        </g><g p:type="Shape" p:def="Evolus.Sketchy.GUI:lineV2" id="7cf1ba5dc63c402eb51de6100348f8f5" transform="matrix(1,0,0,1,477.0000071078539,245.0000036358833)"><p:metadata><p:property name="startLine">0,0</p:property><p:property name="endLine">50,0</p:property><p:property name="mode">free</p:property><p:property name="strokeColor">#9999FFFF</p:property><p:property name="strokeStyle">2|</p:property></p:metadata>
            <g p:name="rect" id="c4cc6a043ab24df69811fa8d1b607241" transform="translate(0,0)" style="stroke: rgb(153, 153, 255); stroke-opacity: 1; stroke-width: 2;">
    			<path style="stroke: #FFFFFF; stroke-opacity: 0; stroke-width: 8px; fill: none;" p:name="bg" id="e6d905f5f57b42fba144e50c15e7affb" transform="translate(0,0)" d="M 0 0 C 17 0 33 0 50 0"/>
    			<path style="stroke-linejoin: round; fill: none;" p:name="line1" id="e632694c5ec143b1a4cd1277a03a4ab5" transform="translate(0,0)" d="M 0 0 C 17 -1 33 -1 50 0"/>
    		</g>
        </g><g p:type="Shape" p:def="Evolus.Sketchy.GUI:lineV2" id="e4bf507d9da7487a9362dc34058deca4" transform="matrix(1,0,0,1,477.0000071078539,288.00000427663326)"><p:metadata><p:property name="startLine">0,0</p:property><p:property name="endLine">50,0</p:property><p:property name="mode">free</p:property><p:property name="strokeColor">#9999FFFF</p:property><p:property name="strokeStyle">2|</p:property></p:metadata>
            <g p:name="rect" id="13c276d5b96f43a0a5c91b5df4063b2a" transform="translate(0,0)" style="stroke: rgb(153, 153, 255); stroke-opacity: 1; stroke-width: 2;">
    			<path style="stroke: #FFFFFF; stroke-opacity: 0; stroke-width: 8px; fill: none;" p:name="bg" id="e575d32732a94cd7af3660ab4f80d719" transform="translate(0,0)" d="M 0 0 C 17 1 33 1 50 0"/>
    			<path style="stroke-linejoin: round; fill: none;" p:name="line1" id="da4bcc257cd84d1b8a04b7769b8670ec" transform="translate(0,0)" d="M 0 0 C 17 1 33 1 50 0"/>
    		</g>
        </g><g p:type="Shape" p:def="Evolus.Sketchy.GUI:lineV2" id="3f99cdbe5f984b2b8602f07f30d429f2" transform="matrix(1,0,0,1,477.0000071078539,333.0000049620867)"><p:metadata><p:property name="startLine">0,0</p:property><p:property name="endLine">50,0</p:property><p:property name="mode">free</p:property><p:property name="strokeColor">#9999FFFF</p:property><p:property name="strokeStyle">2|</p:property></p:metadata>
            <g p:name="rect" id="b936a047ec74446ca54f4b6774dd155c" transform="translate(0,0)" style="stroke: rgb(153, 153, 255); stroke-opacity: 1; stroke-width: 2;">
    			<path style="stroke: #FFFFFF; stroke-opacity: 0; stroke-width: 8px; fill: none;" p:name="bg" id="77ff35dd6c274e75b2b09b66eada3f11" transform="translate(0,0)" d="M 0 0 C 17 0 33 0 50 0"/>
    			<path style="stroke-linejoin: round; fill: none;" p:name="line1" id="58704996543b4a5bbd148b3466df9ca2" transform="translate(0,0)" d="M 0 0 C 17 0 33 0 50 0"/>
    		</g>
        </g><g p:type="Shape" p:def="Evolus.Sketchy.GUI:lineV2" id="d7bf4a42a79148e79ed94211722ab780" transform="matrix(1,0,0,1,477.00000719726086,378.00000563263893)"><p:metadata><p:property name="startLine">0,0</p:property><p:property name="endLine">50,0</p:property><p:property name="mode">free</p:property><p:property name="strokeColor">#9999FFFF</p:property><p:property name="strokeStyle">2|</p:property></p:metadata>
            <g p:name="rect" id="925216fd28384715955dfd6caa3d17f8" transform="translate(0,0)" style="stroke: rgb(153, 153, 255); stroke-opacity: 1; stroke-width: 2;">
    			<path style="stroke: #FFFFFF; stroke-opacity: 0; stroke-width: 8px; fill: none;" p:name="bg" id="5e0426ee3f82443db738acd4c53c02ae" transform="translate(0,0)" d="M 0 0 C 17 -1 33 -1 50 0"/>
    			<path style="stroke-linejoin: round; fill: none;" p:name="line1" id="296f6402525c426ab23fab8817b5f349" transform="translate(0,0)" d="M 0 0 C 17 0 33 0 50 0"/>
    		</g>
        </g><g p:type="Shape" p:def="Evolus.Sketchy.GUI:lineV2" id="6cdf55a13c3a4f56a8e865164990ed86" transform="matrix(1,0,0,1,477.0000071078539,420.00000627338886)"><p:metadata><p:property name="startLine">0,0</p:property><p:property name="endLine">50,0</p:property><p:property name="mode">free</p:property><p:property name="strokeColor">#9999FFFF</p:property><p:property name="strokeStyle">2|</p:property></p:metadata>
            <g p:name="rect" id="9f3c90b56965443a91df5f0221ba715c" transform="translate(0,0)" style="stroke: rgb(153, 153, 255); stroke-opacity: 1; stroke-width: 2;">
    			<path style="stroke: #FFFFFF; stroke-opacity: 0; stroke-width: 8px; fill: none;" p:name="bg" id="055a83d4df714b6d877c00ce41c3868d" transform="translate(0,0)" d="M 0 0 C 17 0 33 0 50 0"/>
    			<path style="stroke-linejoin: round; fill: none;" p:name="line1" id="ecfd411b09df4b54846957c35a148405" transform="translate(0,0)" d="M 0 0 C 17 0 33 0 50 0"/>
    		</g>
        </g></g></g></svg>