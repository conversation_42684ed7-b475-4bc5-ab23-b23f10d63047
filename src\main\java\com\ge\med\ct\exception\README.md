# 异常处理模块

## 概述

异常处理模块提供了一套统一的异常处理机制，包括异常类、错误码、AOP 切面、消息处理和工具类。

## 目录结构

```
com.ge.med.ct.exception/
├── aspect/                 # AOP切面和注解
│   ├── HandleException.java        # 通用异常处理注解
│   └── ExceptionHandlingAspect.java # 异常处理切面
├── code/                   # 错误码定义
│   ├── ErrorCode.java              # 错误码接口
│   ├── SystemErrorCode.java        # 系统错误码
│   ├── DicomErrorCode.java         # DICOM错误码
│   ├── BusinessErrorCode.java      # 业务错误码
│   ├── UIErrorCode.java            # UI错误码
│   └── ErrorCodes.java             # 错误码工具类
├── core/                   # 核心异常类
│   ├── QAToolException.java        # 基础异常类
│   ├── DicomException.java         # DICOM异常类
│   ├── BusinessException.java      # 业务异常类
│   └── UIException.java            # UI异常类
├── event/                  # 异常事件
│   └── ExceptionEvent.java         # 异常事件类
├── factory/                # 异常工厂
│   └── ExceptionFactory.java       # 异常工厂类
├── message/                # 消息处理
│   ├── Message.java                # 消息接口
│   ├── AbstractMessage.java        # 消息抽象基类
│   ├── MessageBuilder.java         # 消息构建器
│   ├── CommonMessages.java          # 通用消息枚举
│   ├── DicomMessages.java          # DICOM消息枚举
│   ├── BusinessMessages.java       # 业务消息枚举
│   └── UIMessages.java             # UI消息枚举
└── util/                   # 工具类
    └── LambdaExceptionUtil.java    # Lambda异常处理工具类
```

## 使用方法

### 1. 使用异常处理注解

#### 1.1 方法级别注解

```java
import com.ge.med.ct.exception.aspect.HandleException;
import com.ge.med.ct.exception.code.ErrorCode;

@HandleException(errorCode = ErrorCode.DICOM_VALIDATION)
public void processImage(DicomImage image) {
    // 处理逻辑
}
```

#### 1.2 类级别注解

```java
import com.ge.med.ct.exception.aspect.HandleException;
import com.ge.med.ct.exception.code.ErrorCode;

// 类级别注解会应用于类中的所有方法
@HandleException(errorCode = ErrorCode.PROCESSING)
public class DicomProcessor {

    // 使用类级别的默认错误码
    public void processFile(String filePath) {
        // 处理逻辑
    }

    // 覆盖类级别的错误码，使用特定的错误码
    @HandleException(errorCode = ErrorCode.DICOM_VALIDATION)
    public void validateFile(String filePath) {
        // 验证逻辑
    }
}
```

#### 1.3 注解继承规则

- 类级别注解会应用于类中的所有方法
- 方法级别注解会覆盖类级别注解
- 如果既没有方法级别注解也没有类级别注解，则不会应用异常处理

### 2. 抛出异常

#### 2.1 使用构造方法

```java
import com.ge.med.ct.exception.core.DicomException;
import com.ge.med.ct.exception.code.DicomErrorCode;
import com.ge.med.ct.exception.message.DicomMessages;

// 简单方式
throw new DicomException("文件格式无效");

// 带错误码
throw new DicomException(DicomErrorCode.INVALID_FILE, "文件格式无效");

// 带消息
throw new DicomException(DicomMessages.FILE_INVALID, filePath);

// 带错误码和消息
throw new DicomException(DicomErrorCode.INVALID_FILE, DicomMessages.FILE_INVALID, filePath);
```

#### 2.2 使用构建器模式

```java
import com.ge.med.ct.exception.core.DicomException;
import com.ge.med.ct.exception.code.DicomErrorCode;
import com.ge.med.ct.exception.message.DicomMessages;

// 使用构建器
throw DicomException.builder()
    .errorCode(DicomErrorCode.INVALID_FILE)
    .message(DicomMessages.FILE_INVALID)
    .messageArgs(filePath)
    .context("fileName", file.getName())
    .context("fileSize", file.length())
    .build();

// 创建DICOM数据异常
throw DicomException.dataBuilder()
    .messageArgs(tagName)
    .context("tagValue", tagValue)
    .build();

// 或者使用静态工厂方法
throw DicomException.createDataException("Invalid tag value");
```

#### 2.3 使用工厂方法

```java
import com.ge.med.ct.exception.factory.ExceptionFactory;
import com.ge.med.ct.exception.code.DicomErrorCode;
import com.ge.med.ct.exception.message.DicomMessages;

// 使用工厂方法
throw ExceptionFactory.createDicomException(DicomErrorCode.INVALID_FILE, DicomMessages.FILE_INVALID, filePath);

// 创建DICOM数据异常
throw ExceptionFactory.createDicomDataException("Invalid tag value");

// 或者指定错误码
throw ExceptionFactory.createDicomDataException(DicomErrorCode.TAG_ERROR, "Invalid tag format");
```

### 3. 处理 Lambda 表达式中的受检异常

```java
import static com.ge.med.ct.exception.util.LambdaExceptionUtil.*;

// 处理Consumer中的受检异常
files.forEach(uncheckedConsumer(file -> {
    // 可能抛出IOException的代码
    Files.readAllBytes(file.toPath());
}));

// 处理Function中的受检异常
List<byte[]> contents = files.stream()
    .map(uncheckedFunction(file -> Files.readAllBytes(file.toPath())))
    .collect(Collectors.toList());
```

### 4. 自定义消息

```java
import com.ge.med.ct.exception.message.Message;
import com.ge.med.ct.exception.message.MessageBuilder;

// 实现Message接口
public enum MyMessage implements Message {
    FILE_NOT_FOUND("file.notFound", "文件未找到: {0}"),
    INVALID_FORMAT("file.invalidFormat", "无效的文件格式: {0}");

    private final String key;
    private final String defaultMessage;

    MyMessage(String key, String defaultMessage) {
        this.key = key;
        this.defaultMessage = defaultMessage;
    }

    @Override
    public String getKey() {
        return key;
    }

    @Override
    public String getDefaultMessage() {
        return defaultMessage;
    }

    @Override
    public String format(Object... args) {
        return MessageBuilder.of(this, args).build();
    }
}

// 使用自定义消息
throw new DicomException(DicomErrorCode.INVALID_FILE, MyMessage.INVALID_FORMAT, filePath);

// 或者使用预定义的消息
throw new DicomException(DicomErrorCode.INVALID_FILE, DicomMessages.FILE_INVALID, filePath);
throw new BusinessException(BusinessErrorCode.DATA_NOT_FOUND, BusinessMessages.DATA_NOT_FOUND, entityId);
throw new UIException(UIErrorCode.COMPONENT_ERROR, UIMessages.COMPONENT_ERROR, componentName);
```

## 错误码规范

错误码格式：`[模块]-[数字编码]`

- 系统错误：`SYS-1xxx`
- DICOM 错误：`DICOM-2xxx`
- 业务错误：`BIZ-3xxx`
- UI 错误：`UI-4xxx`

## 异常层次结构

```
RuntimeException
└── QAToolException
    ├── DicomException
    ├── BusinessException
    └── UIException
```

## 最佳实践

### 1. 异常创建与抛出

1. **使用构建器模式**：对于复杂的异常，使用构建器模式创建异常对象
2. **添加上下文信息**：使用`addContext`方法添加上下文信息，帮助诊断问题
3. **使用枚举错误码**：使用枚举错误码，而不是字符串常量
4. **使用消息接口**：使用`Message`接口定义消息，支持国际化
5. **使用工厂方法**：对于常见的异常，使用工厂方法创建异常对象

### 2. 异常处理注解使用

1. **类级别注解的适用场景**：

   - 类中大多数方法需要相同的异常处理策略
   - 希望简化代码，避免在每个方法上重复添加相同的注解
   - 处理的是通用异常，如 IO 异常、网络异常等

2. **方法级别注解的适用场景**：

   - 方法需要与类不同的异常处理策略
   - 方法需要特定的错误码
   - 方法需要特定的重试策略
   - 方法处理的是特定类型的异常

3. **明确性优先**：
   - 为了代码的可读性和维护性，建议在重要或复杂的方法上显式添加注解
   - 即使与类级别相同，这样可以使意图更加明确

### 3. 异常恢复与降级

1. **使用重试机制**：对于可能是暂时性的错误，如网络超时，实现自动重试
2. **实现降级服务**：当某些功能不可用时，提供替代方案
3. **使用断路器模式**：防止系统资源耗尽在失败的操作上
