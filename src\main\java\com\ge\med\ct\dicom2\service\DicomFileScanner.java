package com.ge.med.ct.dicom2.service;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.logging.Logger;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import com.ge.med.ct.cfg.ConfigManager;
import com.ge.med.ct.dicom2.reader.DicomMetadataReader;
import com.ge.med.ct.dicom2.utils.DicomValidationHandler;
import com.ge.med.ct.dicom2.utils.PreScanResult;
import com.ge.med.ct.exception.aspect.HandleException;
import com.ge.med.ct.exception.code.ErrorCode;
import com.ge.med.ct.exception.core.DicomException;
import com.ge.med.ct.exception.message.DicomMessages;
import com.ge.med.ct.service.LogManager;

/**
 * DICOM文件扫描器 - 专门负责文件扫描和预处理
 */
@HandleException(errorCode = ErrorCode.PROCESSING)
public class DicomFileScanner {
    private static final Logger LOG = LogManager.getInstance().getLogger(DicomFileScanner.class);
    private static final int MAX_SCAN_DEPTH = 10;

    private final DicomMetadataReader metadataReader;
    private final boolean useParallelScan;
    private final boolean detailedWarnings;
    private final boolean groupWarnings;
    private final DicomValidationHandler validationHandler;

    public DicomFileScanner() {
        ConfigManager configManager = ConfigManager.getInstance();
        this.metadataReader = new DicomMetadataReader();
        this.useParallelScan = configManager.getBoolean("dicom.scan.parallel", true);
        this.detailedWarnings = configManager.getBoolean("dicom.validation.detailed_warnings", true);
        this.groupWarnings = configManager.getBoolean("dicom.validation.group_warnings", true);

        DicomFileValidator validationService = DicomFileValidator.getInstance();
        boolean skipInvalidFiles = configManager.getBoolean("dicom.validation.skip_invalid", true);
        this.validationHandler = new DicomValidationHandler(configManager, validationService, skipInvalidFiles);
    }

    /**
     * 扫描目录，返回DICOM文件路径列表
     *
     * @param directory 目录路径
     * @param recursive 是否递归扫描
     * @return DICOM文件路径列表
     * @throws DicomException 如果扫描失败
     */
    public List<String> scanDirectory(String directory, boolean recursive) throws DicomException {
        LOG.info("开始扫描目录: " + directory + (recursive ? " 递归" : ""));

        try {
            List<String> files = findDicomFiles(directory, recursive, MAX_SCAN_DEPTH);
            LOG.info("扫描完成: 发现 " + files.size() + " 个文件");
            return files;
        } catch (IOException e) {
            throw new DicomException(ErrorCode.PROCESSING, DicomMessages.SCAN_FAILED, e, e.getMessage());
        }
    }

    /**
     * 查找DICOM文件
     *
     * @param directory 目录路径
     * @param recursive 是否递归扫描
     * @param maxDepth  最大扫描深度
     * @return DICOM文件路径列表
     * @throws IOException 如果扫描失败
     */
    private List<String> findDicomFiles(String directory, boolean recursive, int maxDepth) throws IOException {
        if (directory == null || directory.isEmpty()) {
            return new ArrayList<>();
        }

        Path dirPath = Paths.get(directory);
        if (!Files.exists(dirPath) || !Files.isDirectory(dirPath)) {
            LOG.warning("目录不存在或不是目录: " + directory);
            return new ArrayList<>();
        }

        int depth = recursive ? maxDepth : 1;

        try (Stream<Path> pathStream = Files.walk(dirPath, depth)) {
            return pathStream
                    .filter(Files::isRegularFile)
                    .filter(path -> DicomFileValidator.isDicomFile(path))
                    .map(Path::toString)
                    .collect(Collectors.toList());
        } catch (IOException e) {
            LOG.warning("扫描目录时出错: " + e.getMessage());
            throw e;
        }
    }

    /**
     * 预扫描目录，按Exam和Series分组
     *
     * @param directory 目录路径
     * @param recursive 是否递归扫描
     * @return 预扫描结果
     * @throws DicomException 如果扫描失败
     */
    public PreScanResult preScanDirectory(String directory, boolean recursive) throws DicomException {
        // 验证目录
        if (directory == null || directory.isEmpty()) {
            throw new DicomException(ErrorCode.PROCESSING, DicomMessages.SCAN_FAILED, "目录路径不能为空");
        }

        // 扫描目录，获取所有DICOM文件
        List<String> files = scanDirectory(directory, recursive);
        if (files.isEmpty()) {
            LOG.warning("目录中没有找到DICOM文件: " + directory);
            return new PreScanResult(
                    new HashMap<>(),
                    new HashMap<>(),
                    new ArrayList<>(),
                    new ArrayList<>());
        }

        return performPreScan(files);
    }

    /**
     * 执行预扫描处理
     */
    private PreScanResult performPreScan(List<String> files) {
        // 预扫描结果
        Map<String, List<String>> examToFilesMap = new ConcurrentHashMap<>();
        Map<String, Map<String, List<String>>> examToSeriesMap = new ConcurrentHashMap<>();
        List<String> invalidFiles = Collections.synchronizedList(new ArrayList<>());
        List<String> emptyExamFiles = Collections.synchronizedList(new ArrayList<>());

        LOG.info("开始预扫描 " + files.size() + " 个DICOM文件...");

        // 根据配置决定是否使用并行流
        Stream<String> fileStream = useParallelScan ? files.parallelStream() : files.stream();

        // 使用流进行预扫描
        fileStream.forEach(filePath -> processFileForPreScan(filePath, examToFilesMap, examToSeriesMap, invalidFiles,
                emptyExamFiles));

        // 记录详细警告信息
        if (detailedWarnings) {
            validationHandler.logEmptyExamFiles(emptyExamFiles);
            validationHandler.logInvalidFiles(invalidFiles);
        }

        // 生成验证错误汇总报告
        if (groupWarnings) {
            validationHandler.printSummaryReport();
        }

        // 记录预扫描结果
        logPreScanResults(examToFilesMap, examToSeriesMap, emptyExamFiles, invalidFiles);

        return new PreScanResult(examToFilesMap, examToSeriesMap, emptyExamFiles, invalidFiles);
    }

    /**
     * 处理单个文件的预扫描
     */
    private void processFileForPreScan(String filePath,
            Map<String, List<String>> examToFilesMap,
            Map<String, Map<String, List<String>>> examToSeriesMap,
            List<String> invalidFiles,
            List<String> emptyExamFiles) {
        try {
            // 读取关键标签
            Map<String, String> keyTags = metadataReader.readKeyTags(filePath);

            // 提取检查和序列ID
            String examId = keyTags.getOrDefault("StudyInstanceUID", "");
            String seriesId = keyTags.getOrDefault("SeriesInstanceUID", "");

            // 处理缺少检查信息的文件
            if (examId.isEmpty()) {
                emptyExamFiles.add(filePath);
                return;
            }

            // 按Exam分组
            examToFilesMap.computeIfAbsent(examId, k -> Collections.synchronizedList(new ArrayList<>()))
                    .add(filePath);

            // 按Series分组
            examToSeriesMap.computeIfAbsent(examId, k -> new ConcurrentHashMap<>())
                    .computeIfAbsent(seriesId, k -> Collections.synchronizedList(new ArrayList<>()))
                    .add(filePath);
        } catch (Exception e) {
            // 获取详细的错误信息
            String errorMessage = getDetailedErrorMessage(e);

            LOG.warning("预扫描文件失败: " + filePath + ", 原因: " + errorMessage);
            invalidFiles.add(filePath);
            validationHandler.addError(filePath, "PRESCAN_ERROR", errorMessage);
        }
    }

    /**
     * 获取详细的错误信息
     */
    private String getDetailedErrorMessage(Exception e) {
        String errorMessage = e.getMessage();
        if (errorMessage == null) {
            if (e.getCause() != null && e.getCause().getMessage() != null) {
                errorMessage = e.getCause().getMessage();
            } else {
                errorMessage = "未知错误: " + e.getClass().getSimpleName();
            }
        }
        return errorMessage;
    }

    /**
     * 记录预扫描结果
     */
    private void logPreScanResults(Map<String, List<String>> examToFilesMap,
            Map<String, Map<String, List<String>>> examToSeriesMap,
            List<String> emptyExamFiles,
            List<String> invalidFiles) {
        int examCount = examToFilesMap.size();
        int seriesCount = countSeries(examToSeriesMap);
        int emptyCount = emptyExamFiles.size();
        int invalidCount = invalidFiles.size();

        LOG.info(String.format("预扫描完成: 发现 %d 个检查, %d 个序列, %d 个缺少检查信息的文件, %d 个无效文件",
                examCount, seriesCount, emptyCount, invalidCount));
    }

    /**
     * 统计序列数量
     */
    private int countSeries(Map<String, Map<String, List<String>>> examToSeriesMap) {
        return examToSeriesMap.values().stream()
                .mapToInt(Map::size)
                .sum();
    }
}