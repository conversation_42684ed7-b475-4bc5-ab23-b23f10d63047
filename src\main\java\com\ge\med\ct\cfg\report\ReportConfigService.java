package com.ge.med.ct.cfg.report;

import com.ge.med.ct.cfg.AppDefaults;
import com.ge.med.ct.cfg.ConfigManager;
import com.ge.med.ct.exception.aspect.HandleException;
import com.ge.med.ct.exception.code.ErrorCode;
import com.ge.med.ct.exception.core.QAToolException;
import com.ge.med.ct.exception.factory.ExceptionFactory;
import com.ge.med.ct.exception.message.ConfigMessages;
import com.ge.med.ct.service.PlatformUtils;

import java.io.File;
import java.util.logging.Logger;

/**
 * 报告配置服务
 * 负责管理报告相关配置
 */
public class ReportConfigService {
    private static final Logger LOG = Logger.getLogger(ReportConfigService.class.getName());

    // 配置键常量
    private static final String KEY_REPORT_DIR_WIN = "report.output.directory.win";
    private static final String KEY_REPORT_DIR_LINUX = "report.output.directory.linux";

    // 依赖的配置管理器
    private final ConfigManager configManager;

    /**
     * 构造函数
     * 
     * @param configManager 配置管理器
     */
    public ReportConfigService(ConfigManager configManager) {
        this.configManager = configManager;
    }

    /**
     * 获取报告输出目录
     * 
     * @return 根据当前操作系统返回合适的报告输出目录
     */
    public String getReportOutputDirectory() {
        return PlatformUtils.getPlatformSpecificValue(
            configManager.getString(KEY_REPORT_DIR_WIN, AppDefaults.DEFAULT_REPORT_DIR),
            configManager.getString(KEY_REPORT_DIR_LINUX, AppDefaults.DEFAULT_REPORT_DIR)
        );
    }
    
    /**
     * 验证配置
     * 
     * @throws QAToolException 如果配置无效
     */
    @HandleException(errorCode = ErrorCode.CONFIG)
    public void validate() throws QAToolException {
        validateOutputDirectory();
    }

    private void validateOutputDirectory() throws QAToolException {
        String outputDir = getReportOutputDirectory();
        if (outputDir == null || outputDir.trim().isEmpty()) {
            throw ExceptionFactory.createConfigValidationException(
                    ErrorCode.CONFIG,
                    ConfigMessages.CONFIG_INVALID.format("报告输出目录"));
        }

        // 检查父目录是否存在，如果不存在则创建
        File outputDirFile = new File(outputDir);
        if (!outputDirFile.exists()) {
            File parentDir = outputDirFile.getParentFile();
            if (parentDir != null && !parentDir.exists()) {
                if (!parentDir.mkdirs()) {
                    LOG.warning("无法创建报告输出目录的父目录: " + parentDir.getAbsolutePath());
                }
            }
            
            // 尝试创建输出目录
            if (!outputDirFile.mkdir()) {
                LOG.warning("无法创建报告输出目录: " + outputDir);
            } else {
                LOG.info("成功创建报告输出目录: " + outputDir);
            }
        } else if (!outputDirFile.isDirectory()) {
            throw ExceptionFactory.createConfigValidationException(
                    ErrorCode.CONFIG,
                    ConfigMessages.CONFIG_INVALID.format("报告输出目录"),
                    outputDir);
        }
    }

    /**
     * 重新加载配置
     */
    public void reload() {
        LOG.info("报告配置已重新加载");
        try {
            validate();
        } catch (QAToolException e) {
            LOG.warning("报告配置重新加载验证失败: " + e.getMessage());
        }
    }
} 