<svg xmlns="http://www.w3.org/2000/svg" xmlns:p="http://www.evolus.vn/Namespace/Pencil" xmlns:pencil="http://www.evolus.vn/Namespace/Pencil" xmlns:html="http://www.w3.org/1999/xhtml" xmlns:xhtml="http://www.w3.org/1999/xhtml" xmlns:office="urn:oasis:names:tc:opendocument:xmlns:office:1.0" xmlns:style="urn:oasis:names:tc:opendocument:xmlns:style:1.0" xmlns:text="urn:oasis:names:tc:opendocument:xmlns:text:1.0" xmlns:table="urn:oasis:names:tc:opendocument:xmlns:table:1.0" xmlns:draw="urn:oasis:names:tc:opendocument:xmlns:drawing:1.0" xmlns:fo="urn:oasis:names:tc:opendocument:xmlns:xsl-fo-compatible:1.0" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:meta="urn:oasis:names:tc:opendocument:xmlns:meta:1.0" xmlns:number="urn:oasis:names:tc:opendocument:xmlns:datastyle:1.0" xmlns:svg="urn:oasis:names:tc:opendocument:xmlns:svg-compatible:1.0" xmlns:chart="urn:oasis:names:tc:opendocument:xmlns:chart:1.0" xmlns:dr3d="urn:oasis:names:tc:opendocument:xmlns:dr3d:1.0" xmlns:math="http://www.w3.org/1998/Math/MathML" xmlns:form="urn:oasis:names:tc:opendocument:xmlns:form:1.0" xmlns:script="urn:oasis:names:tc:opendocument:xmlns:script:1.0" xmlns:ooo="http://openoffice.org/2004/office" xmlns:ooow="http://openoffice.org/2004/writer" xmlns:oooc="http://openoffice.org/2004/calc" xmlns:dom="http://www.w3.org/2001/xml-events" xmlns:xforms="http://www.w3.org/2002/xforms" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:rpt="http://openoffice.org/2005/report" xmlns:of="urn:oasis:names:tc:opendocument:xmlns:of:1.2" xmlns:rdfa="http://docs.oasis-open.org/opendocument/meta/rdfa#" xmlns:field="urn:openoffice:names:experimental:ooo-ms-interop:xmlns:field:1.0" xmlns:regexp="http://exslt.org/regular-expressions" xmlns:em="http://exslt.org/math" xmlns:sodipodi="http://sodipodi.sourceforge.net/DTD/sodipodi-0.dtd" xmlns:inkscape="http://www.inkscape.org/namespaces/inkscape" width="900" height="639" id="exportedSVG" version="1.1" pencil:version="1.2.2" sodipodi:docname="qat"><g inkscape:label="Home" inkscape:groupmode="layer" id="layer_home"><g><rect x="0" y="0" width="900" height="639" fill="none"/><g p:type="Shape" p:def="Evolus.Prototype.GUI:frame" id="16dd3fe80c774e9daf3275e3e1036ae9" transform="matrix(1,0,0,1,0,0)"><p:metadata><p:property name="box">900,639</p:property><p:property name="disabled">false</p:property><p:property name="fillColor">#FAFAFAFF</p:property><p:property name="titleColor">#DEDEDEFF</p:property><p:property name="strokeColor">#666666FF</p:property><p:property name="strokeStyle">1|</p:property><p:property name="textContent">Quality Assurance Tool</p:property><p:property name="textFont">'Arial'|Bold|normal|15px|none|0</p:property><p:property name="textColor">#000000FF</p:property><p:property name="textAlign">0,1</p:property></p:metadata>
            <path style="fill: rgb(250, 250, 250); stroke: rgb(102, 102, 102); stroke-opacity: 1; stroke-width: 1; fill-opacity: 1;" p:name="body" id="156989098fa84db58192be5505270894" d="M 0 32 L 0 0 L 900 0 L 900 639 L 0 639 L 0 32 L 900 32"/>
            <path p:name="menu" id="2385f86ee1f9499d8459d66ade571623" d="M 0.5 0.5 L 899.5 0.5 L 899.5 31 L 0.5 31 z" style="fill: rgb(222, 222, 222); fill-opacity: 1;"/>
            <text p:name="text" id="12c514430ef64aa3bf70c5d4f75f8a3c" transform="translate(10,21)" style="font-family: Arial; font-size: 15px; font-weight: bold; font-style: normal; text-decoration: none; fill: rgb(0, 0, 0); fill-opacity: 1;"><tspan x="0" y="0">Quality Assurance Tool</tspan></text>
        </g><g p:type="Shape" p:def="Evolus.Prototype.GUI:frameControl" id="bd28b958c1c14a369af1fb45120fc838" transform="matrix(1,0,0,1,896.0000133514404,1.0000000298023224)"><p:metadata><p:property name="box">90,30</p:property><p:property name="withMinButton">true</p:property><p:property name="withRestoreButton">true</p:property><p:property name="withCloseButton">true</p:property><p:property name="disabled">false</p:property><p:property name="fillColor">#FAFAFAFF</p:property><p:property name="strokeColor">#666666FF</p:property><p:property name="strokeStyle">2|</p:property></p:metadata>
            <path style="fill: none; stroke: rgb(102, 102, 102); stroke-opacity: 1; stroke-width: 2;" p:name="button" id="64d598d582fb42339385213db6c49927" d="M 66 8 L 82 24 M 82 8 L 66 24 M 39 9 L 53 9 L 53 23 L 39 23 z M 11 23 L 25 23" transform="translate(2.5,-1)"/>
            <path p:name="box" id="97831e430839474f8214fc74c1b9be94" d="M 0 0 L 90 0 L 90 30 L 0 30 z" style="fill: rgb(250, 250, 250); fill-opacity: 0;"/>
        </g><g p:type="Shape" p:def="Evolus.BasicWebElements:pane-v2" id="152868585414434d9fc71d0e428dcb3f" transform="matrix(1,0,0,1,1,32.00000047683716)"><p:metadata><p:property name="radius">10,0</p:property><p:property name="textPadding">0,1.6666666666666667</p:property><p:property name="fixedRadius">false</p:property><p:property name="fillColor">#EEEEEEFF</p:property><p:property name="shadowStyle">0|0|3|1|#000000</p:property><p:property name="shadowColor">#00000000</p:property><p:property name="strokeColor">#CCCCCCFF</p:property><p:property name="strokeStyle">1|</p:property><p:property name="rightBorder">true</p:property><p:property name="bottomBorder">true</p:property><p:property name="textContent"/><p:property name="textFont">PencilUI|normal|normal|13px|none|0</p:property><p:property name="textColor">#000000FF</p:property><p:property name="textAlign">1,1</p:property><p:property name="box">898,50</p:property><p:property name="cornerStyle">none</p:property><p:property name="customStyle"/></p:metadata>
            <foreignObject x="-3" y="-3" width="904" height="56" p:name="htmlObject" id="61f601ed2468463e9dfb23b3a6c2498d" style="padding-top: 3px;">
                <div xmlns="http://www.w3.org/1999/xhtml" style="box-sizing: border-box; overflow: hidden; box-shadow: none; width: 898px; height: 50px; padding: 0px; background-color: rgb(238, 238, 238); x: 10px; border-radius: 0px; border: 1px solid rgb(204, 204, 204); margin-left: 3px;" p:name="div" id="dab7f0da253e4c17bac5b7b5662d2ed3">
                </div>
            </foreignObject>
            <foreignObject x="0" y="0" width="898" height="50" p:name="htmlObject2" id="e00596769cd942d292ff3792898eb272" style="font-family: PencilUI; font-size: 13px; font-weight: normal; font-style: normal; text-decoration: none; fill: rgb(0, 0, 0); fill-opacity: 1; color: rgb(0, 0, 0);">
                <div xmlns="http://www.w3.org/1999/xhtml" style="box-sizing: border-box; overflow: hidden; display: table-cell; width: 898px; height: 50px; padding: 0px; text-align: center; vertical-align: middle;" p:name="div2" id="207fae95520841dea344419726e425e8"><div></div></div>
            </foreignObject>
        </g><g p:type="Shape" p:def="Evolus.WindowsXP.Widgets:TabHeader" id="581a735ea86c4eb2bc93b4d1449ab903" transform="matrix(1,0,0,1,24.00000035762787,50.0000007301569)"><p:metadata><p:property name="box">80,30</p:property><p:property name="disabled">false</p:property><p:property name="tabTitle">Home</p:property><p:property name="textColor">#000000FF</p:property><p:property name="textFont">PencilUI|500|normal|13px|none|0</p:property><p:property name="textAlign">1,1</p:property></p:metadata>
            <g transform="translate(0.5, 2.5)">
                <path style="fill: rgb(245, 244, 234); stroke-width: 1px; fill-opacity: 1; stroke: rgb(145, 167, 180); stroke-opacity: 1;" p:name="rect" id="fdb35231be7647229ef7c6ee482d692e" d="M 0 29.5 L 0 5 C 0 0 0 0 5 0 L 73 0 C 78 0 78 0 78 5 L 78 29.5"/>
                <text x="33.415905" y="14.214675" style="text-align: left; text-anchor: start; dominant-baseline: central; fill: rgb(0, 0, 0); fill-opacity: 1; font-family: PencilUI; font-size: 13px; font-weight: 500; font-style: normal; text-decoration: none;" transform="translate(-11,1)" xml:space="preserve" p:name="text" id="1e89442621e244c6b2d2137c64a09089">Home</text>
            </g>
        </g><g p:type="Shape" p:def="Evolus.WindowsXP.Widgets:TabHeader" id="85b0b85e475b4b23b7bd04202d374da7" transform="matrix(1,0,0,1,117.00000174343586,50.0000007301569)"><p:metadata><p:property name="box">80,30</p:property><p:property name="disabled">false</p:property><p:property name="tabTitle">Report</p:property><p:property name="textColor">#000000FF</p:property><p:property name="textFont">PencilUI|500|normal|13px|none|0</p:property><p:property name="textAlign">1,1</p:property></p:metadata>
            <g transform="translate(0.5, 2.5)">
                <path style="fill: rgb(245, 244, 234); stroke-width: 1px; fill-opacity: 1; stroke: rgb(145, 167, 180); stroke-opacity: 1;" p:name="rect" id="8cf871c850f0403abca63bd821c65501" d="M 0 29.5 L 0 5 C 0 0 0 0 5 0 L 73 0 C 78 0 78 0 78 5 L 78 29.5"/>
                <text x="33.415905" y="14.214675" style="text-align: left; text-anchor: start; dominant-baseline: central; fill: rgb(0, 0, 0); fill-opacity: 1; font-family: PencilUI; font-size: 13px; font-weight: 500; font-style: normal; text-decoration: none;" transform="translate(-13,1)" xml:space="preserve" p:name="text" id="1083072a4b9b484fac082b3223ecb515">Report</text>
            </g>
        </g><g p:type="Shape" p:def="Evolus.WindowsXP.Widgets:TabHeader" id="f49fb79e65f749559d1e11db7ddd0a2d" transform="matrix(1,0,0,1,210.00000312924385,50.0000007301569)"><p:metadata><p:property name="box">100,30</p:property><p:property name="disabled">false</p:property><p:property name="tabTitle">Configuration</p:property><p:property name="textColor">#000000FF</p:property><p:property name="textFont">PencilUI|500|normal|13px|none|0</p:property><p:property name="textAlign">1,1</p:property></p:metadata>
            <g transform="translate(0.5, 2.5)">
                <path style="fill: rgb(245, 244, 234); stroke-width: 1px; fill-opacity: 1; stroke: rgb(145, 167, 180); stroke-opacity: 1;" p:name="rect" id="ce1ba676aea14ce991bb0fe465076d3e" d="M 0 29.5 L 0 5 C 0 0 0 0 5 0 L 93 0 C 98 0 98 0 98 5 L 98 29.5"/>
                <text x="33.415905" y="14.214675" style="text-align: left; text-anchor: start; dominant-baseline: central; fill: rgb(0, 0, 0); fill-opacity: 1; font-family: PencilUI; font-size: 13px; font-weight: 500; font-style: normal; text-decoration: none;" transform="translate(-23,1)" xml:space="preserve" p:name="text" id="d8f718038f634e3796f607be1227bcd8">Configuration</text>
            </g>
        </g><g p:type="Shape" p:def="Evolus.WindowsXP.Widgets:TabHeader" id="98d32bfc2af149f2beebbd7da2076cdb" transform="matrix(1,0,0,1,323.00000481307507,50.0000007301569)"><p:metadata><p:property name="box">80,30</p:property><p:property name="disabled">false</p:property><p:property name="tabTitle">History</p:property><p:property name="textColor">#000000FF</p:property><p:property name="textFont">PencilUI|500|normal|13px|none|0</p:property><p:property name="textAlign">1,1</p:property></p:metadata>
            <g transform="translate(0.5, 2.5)">
                <path style="fill: rgb(245, 244, 234); stroke-width: 1px; fill-opacity: 1; stroke: rgb(145, 167, 180); stroke-opacity: 1;" p:name="rect" id="6f496a713d9a43bbbf169b06329f109a" d="M 0 29.5 L 0 5 C 0 0 0 0 5 0 L 73 0 C 78 0 78 0 78 5 L 78 29.5"/>
                <text x="33.415905" y="14.214675" style="text-align: left; text-anchor: start; dominant-baseline: central; fill: rgb(0, 0, 0); fill-opacity: 1; font-family: PencilUI; font-size: 13px; font-weight: 500; font-style: normal; text-decoration: none;" transform="translate(-14,1)" xml:space="preserve" p:name="text" id="3ccb4dd33fb848eb9e1ee04f487989b3">History</text>
            </g>
        </g><g p:type="Shape" p:def="Evolus.BasicWebElements:pane-v2" id="6871bebd63864fedbf0ae992ce22ea7f" transform="matrix(1,0,0,1,1.0000000149011612,608.0000090450048)"><p:metadata><p:property name="radius">10,0</p:property><p:property name="textPadding">0,0.9999999999999999</p:property><p:property name="fixedRadius">false</p:property><p:property name="fillColor">#EEEEEEFF</p:property><p:property name="shadowStyle">0|0|3|1|#000000</p:property><p:property name="shadowColor">#00000000</p:property><p:property name="strokeColor">#CCCCCCFF</p:property><p:property name="strokeStyle">1|</p:property><p:property name="rightBorder">true</p:property><p:property name="bottomBorder">true</p:property><p:property name="textContent"/><p:property name="textFont">PencilUI|normal|normal|13px|none|0</p:property><p:property name="textColor">#000000FF</p:property><p:property name="textAlign">1,1</p:property><p:property name="box">898,30</p:property><p:property name="cornerStyle">none</p:property><p:property name="customStyle"/></p:metadata>
            <foreignObject x="-3" y="-3" width="904" height="36" p:name="htmlObject" id="23c09e5624034944bdb78591e2e1d2ee" style="padding-top: 3px;">
                <div xmlns="http://www.w3.org/1999/xhtml" style="box-sizing: border-box; overflow: hidden; box-shadow: none; width: 898px; height: 30px; padding: 0px; background-color: rgb(238, 238, 238); x: 10px; border-radius: 0px; border: 1px solid rgb(204, 204, 204); margin-left: 3px;" p:name="div" id="18f5dc3d3b164a1ea12ae143190b82ef">
                </div>
            </foreignObject>
            <foreignObject x="0" y="0" width="898" height="30" p:name="htmlObject2" id="333c00261060401687af47f6120931bd" style="font-family: PencilUI; font-size: 13px; font-weight: normal; font-style: normal; text-decoration: none; fill: rgb(0, 0, 0); fill-opacity: 1; color: rgb(0, 0, 0);">
                <div xmlns="http://www.w3.org/1999/xhtml" style="box-sizing: border-box; overflow: hidden; display: table-cell; width: 898px; height: 30px; padding: 0px; text-align: center; vertical-align: middle;" p:name="div2" id="5e148734bbc34be4a5c3df2e22d62812"><div></div></div>
            </foreignObject>
        </g><g p:type="Shape" p:def="Evolus.Prototype.GUI:frameControl" id="e5d72faaf0644a79a45f5a37854693c2" transform="matrix(1,0,0,1,790.0000000149012,0)"><p:metadata><p:property name="box">100,32</p:property><p:property name="withMinButton">true</p:property><p:property name="withRestoreButton">true</p:property><p:property name="withCloseButton">true</p:property><p:property name="disabled">false</p:property><p:property name="fillColor">#FAFAFAFF</p:property><p:property name="strokeColor">#666666FF</p:property><p:property name="strokeStyle">2|</p:property></p:metadata>
            <path style="fill: none; stroke: rgb(102, 102, 102); stroke-opacity: 1; stroke-width: 2;" p:name="button" id="aae6de62d9c147eab7ab63f99e4510ab" d="M 76 8 L 92 24 M 92 8 L 76 24 M 49 9 L 63 9 L 63 23 L 49 23 z M 21 23 L 35 23" transform="translate(2.5,0)"/>
            <path p:name="box" id="2ba4f2af80864552bba82a4c9278ba90" d="M 0 0 L 100 0 L 100 32 L 0 32 z" style="fill: rgb(250, 250, 250); fill-opacity: 0;"/>
        </g></g></g><g inkscape:label="Report Page" inkscape:groupmode="layer" id="layer_report_page"><g><rect x="0" y="0" width="900" height="639" fill="none"/><g p:type="Shape" p:def="Evolus.BasicWebElements:pane-v2" id="a7147f4719124a56afae328dcfd92d89" transform="matrix(1,0,0,1,3,85)"><p:metadata><p:property name="radius">10,0</p:property><p:property name="textPadding">0,17.3</p:property><p:property name="fixedRadius">false</p:property><p:property name="fillColor">#EEEEEEFF</p:property><p:property name="shadowStyle">0|0|3|1|#000000</p:property><p:property name="shadowColor">#00000000</p:property><p:property name="strokeColor">#CCCCCCFF</p:property><p:property name="strokeStyle">1|</p:property><p:property name="rightBorder">true</p:property><p:property name="bottomBorder">true</p:property><p:property name="textContent"/><p:property name="textFont">"Liberation Sans",Arial,sans-serif|normal|normal|13px|none|0</p:property><p:property name="textColor">#000000FF</p:property><p:property name="textAlign">1,1</p:property><p:property name="box">250,519</p:property><p:property name="cornerStyle">none</p:property><p:property name="customStyle"/></p:metadata>
            <foreignObject x="-3" y="-3" width="256" height="525" p:name="htmlObject" id="f7541e01c3c04c38bb8bc2f144a60cba" style="padding-top: 3px;">
                <div xmlns="http://www.w3.org/1999/xhtml" style="box-sizing: border-box; overflow: hidden; box-shadow: none; width: 250px; height: 519px; padding: 0px; background-color: rgb(238, 238, 238); x: 10px; border-radius: 0px; border: 1px solid rgb(204, 204, 204); margin-left: 3px;" p:name="div" id="a6901b84f5c743328aa3a5446e3dda2e">
                </div>
            </foreignObject>
            <foreignObject x="0" y="0" width="250" height="519" p:name="htmlObject2" id="b5d20e43372e45bdb5a40262f0af0124" style="font-family: &quot;Liberation Sans&quot;, Arial, sans-serif; font-size: 13px; font-weight: normal; font-style: normal; text-decoration: none; fill: rgb(0, 0, 0); fill-opacity: 1; color: rgb(0, 0, 0);">
                <div xmlns="http://www.w3.org/1999/xhtml" style="box-sizing: border-box; overflow: hidden; display: table-cell; width: 250px; height: 519px; padding: 0px; text-align: center; vertical-align: middle;" p:name="div2" id="e1771af1700a4a2b93f9bcf81ce04c97"><div></div></div>
            </foreignObject>
        </g><g p:type="Shape" p:def="Evolus.WindowsXP.Widgets:Groupbox" id="833c1efff2834b989f46b1aaeee3e828" transform="matrix(1,0,0,1,10,107)"><p:metadata><p:property name="box">236,139</p:property><p:property name="label">Analysis Protocols</p:property><p:property name="textColor">#000000FF</p:property><p:property name="textFont">PencilUI|500|normal|10px|none|0</p:property></p:metadata>
            <path d="M 99.3589859008789,0.5 L 231.5,0.5 C 234,0.5 235.5,2.2946114 235.5,4.5 L 235.5,134.5 C 235.5,137 234,138.5 231.5,138.5 L 4.5,138.5 C 2.284,138.5 0.5,137 0.5,134.5 L 0.5,4.5106114 C 0.5,2.2946114 2.284,0.51061056 4.5,0.51061056 L 5.5,0.51061056" style="color:#000000;fill:#ffffff;fill-opacity:0;fill-rule:nonzero;stroke:#aca899;stroke-width:1.00000012;stroke-linecap:butt;stroke-linejoin:miter;marker:none;marker-start:none;marker-mid:none;marker-end:none;stroke-miterlimit:4;stroke-dashoffset:0;stroke-opacity:1;visibility:visible;display:inline;overflow:visible;enable-background:accumulate" p:name="rect" id="80d733d0ca054106bf55008017f1a753"/>
            <text y="9.0077095" x="17.999992" style="font-size: 10px; font-style: normal; font-weight: 500; text-align: left; text-anchor: start; dominant-baseline: central; fill: rgb(0, 0, 0); font-family: PencilUI; fill-opacity: 1; text-decoration: none;" xml:space="preserve" p:name="text" id="c6cea30a95a74da8bb6dfacc938f6fcb" transform="translate(-7,-9)">Analysis Protocols</text>
        </g><g p:type="Shape" p:def="Evolus.WindowsXP.Widgets:Groupbox" id="1ae41a1bc47344028ab92e8f895f011f" transform="matrix(1,0,0,1,10.000000149011612,259.00000385940075)"><p:metadata><p:property name="box">236,104</p:property><p:property name="label">Protocol Series List</p:property><p:property name="textColor">#000000FF</p:property><p:property name="textFont">PencilUI|500|normal|10px|none|0</p:property></p:metadata>
            <path d="M 102.88182830810547,0.5 L 231.5,0.5 C 234,0.5 235.5,2.2946114 235.5,4.5 L 235.5,99.5 C 235.5,102 234,103.5 231.5,103.5 L 4.5,103.5 C 2.284,103.5 0.5,102 0.5,99.5 L 0.5,4.5106114 C 0.5,2.2946114 2.284,0.51061056 4.5,0.51061056 L 5.5,0.51061056" style="color:#000000;fill:#ffffff;fill-opacity:0;fill-rule:nonzero;stroke:#aca899;stroke-width:1.00000012;stroke-linecap:butt;stroke-linejoin:miter;marker:none;marker-start:none;marker-mid:none;marker-end:none;stroke-miterlimit:4;stroke-dashoffset:0;stroke-opacity:1;visibility:visible;display:inline;overflow:visible;enable-background:accumulate" p:name="rect" id="c957fe3e949747e6b6ba5cf0e7837fcd"/>
            <text y="9.0077095" x="17.999992" style="font-size: 10px; font-style: normal; font-weight: 500; text-align: left; text-anchor: start; dominant-baseline: central; fill: rgb(0, 0, 0); font-family: PencilUI; fill-opacity: 1; text-decoration: none;" xml:space="preserve" p:name="text" id="dabae9a4f50042e49d93b4108cf8328c" transform="translate(-8,-9)">Protocol Series List</text>
        </g><g p:type="Shape" p:def="Evolus.WindowsXP.Widgets:Groupbox" id="b329ffd822734ef4872f55a14fdde815" transform="matrix(1,0,0,1,10,376)"><p:metadata><p:property name="box">236,219</p:property><p:property name="label">Selected Images</p:property><p:property name="textColor">#000000FF</p:property><p:property name="textFont">PencilUI|500|normal|10px|none|0</p:property></p:metadata>
            <path d="M 89.9505844116211,0.5 L 231.5,0.5 C 234,0.5 235.5,2.2946114 235.5,4.5 L 235.5,214.5 C 235.5,217 234,218.5 231.5,218.5 L 4.5,218.5 C 2.284,218.5 0.5,217 0.5,214.5 L 0.5,4.5106114 C 0.5,2.2946114 2.284,0.51061056 4.5,0.51061056 L 5.5,0.51061056" style="color:#000000;fill:#ffffff;fill-opacity:0;fill-rule:nonzero;stroke:#aca899;stroke-width:1.00000012;stroke-linecap:butt;stroke-linejoin:miter;marker:none;marker-start:none;marker-mid:none;marker-end:none;stroke-miterlimit:4;stroke-dashoffset:0;stroke-opacity:1;visibility:visible;display:inline;overflow:visible;enable-background:accumulate" p:name="rect" id="5beb415f30e64d928793696c31630a0e"/>
            <text y="9.0077095" x="17.999992" style="font-size: 10px; font-style: normal; font-weight: 500; text-align: left; text-anchor: start; dominant-baseline: central; fill: rgb(0, 0, 0); font-family: PencilUI; fill-opacity: 1; text-decoration: none;" xml:space="preserve" p:name="text" id="70513cd8e79e4e78811b7253fedc7d48" transform="translate(-8,-9)">Selected Images</text>
        </g><g p:type="Shape" p:def="Evolus.WindowsXP.Widgets:Groupbox" id="495171d76bb647ddbb5ef5a0859ea074" transform="matrix(1,0,0,1,258.0000038295984,107.00000159442425)"><p:metadata><p:property name="box">639,489</p:property><p:property name="label">Reports</p:property><p:property name="textColor">#000000FF</p:property><p:property name="textFont">PencilUI|500|normal|10px|none|0</p:property></p:metadata>
            <path d="M 50.61465072631836,0.5 L 634.5,0.5 C 637,0.5 638.5,2.2946114 638.5,4.5 L 638.5,484.5 C 638.5,487 637,488.5 634.5,488.5 L 4.5,488.5 C 2.284,488.5 0.5,487 0.5,484.5 L 0.5,4.5106114 C 0.5,2.2946114 2.284,0.51061056 4.5,0.51061056 L 5.5,0.51061056" style="color:#000000;fill:#ffffff;fill-opacity:0;fill-rule:nonzero;stroke:#aca899;stroke-width:1.00000012;stroke-linecap:butt;stroke-linejoin:miter;marker:none;marker-start:none;marker-mid:none;marker-end:none;stroke-miterlimit:4;stroke-dashoffset:0;stroke-opacity:1;visibility:visible;display:inline;overflow:visible;enable-background:accumulate" p:name="rect" id="ab59996b067e48b3aae913c593f75610"/>
            <text y="9.0077095" x="17.999992" style="font-size: 10px; font-style: normal; font-weight: 500; text-align: left; text-anchor: start; dominant-baseline: central; fill: rgb(0, 0, 0); font-family: PencilUI; fill-opacity: 1; text-decoration: none;" xml:space="preserve" p:name="text" id="72101e73e13b41d49c5ade5b3d004551" transform="translate(-8,-9)">Reports</text>
        </g><g p:type="Shape" p:def="Evolus.Prototype.GUI:textInput" id="220a40e2d78c40ed850035e9f33bcbb7" transform="matrix(1,0,0,1,18,133)"><p:metadata><p:property name="box">180,30</p:property><p:property name="fillColor">#CCCCCCFF</p:property><p:property name="strokeColor">#CCCCCCFF</p:property><p:property name="strokeStyle">1|</p:property><p:property name="textContent">LCD</p:property><p:property name="textFont">PencilUI|500|normal|13px|none|0</p:property><p:property name="textColor">#000000FF</p:property><p:property name="textAlign">0,1</p:property></p:metadata>
            <path style="stroke: rgb(204, 204, 204); stroke-opacity: 1; stroke-width: 1; fill: rgb(204, 204, 204); fill-opacity: 1;" p:name="textBox" id="2ced0539742d4b4a91f3171d2b0e49b0" d="M 0 0 L 180 0 L 180 30 L 0 30 z"/>
            <text p:name="text" id="05c4fd60089b44259461f9be986db24a" transform="translate(10,19)" style="font-family: PencilUI; font-size: 13px; font-weight: 500; font-style: normal; text-decoration: none; fill: rgb(0, 0, 0); fill-opacity: 1;">LCD</text>
        </g><g p:type="Shape" p:def="Evolus.Prototype.GUI:textInput" id="2ad7da16d13340a1b8a357c4ee91c1fa" transform="matrix(1,0,0,1,18.0000002682209,163.00000242888925)"><p:metadata><p:property name="box">180,30</p:property><p:property name="fillColor">#FFFFFFFF</p:property><p:property name="strokeColor">#CCCCCCFF</p:property><p:property name="strokeStyle">1|</p:property><p:property name="textContent">NOISE</p:property><p:property name="textFont">PencilUI|500|normal|13px|none|0</p:property><p:property name="textColor">#000000FF</p:property><p:property name="textAlign">0,1</p:property></p:metadata>
            <path style="stroke: rgb(204, 204, 204); stroke-opacity: 1; stroke-width: 1; fill: rgb(255, 255, 255); fill-opacity: 1;" p:name="textBox" id="7b84cde1e1d5490ba038cd3b50544d46" d="M 0 0 L 180 0 L 180 30 L 0 30 z"/>
            <text p:name="text" id="e24de827a5ef4f4d91de8f3f4bb999a4" transform="translate(10,19)" style="font-family: PencilUI; font-size: 13px; font-weight: 500; font-style: normal; text-decoration: none; fill: rgb(0, 0, 0); fill-opacity: 1;">NOISE</text>
        </g><g p:type="Shape" p:def="Evolus.Prototype.GUI:textInput" id="696e5ad37c6d48ecad93bded4601bdf2" transform="matrix(1,0,0,1,18.0000002682209,193.00000287592408)"><p:metadata><p:property name="box">180,30</p:property><p:property name="fillColor">#FFFFFFFF</p:property><p:property name="strokeColor">#CCCCCCFF</p:property><p:property name="strokeStyle">1|</p:property><p:property name="textContent">CT number</p:property><p:property name="textFont">PencilUI|500|normal|13px|none|0</p:property><p:property name="textColor">#000000FF</p:property><p:property name="textAlign">0,1</p:property></p:metadata>
            <path style="stroke: rgb(204, 204, 204); stroke-opacity: 1; stroke-width: 1; fill: rgb(255, 255, 255); fill-opacity: 1;" p:name="textBox" id="797fb72ae27b4bd8b22677f646db6c41" d="M 0 0 L 180 0 L 180 30 L 0 30 z"/>
            <text p:name="text" id="db597f845e4246a0ac921e32b5279ccb" transform="translate(10,19)" style="font-family: PencilUI; font-size: 13px; font-weight: 500; font-style: normal; text-decoration: none; fill: rgb(0, 0, 0); fill-opacity: 1;">CT number</text>
        </g><g p:type="Shape" p:def="Evolus.Prototype.GUI:checkbox" id="c088c4ee14f441e7bb2319082de56936" transform="matrix(1,0,0,1,210.00000004470348,140)"><p:metadata><p:property name="checked">false</p:property><p:property name="disabled">false</p:property><p:property name="fillColor">#FAFAFAFF</p:property><p:property name="strokeColor">#666666FF</p:property><p:property name="strokeStyle">1|</p:property><p:property name="textContent"/><p:property name="textFont">'Arial'|normal|normal|15px|none|0</p:property><p:property name="textColor">#000000FF</p:property></p:metadata>
            <g p:name="rect" id="7b325b8ff7094ab8a9b819a3b7a9dcd5" style="fill: rgb(250, 250, 250); fill-opacity: 1; stroke: rgb(102, 102, 102); stroke-opacity: 1; stroke-width: 1;">
                 <path d="M 0, 0 L 16, 0, L 16,16 L0,16 z" p:name="cbBox" id="576d0604afbb430fa4db2f345e2a922e"/>
                 <path style="fill: rgb(102, 102, 102); visibility: hidden; display: none;" d="M 4,4 L 12, 4, L 12, 12 L 4, 12 z" p:name="cbTick" id="af891edd78f848efb4b75a046b897e22"/>
            </g>
            <text p:name="text" id="6d9fe233487045688b5b6058e47f4ad7" transform="translate(20,8)" style="font-family: Arial; font-size: 15px; font-weight: normal; font-style: normal; text-decoration: none; fill: rgb(0, 0, 0); fill-opacity: 1;"> </text>
        </g><g p:type="Shape" p:def="Evolus.Prototype.GUI:checkbox" p:sc="Check Box(checked)" id="179f0a0550fd428881c6a2aff15d8f6b" transform="matrix(1,0,0,1,210.0000031143427,170.00000253319737)"><p:metadata><p:property name="checked">true</p:property><p:property name="disabled">false</p:property><p:property name="fillColor">#FAFAFAFF</p:property><p:property name="strokeColor">#666666FF</p:property><p:property name="strokeStyle">2|</p:property><p:property name="textContent"/><p:property name="textFont">'Arial'|normal|normal|15px|none|0</p:property><p:property name="textColor">#000000FF</p:property></p:metadata>
            <g p:name="rect" id="24177e223a2949ddb5484c2ab834d6c1" style="fill: rgb(250, 250, 250); fill-opacity: 1; stroke: rgb(102, 102, 102); stroke-opacity: 1; stroke-width: 2;">
                 <path d="M 0, 0 L 16, 0, L 16,16 L0,16 z" p:name="cbBox" id="967afe1bb14845abbe455cc6cc8a17d1"/>
                 <path style="fill: rgb(102, 102, 102); visibility: visible;" d="M 4,4 L 12, 4, L 12, 12 L 4, 12 z" p:name="cbTick" id="5036d239242245ea9a2555b6941845fc"/>
            </g>
            <text p:name="text" id="6ed48d6a7121446b961c602662346003" transform="translate(20,8)" style="font-family: Arial; font-size: 15px; font-weight: normal; font-style: normal; text-decoration: none; fill: rgb(0, 0, 0); fill-opacity: 1;"> </text>
        </g><g p:type="Shape" p:def="Evolus.Prototype.GUI:checkbox" p:sc="Check Box(checked)" id="0151611b77e74a5f83d5073780b807a2" transform="matrix(1,0,0,1,210.00000312924385,200.0000029802322)"><p:metadata><p:property name="checked">true</p:property><p:property name="disabled">false</p:property><p:property name="fillColor">#FAFAFAFF</p:property><p:property name="strokeColor">#666666FF</p:property><p:property name="strokeStyle">2|</p:property><p:property name="textContent"/><p:property name="textFont">'Arial'|normal|normal|15px|none|0</p:property><p:property name="textColor">#000000FF</p:property></p:metadata>
            <g p:name="rect" id="9b28131349d94531b43e8c8871bb6014" style="fill: rgb(250, 250, 250); fill-opacity: 1; stroke: rgb(102, 102, 102); stroke-opacity: 1; stroke-width: 2;">
                 <path d="M 0, 0 L 16, 0, L 16,16 L0,16 z" p:name="cbBox" id="d0f5b86657e541bdb67ef9104a58f1e2"/>
                 <path style="fill: rgb(102, 102, 102); visibility: visible;" d="M 4,4 L 12, 4, L 12, 12 L 4, 12 z" p:name="cbTick" id="d1c168db1f904f16a56d63aae806499b"/>
            </g>
            <text p:name="text" id="b81745de151d4109b456463a38c767ff" transform="translate(20,8)" style="font-family: Arial; font-size: 15px; font-weight: normal; font-style: normal; text-decoration: none; fill: rgb(0, 0, 0); fill-opacity: 1;"> </text>
        </g><g p:type="Shape" p:def="Evolus.Prototype.GUI:textInput" id="ca5aa61672fb4916afd07b635388a988" transform="matrix(1,0,0,1,18.000002175569534,282.00000420212746)"><p:metadata><p:property name="box">180,30</p:property><p:property name="fillColor">#FFFFFFFF</p:property><p:property name="strokeColor">#CCCCCCFF</p:property><p:property name="strokeStyle">1|</p:property><p:property name="textContent">LCD-Series1</p:property><p:property name="textFont">PencilUI|500|normal|13px|none|0</p:property><p:property name="textColor">#000000FF</p:property><p:property name="textAlign">0,1</p:property></p:metadata>
            <path style="stroke: rgb(204, 204, 204); stroke-opacity: 1; stroke-width: 1; fill: rgb(255, 255, 255); fill-opacity: 1;" p:name="textBox" id="4f80bc8b5e7a474f9059708735556c7d" d="M 0 0 L 180 0 L 180 30 L 0 30 z"/>
            <text p:name="text" id="821123dc3ff94532a1ffaa98647e5c7e" transform="translate(10,19)" style="font-family: PencilUI; font-size: 13px; font-weight: 500; font-style: normal; text-decoration: none; fill: rgb(0, 0, 0); fill-opacity: 1;">LCD-Series1</text>
        </g><g p:type="Shape" p:def="Evolus.Prototype.GUI:textInput" id="fd8715f108084946b011f3403204cefb" transform="matrix(1,0,0,1,18.0000002682209,312.00000466406345)"><p:metadata><p:property name="box">180,30</p:property><p:property name="fillColor">#FFFFFFFF</p:property><p:property name="strokeColor">#CCCCCCFF</p:property><p:property name="strokeStyle">1|</p:property><p:property name="textContent">LCD-Series2</p:property><p:property name="textFont">PencilUI|500|normal|13px|none|0</p:property><p:property name="textColor">#000000FF</p:property><p:property name="textAlign">0,1</p:property></p:metadata>
            <path style="stroke: rgb(204, 204, 204); stroke-opacity: 1; stroke-width: 1; fill: rgb(255, 255, 255); fill-opacity: 1;" p:name="textBox" id="e09038c17c2f46dda2082c71901c7144" d="M 0 0 L 180 0 L 180 30 L 0 30 z"/>
            <text p:name="text" id="3f4d2bd737ca46a4b5685755d96d8cc3" transform="translate(10,19)" style="font-family: PencilUI; font-size: 13px; font-weight: 500; font-style: normal; text-decoration: none; fill: rgb(0, 0, 0); fill-opacity: 1;">LCD-Series2</text>
        </g><g p:type="Shape" p:def="Evolus.Prototype.GUI:checkbox" id="c66d2e7cf9e14853a88be980fd957bb6" transform="matrix(1,0,0,1,210.00000312924385,289.0000043064356)"><p:metadata><p:property name="checked">false</p:property><p:property name="disabled">false</p:property><p:property name="fillColor">#FAFAFAFF</p:property><p:property name="strokeColor">#666666FF</p:property><p:property name="strokeStyle">1|</p:property><p:property name="textContent"/><p:property name="textFont">'Arial'|normal|normal|15px|none|0</p:property><p:property name="textColor">#000000FF</p:property></p:metadata>
            <g p:name="rect" id="a4b06a91d0204befac0a79c9f0f706b9" style="fill: rgb(250, 250, 250); fill-opacity: 1; stroke: rgb(102, 102, 102); stroke-opacity: 1; stroke-width: 1;">
                 <path d="M 0, 0 L 16, 0, L 16,16 L0,16 z" p:name="cbBox" id="d00f6982321849a2a118c8aead57f840"/>
                 <path style="fill: rgb(102, 102, 102); visibility: hidden; display: none;" d="M 4,4 L 12, 4, L 12, 12 L 4, 12 z" p:name="cbTick" id="a988ddf19dd14ea9bc108ee1d3e8691d"/>
            </g>
            <text p:name="text" id="d661142f89b04cb79e29e2e07e42c7c0" transform="translate(20,8)" style="font-family: Arial; font-size: 15px; font-weight: normal; font-style: normal; text-decoration: none; fill: rgb(0, 0, 0); fill-opacity: 1;"> </text>
        </g><g p:type="Shape" p:def="Evolus.Prototype.GUI:checkbox" p:sc="Check Box(checked)" id="72bcbfd4f68a44398dc6d9672cc8b6ec" transform="matrix(1,0,0,1,210.00000312924385,319.0000047534704)"><p:metadata><p:property name="checked">true</p:property><p:property name="disabled">false</p:property><p:property name="fillColor">#FAFAFAFF</p:property><p:property name="strokeColor">#666666FF</p:property><p:property name="strokeStyle">2|</p:property><p:property name="textContent"/><p:property name="textFont">'Arial'|normal|normal|15px|none|0</p:property><p:property name="textColor">#000000FF</p:property></p:metadata>
            <g p:name="rect" id="e2c14a7f08c842349aac97b7cdabc597" style="fill: rgb(250, 250, 250); fill-opacity: 1; stroke: rgb(102, 102, 102); stroke-opacity: 1; stroke-width: 2;">
                 <path d="M 0, 0 L 16, 0, L 16,16 L0,16 z" p:name="cbBox" id="016d3cf8f5544422983fad30d933c633"/>
                 <path style="fill: rgb(102, 102, 102); visibility: visible;" d="M 4,4 L 12, 4, L 12, 12 L 4, 12 z" p:name="cbTick" id="643f7c2752354d5ca7b0949f92708694"/>
            </g>
            <text p:name="text" id="8e6a1539bf63478a9a8917cde5b37e06" transform="translate(20,8)" style="font-family: Arial; font-size: 15px; font-weight: normal; font-style: normal; text-decoration: none; fill: rgb(0, 0, 0); fill-opacity: 1;"> </text>
        </g><g p:type="Shape" p:def="Evolus.Sketchy.GUI:image" id="27b8d2d4833e41f597384d6740604909" transform="matrix(1,0,0,1,20,393)"><p:metadata><p:property name="box">206,180</p:property><p:property name="text">true</p:property><p:property name="fillColor">#FFFFFFFF</p:property><p:property name="strokeColor">#000000FF</p:property><p:property name="strokeStyle">1|</p:property><p:property name="textContent">Message</p:property><p:property name="textFont">'Comic Sans MS'|normal|normal|12px|none|0</p:property><p:property name="textColor">#000000FF</p:property><p:property name="textAlign">1,1</p:property></p:metadata>
            <g p:name="rect" id="06d9f89ea7f645a7b98de33e9f5125e1" style="fill: rgb(255, 255, 255); fill-opacity: 1; stroke: rgb(0, 0, 0); stroke-opacity: 1; stroke-width: 1;">
                <path style="stroke-linejoin: round;" p:name="line1" id="8150426b51aa4fcba72c7fc29b926248" d="M 0 0 C 69 -1 137 -1 206 0 C 206 60 206 120 206 180 C 137 180 69 180 0 180 C -2 120 -2 60 0 0 z M 3 3 C 70 61 136 119 203 177 M 3 177 C 71 120 137 62 203 3"/>
            </g>
            <path style="stroke: none; fill: rgb(255, 255, 255); fill-opacity: 1; visibility: visible;" p:name="mask" id="0b34a31248b144e1818646da4de3df7b" d="M 2.5 84 L 203.5 84 L 203.5 96 L 2.5 96 z"/>
            <foreignObject x="0" y="82" width="206" height="17" p:name="text" id="99a7d9261b0544db9bedf70279195b7b" style="font-family: &quot;Comic Sans MS&quot;; font-size: 12px; font-weight: normal; font-style: normal; text-decoration: none; color: rgb(0, 0, 0); text-align: center; visibility: visible;"><div xmlns="http://www.w3.org/1999/xhtml">206 x 180</div></foreignObject>
        </g><g p:type="Shape" p:def="Evolus.WindowsXP.Widgets:Label" id="f9ec1b4ef0db4083a5d5025b2002d236" transform="matrix(1,0,0,1,219.0000032633543,578.00000859797)"><p:metadata><p:property name="disabled">false</p:property><p:property name="label">&gt;</p:property><p:property name="textColor">#FF0000FF</p:property><p:property name="textFont">PencilUI|bold|normal|13px|none|0</p:property></p:metadata>
              <text y="9.0077095" x="17.999992" style="font-size: 13px; font-style: normal; font-weight: bold; text-align: left; text-anchor: start; dominant-baseline: central; fill: rgb(255, 0, 0); font-family: PencilUI; fill-opacity: 1; text-decoration: none;" xml:space="preserve" p:name="text" id="ec002eae177a43179a0ebffd6be0394d" transform="translate(-18,-3)">&gt;</text>
             <path style="fill: none; stroke-width: 1px; visibility: hidden;" p:name="accelIndicator" id="6073f02a2fe64551a5b007381b29c1c5"/>
        </g><g p:type="Shape" p:def="Evolus.WindowsXP.Widgets:Label" id="e2954212b3ad4f57b63409e8611f6ae1" transform="matrix(1,0,0,1,210.00000312924385,578.00000859797)"><p:metadata><p:property name="disabled">false</p:property><p:property name="label">&lt;</p:property><p:property name="textColor">#FF0000FF</p:property><p:property name="textFont">PencilUI|bold|normal|13px|none|0</p:property></p:metadata>
              <text y="9.0077095" x="17.999992" style="font-size: 13px; font-style: normal; font-weight: bold; text-align: left; text-anchor: start; dominant-baseline: central; fill: rgb(255, 0, 0); font-family: PencilUI; fill-opacity: 1; text-decoration: none;" xml:space="preserve" p:name="text" id="8ec3c49636c342cdb11c390414aa292e" transform="translate(-18,-3)">&lt;</text>
             <path style="fill: none; stroke-width: 1px; visibility: hidden;" p:name="accelIndicator" id="ef277f6d15b0467c944195a30fd12a4e"/>
        </g><g p:type="Shape" p:def="Evolus.Prototype.GUI:richTextInput" id="20d503257a9343f1a31cb2c86d202a01" transform="matrix(1,0,0,1,269,110.99999997019768)"><p:metadata><p:property name="box">619,480</p:property><p:property name="fillColor">#FFFFFFFF</p:property><p:property name="strokeColor">#666666FF</p:property><p:property name="strokeStyle">0|</p:property><p:property name="textContent">&lt;font xmlns="http://www.w3.org/1999/xhtml" style="font-size: 1.4em;"&gt;Secrevit  fontes liquidum locoque pronaque?
&lt;br xmlns="http://www.w3.org/1999/xhtml" /&gt;&lt;br xmlns="http://www.w3.org/1999/xhtml" /&gt;&lt;span style="font-weight: normal;"&gt;Illas semine campoque declivia oppida corpora nam inter fuit discordia tellus solidumque iunctarum erat: quae terrenae ubi rerum recessit iudicis aestu fixo&lt;/span&gt;&lt;/font&gt;</p:property><p:property name="textFont">PencilUI|normal|normal|10px|none|0</p:property><p:property name="textColor">#000000FF</p:property></p:metadata>
            <path p:name="textBox" id="c94162f11ec54f448765c180af81edcf" d="M 0 0 L 619 0 L 619 480 L 0 480 z" style="stroke: rgb(102, 102, 102); stroke-opacity: 1; stroke-width: 0; fill: rgb(255, 255, 255); fill-opacity: 1;"/>
            <path style="stroke-linejoin: round; stroke: rgb(192, 192, 192); stroke-opacity: 1; fill: rgb(192, 192, 192); fill-opacity: 1; stroke-width: 0;" p:name="collapse" id="54f333e0c9a946bbb98280d5dd17900b" d="M 614 465 L 614 475 L 604 475 z"/>
            <clipPath p:name="textClipPath" id="0c8e76cec4c64a488672250345a08237">
                <rect p:name="textClipPathRect" id="f822431ab06b42e4abb543b3834d567a" x="10" y="10" width="599" height="460"/>
            </clipPath>
            <g clip-path="url(#0c8e76cec4c64a488672250345a08237)" p:name="text" id="b4591958b9314a25ae2cbbe928c261b3" dominant-baseline="auto" style="font-family: PencilUI; font-size: 10px; font-weight: normal; font-style: normal; text-decoration: none; fill: rgb(0, 0, 0); fill-opacity: 1; color: rgb(0, 0, 0);"><text><tspan x="10" y="24" style="font-family: PencilUI; font-size: 14px; font-weight: 400; font-style: normal; text-decoration: none solid rgb(0, 0, 0); text-transform: none; line-height: 0px; fill: rgb(0, 0, 0); background-color: rgba(0, 0, 0, 0);">Secrevit  fontes liquidum locoque pronaque? </tspan><tspan x="10" y="57" style="font-family: PencilUI; font-size: 14px; font-weight: 400; font-style: normal; text-decoration: none solid rgb(0, 0, 0); text-transform: none; line-height: 0px; fill: rgb(0, 0, 0); background-color: rgba(0, 0, 0, 0);">Illas semine campoque declivia oppida corpora nam inter fuit discordia tellus solidumque</tspan><tspan x="10" y="74" style="font-family: PencilUI; font-size: 14px; font-weight: 400; font-style: normal; text-decoration: none solid rgb(0, 0, 0); text-transform: none; line-height: 0px; fill: rgb(0, 0, 0); background-color: rgba(0, 0, 0, 0);">iunctarum erat: quae terrenae ubi rerum recessit iudicis aestu fixo</tspan></text></g>
        </g><g p:type="Shape" p:def="Evolus.WindowsXP.Widgets:TabHeader" id="07316987f3934d5292a8cb453f3bd50c" transform="matrix(1,0,0,1,809.0000120550394,82.00000122189522)"><p:metadata><p:property name="box">80,23</p:property><p:property name="disabled">false</p:property><p:property name="tabTitle">Export Pdf</p:property><p:property name="textColor">#000000FF</p:property><p:property name="textFont">Tahoma|normal|normal|11px|none|0</p:property><p:property name="textAlign">1,1</p:property></p:metadata>
            <g transform="translate(0.5, 2.5)">
                <path style="fill: rgb(245, 244, 234); stroke-width: 1px; fill-opacity: 1; stroke: rgb(145, 167, 180); stroke-opacity: 1;" p:name="rect" id="dcb26febd92e4bc0a5242a7681630600" d="M 0 22.5 L 0 5 C 0 0 0 0 5 0 L 73 0 C 78 0 78 0 78 5 L 78 22.5"/>
                <text x="33.415905" y="14.214675" style="text-align: left; text-anchor: start; dominant-baseline: central; fill: rgb(0, 0, 0); fill-opacity: 1; font-family: Tahoma; font-size: 11px; font-weight: normal; font-style: normal; text-decoration: none;" transform="translate(-19,-3)" xml:space="preserve" p:name="text" id="34353ae1fb4843da9472bbc87e632dcf">Export Pdf</text>
            </g>
        </g><g p:type="Shape" p:def="Evolus.Sketchy.GUI:lineV2" p:sc="Horizontal Line" id="1cf2b73806524295a0a2c9ed02fe23d0" transform="matrix(1,0,0,1,119,55)"><p:metadata><p:property name="startLine">0,0</p:property><p:property name="endLine">76,0</p:property><p:property name="mode">horizontal</p:property><p:property name="strokeColor">#FF0000FF</p:property><p:property name="strokeStyle">2|</p:property></p:metadata>
            <g p:name="rect" id="574e8e8a25f74b5a8b080921f02b62dd" transform="translate(0,0)" style="stroke: rgb(255, 0, 0); stroke-opacity: 1; stroke-width: 2;">
    			<path style="stroke: #FFFFFF; stroke-opacity: 0; stroke-width: 8px; fill: none;" p:name="bg" id="a26eec470d7941ca87c2472af084e026" transform="translate(0,0)" d="M 0 0 C 25 -1 51 -1 76 0"/>
    			<path style="stroke-linejoin: round; fill: none;" p:name="line1" id="6ed426a3abb34f53a59f3ae76afece63" transform="translate(0,0)" d="M 0 0 C 25 -1 51 -1 76 0"/>
    		</g>
        </g></g></g></svg>