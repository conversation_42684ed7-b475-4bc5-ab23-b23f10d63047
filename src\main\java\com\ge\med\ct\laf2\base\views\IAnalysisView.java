package com.ge.med.ct.laf2.base.views;

import com.ge.med.ct.analysis.model.AnalysisParams;
import com.ge.med.ct.laf2.base.MessageType;
import com.ge.med.ct.laf2.base.listeners.IAnalysisViewListener;

/**
 * 分析视图接口
 * 提供分析功能所需的UI交互方法
 */
public interface IAnalysisView extends IBaseView<IAnalysisViewListener> {
    /**
     * 设置分析状态
     * @param isAnalyzing 是否正在分析
     */
    void setAnalyzing(boolean isAnalyzing);

    /**
     * 刷新患者信息显示
     * 从数据提供者获取最新患者数据并更新UI
     */
    void refreshPatientInfo();

    /**
     * 重置患者信息
     * 清除UI中显示的所有患者相关数据
     */
    void resetPatientInfo();

    /**
     * 获取当前分析参数
     * 包含输入路径、输出路径和协议信息
     * @return 分析参数对象，包含所有必要的分析参数
     */
    AnalysisParams getAnalysisParams();
    
    /**
     * 记录带类型的日志
     * 根据消息类型显示不同样式的信息
     * @param type 消息类型(信息/警告/错误)
     * @param text 要显示的文本内容
     */
    void logToView(MessageType type, String text);
}