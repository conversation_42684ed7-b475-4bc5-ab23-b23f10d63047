package com.ge.med.ct.exception.message;

/**
 * DICOM消息枚举
 * 定义DICOM相关的消息
 */
public enum DicomMessages implements Message {
    // ==================== 基础错误 ====================
    UNKNOWN_ERROR("dicom.unknown", "未知DICOM错误"),
    PROCESSING_ERROR("dicom.processing.error", "处理错误: {0}, {1}"),
    VALIDATION_ERROR("dicom.validation.error", "验证错误: {0}"),
    VALIDATION_FAILED("dicom.validation.failed", "DICOM文件验证失败: {0}"),

    SYSTEM_INIT("system.init", "系统初始化完成: 根目录={0}"),
    SYSTEM_SHUTDOWN("system.shutdown", "系统关闭 {0}"),
    // 文件错误
    FILE_NOT_FOUND("dicom.file.not.found", "DICOM文件不存在: {0}"),
    FILE_READ_ERROR("dicom.file.read.error", "读取DICOM文件失败: {0}"),
    FILE_WRITE_ERROR("dicom.file.write.error", "写入DICOM文件失败: {0}"),
    FILE_INVALID("dicom.file.invalid", "无效的DICOM文件: {0}"),
    FILE_CHANGED("dicom.file.changed", "检测到DICOM文件变化: {0}"),
    SIZE_LIMIT_EXCEEDED("dicom.file.size.limit.exceeded", "FILE SIZE超出限制: {0}"),
    SIZE_MISMATCH("dicom.file.size.mismatch", "FILE SIZE 不匹配: {0}"),
    COUNT_FAILED("dicom.file.count.failed", "计数操作失败"),
    // 目录错误
    DIRECTORY_NOT_EXIST("dicom.directory.not.exist", "DICOM扫描目录不存在: {0}"),
    DIRECTORY_CREATE_FAILED("dicom.directory.create.failed", "无法创建DICOM扫描目录: {0}"),
    DIRECTORY_INVALID("dicom.directory.invalid", "无效的目录: {0}"),

    // 扫描与监控
    SCAN_START("dicom.scan.start", "开始扫描目录: {0} {1}"),
    SCAN_COMPLETE("dicom.scan.complete", "扫描完成: 发现 {0} 个文件"),
    SCAN_FAILED("dicom.scan.failed", "扫描失败: {0}"),
    MONITOR_START("dicom.monitor.start", "开始监控目录: {0}"),
    MONITOR_STOP("dicom.monitor.stop", "停止监控目录: {0}"),
    MONITOR_FAILED("dicom.monitor.failed", "监控失败: {0}"),

    // ==================== DICOM数据相关 ====================
    // 患者信息
    EMPTY_PATIENT_NAME("dicom.patient.name.empty", "PATIENT NAME为空: {0}"),
    EMPTY_PATIENT_ID("dicom.patient.id.empty", "PATIENT ID为空: {0}"),

    // 检查相关
    EXAM_PROCESSING_ERROR("dicom.exam.processing.error", "检查处理错误: {0}"),
    EXAM_INVALID("dicom.exam.invalid", "无效的检查: {0}"),
    EXAM_EMPTY("dicom.exam.empty", "检查为空: {0}"),

    // 序列相关
    SERIES_PROCESSING_ERROR("dicom.series.processing.error", "序列处理错误: {0}"),
    SERIES_INVALID("dicom.series.invalid", "无效的序列: {0}"),
    SERIES_EMPTY("dicom.series.empty", "序列为空: {0}"),

    // 图像相关
    IMAGE_PROCESSING_ERROR("dicom.image.processing.error", "图像处理错误: {0}"),
    IMAGE_INVALID("dicom.image.invalid", "无效的图像数据: {0}"),
    PIXEL_DATA_EMPTY("dicom.pixel.data.empty", "像素数据为空"),
    INVALID_BITS_ALLOCATED("dicom.bits.allocated.invalid", "DICOM BITS ALLOCATED无效"),
    INVALID_DIMENSION("dicom.image.dimension.invalid", "DICOM图像尺寸无效"),
    INVALID_IMAGE_PARAMS("dicom.image.params.invalid", "DICOM图像参数无效: {0}"),
    INVALID_SAMPLES("dicom.samples.invalid", "DICOM采样数无效"),

    // ==================== 标签与属性相关 ====================
    TAG_PARSE_FAILED("dicom.tag.parse.failed", "解析DICOM标签失败: {0}"),
    TAG_MISSING("dicom.tag.missing", "缺少必需的标签: {0}"),
    TAG_INVALID("dicom.tag.invalid", "无效的标签值: {0} = {1}"),
    TAG_CREATE_FAILED("dicom.tag.create.failed", "创建DICOM标签失败: {0}"),
    ATTRIBUTES_EMPTY("dicom.attributes.empty", "DICOM 属性为空"),

    // ==================== 系统与资源相关 ====================
    // 缓存相关
    CACHE_INIT("dicom.cache.init", "初始化缓存: 大小={0}"),
    CACHE_CLEAR("dicom.cache.clear", "清除缓存"),
    CACHE_HIT("dicom.cache.hit", "缓存命中: {0}"),
    CACHE_MISS("dicom.cache.miss", "缓存未命中: {0}"),

    // 数据模型相关
    DATA_CLEARED("dicom.data.cleared", "数据已清除"),
    MODEL_NULL("dicom.model.null", "模型为空"),
    MODEL_EXISTS("dicom.model.exists", "模型存在"),
    ID_EMPTY("dicom.id.empty", "ID不能为空");

    private final AbstractMessage delegate;

    DicomMessages(String key, String defaultMessage) {
        this.delegate = new AbstractMessage(key, defaultMessage) {};
    }

    @Override
    public String getKey() {
        return delegate.getKey();
    }

    @Override
    public String getDefaultMessage() {
        return delegate.getDefaultMessage();
    }

    @Override
    public Message format(Object... args) {
        return delegate.format(args);
    }

    @Override
    public String toStr() {
        return delegate.toStr();
    }
}
