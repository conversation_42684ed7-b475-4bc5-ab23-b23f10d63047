package com.ge.med.ct.cfg.table;

import com.ge.med.ct.cfg.AppDefaults;

/**
 * 表格列构建器
 * 用于构建TableColumn对象
 */
public class TableColumnBuilder {
    private final String name;
    private String tagId = "";
    private String displayName;
    private int width = AppDefaults.DEFAULT_COLUMN_WIDTH;
    private boolean visible = AppDefaults.DEFAULT_COLUMN_VISIBLE;
    private int order = AppDefaults.DEFAULT_COLUMN_ORDER;

    /**
     * 构造函数
     * 
     * @param name 列名
     */
    public TableColumnBuilder(String name) {
        this.name = name;
        this.displayName = name;
    }

    /**
     * 设置标签ID
     * 
     * @param tagId 标签ID
     * @return 构建器实例
     */
    public TableColumnBuilder tagId(String tagId) {
        this.tagId = tagId;
        return this;
    }

    /**
     * 设置显示名称
     * 
     * @param displayName 显示名称
     * @return 构建器实例
     */
    public TableColumnBuilder displayName(String displayName) {
        this.displayName = displayName;
        return this;
    }

    /**
     * 设置列宽度
     * 
     * @param width 列宽度
     * @return 构建器实例
     */
    public TableColumnBuilder width(int width) {
        this.width = width;
        return this;
    }

    /**
     * 设置是否可见
     * 
     * @param visible 是否可见
     * @return 构建器实例
     */
    public TableColumnBuilder visible(boolean visible) {
        this.visible = visible;
        return this;
    }

    /**
     * 设置排序顺序
     * 
     * @param order 排序顺序
     * @return 构建器实例
     */
    public TableColumnBuilder order(int order) {
        this.order = order;
        return this;
    }

    /**
     * 构建TableColumn对象
     * 
     * @return 构建的TableColumn对象
     */
    public TableColumn build() {
        return new TableColumn(name, tagId, displayName, width, visible, order);
    }
}
