package com.ge.med.ct.laf2.utils;

import java.util.Collections;

public class ReportFormatter {
    private static final int[] COLUMN_WIDTHS = new int[]{10, 15, 20, 25, 15, 15};
    private static final String DOT = " ";
    private static final String SIX_DOTS = String.join("", Collections.nCopies(6, DOT));
    private static final String THREE_DOTS = String.join("", Collections.nCopies(3, DOT));
    
    public static String format(String content) {
        if (content == null || content.isEmpty()) {
            return "";
        }
        
        StringBuilder formattedReport = new StringBuilder();
        String[] lines = content.split("\n");
        boolean isHeaderSection = true;
        
        for (String line : lines) {
            if (line.trim().isEmpty()) {
                formattedReport.append("\n");
                continue;
            }
            
            // 处理分隔线
            if (isSeparatorLine(line)) {
                formattedReport.append(formatSeparatorLine());
                isHeaderSection = false;
                continue;
            }
            
            // 处理数据行
            String formattedLine = formatLine(line.trim(), isHeaderSection);
            formattedReport.append(formattedLine).append("\n");
        }
        
        return formattedReport.toString();
    }
    
    private static boolean isSeparatorLine(String line) {
        return line.trim().replaceAll("[\\-=]", "").isEmpty();
    }
    
    private static String formatSeparatorLine() {
        StringBuilder separator = new StringBuilder();
        for (int width : COLUMN_WIDTHS) {
            separator.append(String.join("", Collections.nCopies(width, "-")));
        }
        return separator.toString() + "\n";
    }
    
    private static String formatLine(String line, boolean isHeaderSection) {
        String[] columns = line.split("\\s+");
        if (columns.length == 0) {
            return line;
        }
        
        StringBuilder formattedLine = new StringBuilder();
        
        // 处理每一列
        for (int i = 0; i < columns.length && i < COLUMN_WIDTHS.length; i++) {
            String column = columns[i];
            int width = COLUMN_WIDTHS[i];
            
            // 如果不是最后一列，使用点号填充
            if (i < COLUMN_WIDTHS.length - 1) {
                formattedLine.append(padWithDots(column, width));
            } else {
                // 最后一列右对齐，不使用点号填充
                formattedLine.append(String.format("%" + width + "s", column));
            }
        }
        
        // 添加颜色标记
        if (isHeaderSection && !line.trim().isEmpty()) {
            formattedLine.insert(0, SIX_DOTS);
            formattedLine.append(THREE_DOTS);
        } else if (!isHeaderSection && line.matches(".*\\d.*")) {
            formattedLine.insert(0, SIX_DOTS);
            formattedLine.append(THREE_DOTS);
        }
        
        return formattedLine.toString();
    }
    
    private static String padWithDots(String text, int width) {
        if (text == null || text.isEmpty()) {
            return String.join("", Collections.nCopies(width, DOT));
        }
        
        int padding = width - text.length();
        if (padding <= 0) {
            return text;
        }
        
        return text + String.join("", Collections.nCopies(padding, DOT));
    }
} 