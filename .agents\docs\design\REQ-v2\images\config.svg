<svg xmlns="http://www.w3.org/2000/svg" xmlns:p="http://www.evolus.vn/Namespace/Pencil" xmlns:pencil="http://www.evolus.vn/Namespace/Pencil" xmlns:html="http://www.w3.org/1999/xhtml" xmlns:xhtml="http://www.w3.org/1999/xhtml" xmlns:office="urn:oasis:names:tc:opendocument:xmlns:office:1.0" xmlns:style="urn:oasis:names:tc:opendocument:xmlns:style:1.0" xmlns:text="urn:oasis:names:tc:opendocument:xmlns:text:1.0" xmlns:table="urn:oasis:names:tc:opendocument:xmlns:table:1.0" xmlns:draw="urn:oasis:names:tc:opendocument:xmlns:drawing:1.0" xmlns:fo="urn:oasis:names:tc:opendocument:xmlns:xsl-fo-compatible:1.0" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:meta="urn:oasis:names:tc:opendocument:xmlns:meta:1.0" xmlns:number="urn:oasis:names:tc:opendocument:xmlns:datastyle:1.0" xmlns:svg="urn:oasis:names:tc:opendocument:xmlns:svg-compatible:1.0" xmlns:chart="urn:oasis:names:tc:opendocument:xmlns:chart:1.0" xmlns:dr3d="urn:oasis:names:tc:opendocument:xmlns:dr3d:1.0" xmlns:math="http://www.w3.org/1998/Math/MathML" xmlns:form="urn:oasis:names:tc:opendocument:xmlns:form:1.0" xmlns:script="urn:oasis:names:tc:opendocument:xmlns:script:1.0" xmlns:ooo="http://openoffice.org/2004/office" xmlns:ooow="http://openoffice.org/2004/writer" xmlns:oooc="http://openoffice.org/2004/calc" xmlns:dom="http://www.w3.org/2001/xml-events" xmlns:xforms="http://www.w3.org/2002/xforms" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:rpt="http://openoffice.org/2005/report" xmlns:of="urn:oasis:names:tc:opendocument:xmlns:of:1.2" xmlns:rdfa="http://docs.oasis-open.org/opendocument/meta/rdfa#" xmlns:field="urn:openoffice:names:experimental:ooo-ms-interop:xmlns:field:1.0" xmlns:regexp="http://exslt.org/regular-expressions" xmlns:em="http://exslt.org/math" xmlns:sodipodi="http://sodipodi.sourceforge.net/DTD/sodipodi-0.dtd" xmlns:inkscape="http://www.inkscape.org/namespaces/inkscape" width="900" height="639" id="exportedSVG" version="1.1" pencil:version="1.2.2" sodipodi:docname="qat"><g inkscape:label="Home" inkscape:groupmode="layer" id="layer_home"><g><rect x="0" y="0" width="900" height="639" fill="none"/><g p:type="Shape" p:def="Evolus.Prototype.GUI:frame" id="16dd3fe80c774e9daf3275e3e1036ae9" transform="matrix(1,0,0,1,0,0)"><p:metadata><p:property name="box">900,639</p:property><p:property name="disabled">false</p:property><p:property name="fillColor">#FAFAFAFF</p:property><p:property name="titleColor">#DEDEDEFF</p:property><p:property name="strokeColor">#666666FF</p:property><p:property name="strokeStyle">1|</p:property><p:property name="textContent">Quality Assurance Tool</p:property><p:property name="textFont">'Arial'|Bold|normal|15px|none|0</p:property><p:property name="textColor">#000000FF</p:property><p:property name="textAlign">0,1</p:property></p:metadata>
            <path style="fill: rgb(250, 250, 250); stroke: rgb(102, 102, 102); stroke-opacity: 1; stroke-width: 1; fill-opacity: 1;" p:name="body" id="156989098fa84db58192be5505270894" d="M 0 32 L 0 0 L 900 0 L 900 639 L 0 639 L 0 32 L 900 32"/>
            <path p:name="menu" id="2385f86ee1f9499d8459d66ade571623" d="M 0.5 0.5 L 899.5 0.5 L 899.5 31 L 0.5 31 z" style="fill: rgb(222, 222, 222); fill-opacity: 1;"/>
            <text p:name="text" id="12c514430ef64aa3bf70c5d4f75f8a3c" transform="translate(10,21)" style="font-family: Arial; font-size: 15px; font-weight: bold; font-style: normal; text-decoration: none; fill: rgb(0, 0, 0); fill-opacity: 1;"><tspan x="0" y="0">Quality Assurance Tool</tspan></text>
        </g><g p:type="Shape" p:def="Evolus.Prototype.GUI:frameControl" id="bd28b958c1c14a369af1fb45120fc838" transform="matrix(1,0,0,1,896.0000133514404,1.0000000298023224)"><p:metadata><p:property name="box">90,30</p:property><p:property name="withMinButton">true</p:property><p:property name="withRestoreButton">true</p:property><p:property name="withCloseButton">true</p:property><p:property name="disabled">false</p:property><p:property name="fillColor">#FAFAFAFF</p:property><p:property name="strokeColor">#666666FF</p:property><p:property name="strokeStyle">2|</p:property></p:metadata>
            <path style="fill: none; stroke: rgb(102, 102, 102); stroke-opacity: 1; stroke-width: 2;" p:name="button" id="64d598d582fb42339385213db6c49927" d="M 66 8 L 82 24 M 82 8 L 66 24 M 39 9 L 53 9 L 53 23 L 39 23 z M 11 23 L 25 23" transform="translate(2.5,-1)"/>
            <path p:name="box" id="97831e430839474f8214fc74c1b9be94" d="M 0 0 L 90 0 L 90 30 L 0 30 z" style="fill: rgb(250, 250, 250); fill-opacity: 0;"/>
        </g><g p:type="Shape" p:def="Evolus.BasicWebElements:pane-v2" id="152868585414434d9fc71d0e428dcb3f" transform="matrix(1,0,0,1,1,32.00000047683716)"><p:metadata><p:property name="radius">10,0</p:property><p:property name="textPadding">0,1.6666666666666667</p:property><p:property name="fixedRadius">false</p:property><p:property name="fillColor">#EEEEEEFF</p:property><p:property name="shadowStyle">0|0|3|1|#000000</p:property><p:property name="shadowColor">#00000000</p:property><p:property name="strokeColor">#CCCCCCFF</p:property><p:property name="strokeStyle">1|</p:property><p:property name="rightBorder">true</p:property><p:property name="bottomBorder">true</p:property><p:property name="textContent"/><p:property name="textFont">PencilUI|normal|normal|13px|none|0</p:property><p:property name="textColor">#000000FF</p:property><p:property name="textAlign">1,1</p:property><p:property name="box">898,50</p:property><p:property name="cornerStyle">none</p:property><p:property name="customStyle"/></p:metadata>
            <foreignObject x="-3" y="-3" width="904" height="56" p:name="htmlObject" id="61f601ed2468463e9dfb23b3a6c2498d" style="padding-top: 3px;">
                <div xmlns="http://www.w3.org/1999/xhtml" style="box-sizing: border-box; overflow: hidden; box-shadow: none; width: 898px; height: 50px; padding: 0px; background-color: rgb(238, 238, 238); x: 10px; border-radius: 0px; border: 1px solid rgb(204, 204, 204); margin-left: 3px;" p:name="div" id="dab7f0da253e4c17bac5b7b5662d2ed3">
                </div>
            </foreignObject>
            <foreignObject x="0" y="0" width="898" height="50" p:name="htmlObject2" id="e00596769cd942d292ff3792898eb272" style="font-family: PencilUI; font-size: 13px; font-weight: normal; font-style: normal; text-decoration: none; fill: rgb(0, 0, 0); fill-opacity: 1; color: rgb(0, 0, 0);">
                <div xmlns="http://www.w3.org/1999/xhtml" style="box-sizing: border-box; overflow: hidden; display: table-cell; width: 898px; height: 50px; padding: 0px; text-align: center; vertical-align: middle;" p:name="div2" id="207fae95520841dea344419726e425e8"><div></div></div>
            </foreignObject>
        </g><g p:type="Shape" p:def="Evolus.WindowsXP.Widgets:TabHeader" id="581a735ea86c4eb2bc93b4d1449ab903" transform="matrix(1,0,0,1,24.00000035762787,50.0000007301569)"><p:metadata><p:property name="box">80,30</p:property><p:property name="disabled">false</p:property><p:property name="tabTitle">Home</p:property><p:property name="textColor">#000000FF</p:property><p:property name="textFont">PencilUI|500|normal|13px|none|0</p:property><p:property name="textAlign">1,1</p:property></p:metadata>
            <g transform="translate(0.5, 2.5)">
                <path style="fill: rgb(245, 244, 234); stroke-width: 1px; fill-opacity: 1; stroke: rgb(145, 167, 180); stroke-opacity: 1;" p:name="rect" id="fdb35231be7647229ef7c6ee482d692e" d="M 0 29.5 L 0 5 C 0 0 0 0 5 0 L 73 0 C 78 0 78 0 78 5 L 78 29.5"/>
                <text x="33.415905" y="14.214675" style="text-align: left; text-anchor: start; dominant-baseline: central; fill: rgb(0, 0, 0); fill-opacity: 1; font-family: PencilUI; font-size: 13px; font-weight: 500; font-style: normal; text-decoration: none;" transform="translate(-11,1)" xml:space="preserve" p:name="text" id="1e89442621e244c6b2d2137c64a09089">Home</text>
            </g>
        </g><g p:type="Shape" p:def="Evolus.WindowsXP.Widgets:TabHeader" id="85b0b85e475b4b23b7bd04202d374da7" transform="matrix(1,0,0,1,117.00000174343586,50.0000007301569)"><p:metadata><p:property name="box">80,30</p:property><p:property name="disabled">false</p:property><p:property name="tabTitle">Report</p:property><p:property name="textColor">#000000FF</p:property><p:property name="textFont">PencilUI|500|normal|13px|none|0</p:property><p:property name="textAlign">1,1</p:property></p:metadata>
            <g transform="translate(0.5, 2.5)">
                <path style="fill: rgb(245, 244, 234); stroke-width: 1px; fill-opacity: 1; stroke: rgb(145, 167, 180); stroke-opacity: 1;" p:name="rect" id="8cf871c850f0403abca63bd821c65501" d="M 0 29.5 L 0 5 C 0 0 0 0 5 0 L 73 0 C 78 0 78 0 78 5 L 78 29.5"/>
                <text x="33.415905" y="14.214675" style="text-align: left; text-anchor: start; dominant-baseline: central; fill: rgb(0, 0, 0); fill-opacity: 1; font-family: PencilUI; font-size: 13px; font-weight: 500; font-style: normal; text-decoration: none;" transform="translate(-13,1)" xml:space="preserve" p:name="text" id="1083072a4b9b484fac082b3223ecb515">Report</text>
            </g>
        </g><g p:type="Shape" p:def="Evolus.WindowsXP.Widgets:TabHeader" id="f49fb79e65f749559d1e11db7ddd0a2d" transform="matrix(1,0,0,1,210.00000312924385,50.0000007301569)"><p:metadata><p:property name="box">100,30</p:property><p:property name="disabled">false</p:property><p:property name="tabTitle">Configuration</p:property><p:property name="textColor">#000000FF</p:property><p:property name="textFont">PencilUI|500|normal|13px|none|0</p:property><p:property name="textAlign">1,1</p:property></p:metadata>
            <g transform="translate(0.5, 2.5)">
                <path style="fill: rgb(245, 244, 234); stroke-width: 1px; fill-opacity: 1; stroke: rgb(145, 167, 180); stroke-opacity: 1;" p:name="rect" id="ce1ba676aea14ce991bb0fe465076d3e" d="M 0 29.5 L 0 5 C 0 0 0 0 5 0 L 93 0 C 98 0 98 0 98 5 L 98 29.5"/>
                <text x="33.415905" y="14.214675" style="text-align: left; text-anchor: start; dominant-baseline: central; fill: rgb(0, 0, 0); fill-opacity: 1; font-family: PencilUI; font-size: 13px; font-weight: 500; font-style: normal; text-decoration: none;" transform="translate(-23,1)" xml:space="preserve" p:name="text" id="d8f718038f634e3796f607be1227bcd8">Configuration</text>
            </g>
        </g><g p:type="Shape" p:def="Evolus.WindowsXP.Widgets:TabHeader" id="98d32bfc2af149f2beebbd7da2076cdb" transform="matrix(1,0,0,1,323.00000481307507,50.0000007301569)"><p:metadata><p:property name="box">80,30</p:property><p:property name="disabled">false</p:property><p:property name="tabTitle">History</p:property><p:property name="textColor">#000000FF</p:property><p:property name="textFont">PencilUI|500|normal|13px|none|0</p:property><p:property name="textAlign">1,1</p:property></p:metadata>
            <g transform="translate(0.5, 2.5)">
                <path style="fill: rgb(245, 244, 234); stroke-width: 1px; fill-opacity: 1; stroke: rgb(145, 167, 180); stroke-opacity: 1;" p:name="rect" id="6f496a713d9a43bbbf169b06329f109a" d="M 0 29.5 L 0 5 C 0 0 0 0 5 0 L 73 0 C 78 0 78 0 78 5 L 78 29.5"/>
                <text x="33.415905" y="14.214675" style="text-align: left; text-anchor: start; dominant-baseline: central; fill: rgb(0, 0, 0); fill-opacity: 1; font-family: PencilUI; font-size: 13px; font-weight: 500; font-style: normal; text-decoration: none;" transform="translate(-14,1)" xml:space="preserve" p:name="text" id="3ccb4dd33fb848eb9e1ee04f487989b3">History</text>
            </g>
        </g><g p:type="Shape" p:def="Evolus.BasicWebElements:pane-v2" id="6871bebd63864fedbf0ae992ce22ea7f" transform="matrix(1,0,0,1,1.0000000149011612,608.0000090450048)"><p:metadata><p:property name="radius">10,0</p:property><p:property name="textPadding">0,0.9999999999999999</p:property><p:property name="fixedRadius">false</p:property><p:property name="fillColor">#EEEEEEFF</p:property><p:property name="shadowStyle">0|0|3|1|#000000</p:property><p:property name="shadowColor">#00000000</p:property><p:property name="strokeColor">#CCCCCCFF</p:property><p:property name="strokeStyle">1|</p:property><p:property name="rightBorder">true</p:property><p:property name="bottomBorder">true</p:property><p:property name="textContent"/><p:property name="textFont">PencilUI|normal|normal|13px|none|0</p:property><p:property name="textColor">#000000FF</p:property><p:property name="textAlign">1,1</p:property><p:property name="box">898,30</p:property><p:property name="cornerStyle">none</p:property><p:property name="customStyle"/></p:metadata>
            <foreignObject x="-3" y="-3" width="904" height="36" p:name="htmlObject" id="23c09e5624034944bdb78591e2e1d2ee" style="padding-top: 3px;">
                <div xmlns="http://www.w3.org/1999/xhtml" style="box-sizing: border-box; overflow: hidden; box-shadow: none; width: 898px; height: 30px; padding: 0px; background-color: rgb(238, 238, 238); x: 10px; border-radius: 0px; border: 1px solid rgb(204, 204, 204); margin-left: 3px;" p:name="div" id="18f5dc3d3b164a1ea12ae143190b82ef">
                </div>
            </foreignObject>
            <foreignObject x="0" y="0" width="898" height="30" p:name="htmlObject2" id="333c00261060401687af47f6120931bd" style="font-family: PencilUI; font-size: 13px; font-weight: normal; font-style: normal; text-decoration: none; fill: rgb(0, 0, 0); fill-opacity: 1; color: rgb(0, 0, 0);">
                <div xmlns="http://www.w3.org/1999/xhtml" style="box-sizing: border-box; overflow: hidden; display: table-cell; width: 898px; height: 30px; padding: 0px; text-align: center; vertical-align: middle;" p:name="div2" id="5e148734bbc34be4a5c3df2e22d62812"><div></div></div>
            </foreignObject>
        </g><g p:type="Shape" p:def="Evolus.Prototype.GUI:frameControl" id="e5d72faaf0644a79a45f5a37854693c2" transform="matrix(1,0,0,1,790.0000000149012,0)"><p:metadata><p:property name="box">100,32</p:property><p:property name="withMinButton">true</p:property><p:property name="withRestoreButton">true</p:property><p:property name="withCloseButton">true</p:property><p:property name="disabled">false</p:property><p:property name="fillColor">#FAFAFAFF</p:property><p:property name="strokeColor">#666666FF</p:property><p:property name="strokeStyle">2|</p:property></p:metadata>
            <path style="fill: none; stroke: rgb(102, 102, 102); stroke-opacity: 1; stroke-width: 2;" p:name="button" id="aae6de62d9c147eab7ab63f99e4510ab" d="M 76 8 L 92 24 M 92 8 L 76 24 M 49 9 L 63 9 L 63 23 L 49 23 z M 21 23 L 35 23" transform="translate(2.5,0)"/>
            <path p:name="box" id="2ba4f2af80864552bba82a4c9278ba90" d="M 0 0 L 100 0 L 100 32 L 0 32 z" style="fill: rgb(250, 250, 250); fill-opacity: 0;"/>
        </g></g></g><g inkscape:label="Configuration Page" inkscape:groupmode="layer" id="layer_configuration_page"><g><rect x="0" y="0" width="900" height="639" fill="none"/><g p:type="Shape" p:def="Evolus.WindowsXP.Widgets:Groupbox" id="5127d0fc07f9444f8f7026dfe26b3fda" transform="matrix(1,0,0,1,206.00000302493572,136.00000202655792)"><p:metadata><p:property name="box">451,380</p:property><p:property name="label"/><p:property name="textColor">#000000FF</p:property><p:property name="textFont">PencilUI|500|normal|10px|none|0</p:property></p:metadata>
            <path d="M 17.5,0.5 L 446.5,0.5 C 449,0.5 450.5,2.2946114 450.5,4.5 L 450.5,375.5 C 450.5,378 449,379.5 446.5,379.5 L 4.5,379.5 C 2.284,379.5 0.5,378 0.5,375.5 L 0.5,4.5106114 C 0.5,2.2946114 2.284,0.51061056 4.5,0.51061056 L 5.5,0.51061056" style="color:#000000;fill:#ffffff;fill-opacity:0;fill-rule:nonzero;stroke:#aca899;stroke-width:1.00000012;stroke-linecap:butt;stroke-linejoin:miter;marker:none;marker-start:none;marker-mid:none;marker-end:none;stroke-miterlimit:4;stroke-dashoffset:0;stroke-opacity:1;visibility:visible;display:inline;overflow:visible;enable-background:accumulate" p:name="rect" id="9faf76034104428b8be9a51d1ae40b25"/>
            <text y="9.0077095" x="17.999992" style="font-size: 10px; font-style: normal; font-weight: 500; text-align: left; text-anchor: start; dominant-baseline: central; fill: rgb(0, 0, 0); font-family: PencilUI; fill-opacity: 1; text-decoration: none;" xml:space="preserve" p:name="text" id="8f455869d983467c9f87fc77c7636dd9" transform="translate(-8,-9)"> </text>
        </g><g p:type="Shape" p:def="Evolus.WindowsXP.Widgets:Button" id="28eeb09804d443bda99f9a63b0cd2180" transform="matrix(1,0,0,1,567.0000084489584,529.0000078827143)"><p:metadata><p:property name="box">80,25</p:property><p:property name="buttonText">Save</p:property><p:property name="textColor">#000000FF</p:property><p:property name="textFont">Tahoma|normal|normal|11px|none|0</p:property><p:property name="textAlign">1,1</p:property><p:property name="disabled">false</p:property><p:property name="defaultButton">false</p:property></p:metadata>
            <defs p:name="defs2177" id="cc67952b0a2f4784868c1facf76f0c22">
                <linearGradient x1="0%" y1="0%" x2="0%" y2="100%" p:name="linearGradient4109" id="90f3ba893ba3420fa40810e7443cbdd7">
                  <stop style="stop-color:#ffffff;stop-opacity:1" offset="0%" p:name="stop1" id="b3e7ebe09b8346b3b2bad78b4bd340b5"/>
                  <stop style="stop-color:#f0f0ea;stop-opacity:1" offset="72%" p:name="stop2" id="c92c6437b1ec434989f1e10e3af7b2b9"/>
                  <stop style="stop-color:#d6d0c5;stop-opacity:1" offset="100%" p:name="stop3" id="67d0135776024c6d88a948b643b64d9c"/>
                </linearGradient>
            </defs>
            <rect width="79" height="24" rx="3" ry="3" x="0.5" y="0.5" style="opacity:1;color:#000000;fill:url(#90f3ba893ba3420fa40810e7443cbdd7);fill-opacity:1;fill-rule:nonzero;stroke:#003c74;stroke-width:1;stroke-linecap:square;stroke-linejoin:miter;marker:none;marker-start:none;marker-mid:none;marker-end:none;stroke-miterlimit:4;stroke-dasharray:none;stroke-dashoffset:0;stroke-opacity:1;visibility:visible;display:inline;overflow:visible;enable-background:accumulate" p:name="rect" id="600f8e081b7f4c2c82937e75774ac88d"/>
            <rect width="76" height="21" rx="2" ry="2" x="2" y="2" style="opacity: 1; color: rgb(0, 0, 0); fill: none; fill-opacity: 1; fill-rule: nonzero; stroke: rgb(125, 164, 228); stroke-width: 2; stroke-linecap: square; stroke-linejoin: miter; marker: none; stroke-miterlimit: 4; stroke-dasharray: none; stroke-dashoffset: 0; stroke-opacity: 1; visibility: hidden; display: none; overflow: visible;" p:name="defaultMarker" id="0fadcfec00a34c2e8f154db49cf8438c"/>
            <text x="33.415905" y="14.214675" style="text-align: left; text-anchor: start; dominant-baseline: central; fill: rgb(0, 0, 0); fill-opacity: 1; font-family: Tahoma; font-size: 11px; font-weight: normal; font-style: normal; text-decoration: none;" xml:space="preserve" p:name="text" id="8a531431b24345ffa516ffeab351ba5b" transform="translate(-5,-2)">Save</text>
            <path style="fill: none; stroke-width: 1px; visibility: hidden;" p:name="accelIndicator" id="69699e32718e4adba1b9205711fb757e"/>
        </g><g p:type="Shape" p:def="Evolus.Sketchy.GUI:lineV2" p:sc="Horizontal Line" id="722ee9e6666141a3b09b18e5d81e88ee" transform="matrix(1,0,0,1,211.99999995529652,55)"><p:metadata><p:property name="startLine">0,0</p:property><p:property name="endLine">95,0</p:property><p:property name="mode">horizontal</p:property><p:property name="strokeColor">#FF0000FF</p:property><p:property name="strokeStyle">2|</p:property></p:metadata>
            <g p:name="rect" id="b6c0026263a744768209acbc841d4832" transform="translate(0,0)" style="stroke: rgb(255, 0, 0); stroke-opacity: 1; stroke-width: 2;">
    			<path style="stroke: #FFFFFF; stroke-opacity: 0; stroke-width: 8px; fill: none;" p:name="bg" id="4db1f4bf804249718d2c1c3a92884b67" transform="translate(0,0)" d="M 0 0 C 32 0 63 0 95 0"/>
    			<path style="stroke-linejoin: round; fill: none;" p:name="line1" id="735bf0cb1f824328b051e5da040e1d09" transform="translate(0,0)" d="M 0 0 C 32 1 63 1 95 0"/>
    		</g>
        </g><g p:type="Shape" p:def="Evolus.WindowsXP.Widgets:Seprator" id="ca295efbdd6a4589b542f4b3bccd3c7a" transform="matrix(1,0,0,1,224.0000033378601,168.00000250339505)"><p:metadata><p:property name="box">200,10</p:property><p:property name="type">normal</p:property></p:metadata>
            <rect style="fill: #000000; fill-opacity: 0; stroke: none;" x="0" y="0" p:name="bgRect" id="f160af2b9bbe424c9a440a7a06aefa35" width="200" height="10"/>
            <path style="fill:none; stroke: #ACA899; stroke-width: 1px;" d="M 0 5 L 200 5" transform="translate(0.5,0.5)" p:name="line1" id="63e4df8ab22f4f6395500a751a65f303"/>
            <path style="fill: none; stroke: rgb(255, 255, 255); stroke-width: 1px; visibility: visible;" d="M 0 6 L 200 6" transform="translate(0.5,0.5)" p:name="line2" id="e12c3aa5bcb44de2803386931612d46d"/>
        </g><g p:type="Shape" p:def="Evolus.WindowsXP.Widgets:Groupbox" id="3b6d132009254a3594c8595877e5e0b8" transform="matrix(1,0,0,1,250.00000371038914,381.0000056773424)"><p:metadata><p:property name="box">364,80</p:property><p:property name="label">Option 2</p:property><p:property name="textColor">#000000FF</p:property><p:property name="textFont">Tahoma|normal|normal|11px|none|0</p:property></p:metadata>
            <path d="M 56.999610900878906,0.5 L 359.5,0.5 C 362,0.5 363.5,2.2946114 363.5,4.5 L 363.5,75.5 C 363.5,78 362,79.5 359.5,79.5 L 4.5,79.5 C 2.284,79.5 0.5,78 0.5,75.5 L 0.5,4.5106114 C 0.5,2.2946114 2.284,0.51061056 4.5,0.51061056 L 5.5,0.51061056" style="color:#000000;fill:#ffffff;fill-opacity:0;fill-rule:nonzero;stroke:#aca899;stroke-width:1.00000012;stroke-linecap:butt;stroke-linejoin:miter;marker:none;marker-start:none;marker-mid:none;marker-end:none;stroke-miterlimit:4;stroke-dashoffset:0;stroke-opacity:1;visibility:visible;display:inline;overflow:visible;enable-background:accumulate" p:name="rect" id="479721f15f7e4f3798a6a1edc99d3350"/>
            <text y="9.0077095" x="17.999992" style="font-size: 11px; font-style: normal; font-weight: normal; text-align: left; text-anchor: start; dominant-baseline: central; fill: rgb(0, 0, 0); font-family: Tahoma; fill-opacity: 1; text-decoration: none;" xml:space="preserve" p:name="text" id="8362f55c943a4127b575eaff6c4a1a56" transform="translate(-8,-9)">Option 2</text>
        </g><g p:type="Shape" p:def="Evolus.WindowsXP.Widgets:Label" id="6ce949d59bbb4c32b6eef04626f520dd" transform="matrix(1,0,0,1,227.99999998509884,156)"><p:metadata><p:property name="disabled">false</p:property><p:property name="label">Scan Record Name Format</p:property><p:property name="textColor">#000000FF</p:property><p:property name="textFont">PencilUI|500|normal|10px|none|0</p:property></p:metadata>
              <text y="9.0077095" x="17.999992" style="font-size: 10px; font-style: normal; font-weight: 500; text-align: left; text-anchor: start; dominant-baseline: central; fill: rgb(0, 0, 0); font-family: PencilUI; fill-opacity: 1; text-decoration: none;" xml:space="preserve" p:name="text" id="bd74f162c69d4141ae0f9e0dcd1816c7" transform="translate(-18,-3)">Scan Record Name Format</text>
             <path style="fill: none; stroke-width: 1px; visibility: hidden;" p:name="accelIndicator" id="e4f4badd3f7449209c6e84cc0434e15f"/>
        </g><g p:type="Shape" p:def="Evolus.WindowsXP.Widgets:Seprator" id="05bfa0a2d1cd40a7992d9d449f699a2e" transform="matrix(1,0,0,1,224.0000033378601,262.00000390410423)"><p:metadata><p:property name="box">200,10</p:property><p:property name="type">normal</p:property></p:metadata>
            <rect style="fill: #000000; fill-opacity: 0; stroke: none;" x="0" y="0" p:name="bgRect" id="cb78ebdd8a3a493d9a1508fb1becae48" width="200" height="10"/>
            <path style="fill:none; stroke: #ACA899; stroke-width: 1px;" d="M 0 5 L 200 5" transform="translate(0.5,0.5)" p:name="line1" id="07b975e5f5574ef0abb5d8808d3154bf"/>
            <path style="fill: none; stroke: rgb(255, 255, 255); stroke-width: 1px; visibility: visible;" d="M 0 6 L 200 6" transform="translate(0.5,0.5)" p:name="line2" id="ab890f4f33f84c749b2d7b64c653cad4"/>
        </g><g p:type="Shape" p:def="Evolus.WindowsXP.Widgets:Label" id="d30210b14550442e9a47718f2f1f4f86" transform="matrix(1,0,0,1,227.99999998509884,251.99999999999997)"><p:metadata><p:property name="disabled">false</p:property><p:property name="label">Image Index 2 Analysis</p:property><p:property name="textColor">#000000FF</p:property><p:property name="textFont">PencilUI|500|normal|10px|none|0</p:property></p:metadata>
              <text y="9.0077095" x="17.999992" style="font-size: 10px; font-style: normal; font-weight: 500; text-align: left; text-anchor: start; dominant-baseline: central; fill: rgb(0, 0, 0); font-family: PencilUI; fill-opacity: 1; text-decoration: none;" xml:space="preserve" p:name="text" id="587ff92743d64bdf9eb03e8033cc2abd" transform="translate(-18,-3)">Image Index 2 Analysis</text>
             <path style="fill: none; stroke-width: 1px; visibility: hidden;" p:name="accelIndicator" id="6fa7fc7eebad4a789cfd5bec321cf035"/>
        </g><g p:type="Shape" p:def="Evolus.WindowsXP.Widgets:Groupbox" id="3b7b2a66b6404d43b34da498b3b2c8e9" transform="matrix(1,0,0,1,250.0000037252903,287.00000427663326)"><p:metadata><p:property name="box">364,80</p:property><p:property name="label">Option 1</p:property><p:property name="textColor">#000000FF</p:property><p:property name="textFont">Tahoma|normal|normal|11px|none|0</p:property></p:metadata>
            <path d="M 56.61249923706055,0.5 L 359.5,0.5 C 362,0.5 363.5,2.2946114 363.5,4.5 L 363.5,75.5 C 363.5,78 362,79.5 359.5,79.5 L 4.5,79.5 C 2.284,79.5 0.5,78 0.5,75.5 L 0.5,4.5106114 C 0.5,2.2946114 2.284,0.51061056 4.5,0.51061056 L 5.5,0.51061056" style="color:#000000;fill:#ffffff;fill-opacity:0;fill-rule:nonzero;stroke:#aca899;stroke-width:1.00000012;stroke-linecap:butt;stroke-linejoin:miter;marker:none;marker-start:none;marker-mid:none;marker-end:none;stroke-miterlimit:4;stroke-dashoffset:0;stroke-opacity:1;visibility:visible;display:inline;overflow:visible;enable-background:accumulate" p:name="rect" id="e52e6bc5f2624e2d9bb9f9f70cc54f8a"/>
            <text y="9.0077095" x="17.999992" style="font-size: 11px; font-style: normal; font-weight: normal; text-align: left; text-anchor: start; dominant-baseline: central; fill: rgb(0, 0, 0); font-family: Tahoma; fill-opacity: 1; text-decoration: none;" xml:space="preserve" p:name="text" id="2108808e5431489bb92dfda4c7e66387" transform="translate(-8,-9)">Option 1</text>
        </g><g p:type="Shape" p:def="Evolus.WindowsXP.Widgets:Radio" id="561c65c5f7ac4a449851d3e4c5e227b4" transform="matrix(1,0,0,1,275.00000408291817,404.0000060200691)"><p:metadata><p:property name="disabled">false</p:property><p:property name="selected">false</p:property><p:property name="label">中间1张图片</p:property><p:property name="textColor">#000000FF</p:property><p:property name="textFont">Tahoma|normal|normal|11px|none|0</p:property></p:metadata>
        <defs p:name="defs2659" id="946c0f2391dc4fc7b83b8db11150703b">
            <linearGradient p:name="linearGradient31435" id="22d672153a6645e58b40e65aef83cead">
              <stop style="stop-color:#bab4a7;stop-opacity:1" offset="0" p:name="stop31437" id="0b72be00057345c981043d4076ecab5d"/>
              <stop style="stop-color:#bab4a8;stop-opacity:1" offset="0.30131003" p:name="stop31439" id="bba0edf1e085486b93a2f578056a0cb8"/>
              <stop style="stop-color:#d6cec0;stop-opacity:1" offset="1" p:name="stop31441" id="ba3a1f6850ba4315b1b823575656108b"/>
            </linearGradient>
            <linearGradient x1="503" y1="106" x2="503" y2="86" xlink:href="#22d672153a6645e58b40e65aef83cead" gradientUnits="userSpaceOnUse" gradientTransform="translate(-381.00001,-85.899192)" p:name="linearGradient55484" id="e01dd37d60a3412da0b8392aa1b33767"/>
            <linearGradient p:name="linearGradient2237" id="fb0713daac354d2696e7b2e9580cb14e">
              <stop style="stop-color:#0080c3;stop-opacity:1" offset="0" p:name="stop2239" id="7422edaf1b1448dbb66c865710c2a72a"/>
              <stop style="stop-color:#28a2e1;stop-opacity:1" offset="0.57124019" p:name="stop2268" id="248afb60431540c2838e75878556658d"/>
              <stop style="stop-color:#3cb3f0;stop-opacity:1" offset="0.74453777" p:name="stop2270" id="81e44e642a3c468eb12dfddbe2bca084"/>
              <stop style="stop-color:#51c4ff;stop-opacity:1" offset="1" p:name="stop2241" id="85e8cfdfbd3b48b3b0415171dc25a753"/>
            </linearGradient>
            <linearGradient x1="439.5" y1="104" x2="439.5" y2="88" xlink:href="#fb0713daac354d2696e7b2e9580cb14e" gradientUnits="userSpaceOnUse" gradientTransform="translate(-381.00001,-85.899192)" p:name="linearGradient57450" id="17076a0b6cfb4064942db8a1692c657b"/>
          </defs>
          <path transform="matrix(0.9362048,0,0,0.9362048,-111.82866,-312.21823)" d="M 131.75 339.875 A 5.875 5.875 0 1 1  120,339.875 A 5.875 5.875 0 1 1  131.75 339.875 z" style="opacity: 1; color: rgb(0, 0, 0); fill: rgb(255, 255, 255); fill-opacity: 1; fill-rule: nonzero; stroke: rgb(35, 86, 130); stroke-width: 1.06771; stroke-linecap: round; stroke-linejoin: round; marker: none; stroke-miterlimit: 4; stroke-dasharray: none; stroke-dashoffset: 0; stroke-opacity: 1; visibility: visible; display: inline; overflow: visible;" p:name="outerPath" id="3d3f2fbee4a94008936c82133288eed0"/>
          <text xml:space="preserve" style="font-size: 11px; font-style: normal; font-weight: normal; text-align: left; text-anchor: start; dominant-baseline: central; fill: rgb(0, 0, 0); font-family: Tahoma; fill-opacity: 1; text-decoration: none;" x="18.016193" y="8.9744911" p:name="text" id="4799b38a51a34042b7ab19261ce0c83a" transform="translate(0,-3)">中间1张图片</text>
          <path d="M 3.5201411,9.8478598 C 1.4655909,8.4017026 0.93900042,5.5125628 2.3446111,3.3988028 C 3.7503111,1.2850728 6.5585511,0.74328308 8.6131111,2.1894328 C 9.2297411,2.6234428 9.6679911,3.1221828 10.025751,3.7969628" style="opacity: 0.273632; color: rgb(0, 0, 0); fill: none; fill-opacity: 1; fill-rule: nonzero; stroke: rgb(109, 109, 109); stroke-width: 1; stroke-linecap: round; stroke-linejoin: round; marker: none; stroke-miterlimit: 4; stroke-dashoffset: 0; stroke-opacity: 1; visibility: visible; overflow: visible;" p:name="shadowPath" id="0d6e98a84ecf41b8b2c6e7f78b5beda0"/>
          <path style="opacity: 1; color: rgb(0, 0, 0); fill: rgb(56, 185, 53); fill-opacity: 1; fill-rule: nonzero; stroke: none; stroke-width: 1; stroke-linecap: round; stroke-linejoin: round; marker: none; stroke-miterlimit: 4; stroke-dasharray: none; stroke-dashoffset: 0; stroke-opacity: 1; visibility: hidden; display: none; overflow: visible;" d="M 128.5 363.75 A 2.5 2.75 0 1 1  123.5,363.75 A 2.5 2.75 0 1 1  128.5 363.75 z" transform="matrix(1.2,0,0,1.0909092,-145.18388,-390.84385)" p:name="bullet" id="82ca80291ecb42b3a2cbabde4e75b01c"/>
        </g><g p:type="Shape" p:def="Evolus.WindowsXP.Widgets:Radio" id="139f3637a63d44a38d25efdd44a93524" transform="matrix(1,0,0,1,275.00000408291817,425.0000063329935)"><p:metadata><p:property name="disabled">false</p:property><p:property name="selected">false</p:property><p:property name="label">中间2张图片</p:property><p:property name="textColor">#000000FF</p:property><p:property name="textFont">Tahoma|normal|normal|11px|none|0</p:property></p:metadata>
        <defs p:name="defs2659" id="b4f5eb3c259845188515cdb74abcefc5">
            <linearGradient p:name="linearGradient31435" id="4963e2bd9a2545abac16cf601decbdaa">
              <stop style="stop-color:#bab4a7;stop-opacity:1" offset="0" p:name="stop31437" id="c7574658de08473ba12d04d152f6bb3a"/>
              <stop style="stop-color:#bab4a8;stop-opacity:1" offset="0.30131003" p:name="stop31439" id="29a1ff58b3844d9ebddc7619e521c9a3"/>
              <stop style="stop-color:#d6cec0;stop-opacity:1" offset="1" p:name="stop31441" id="9b28b2733cf34ccfb3860838bbf6306a"/>
            </linearGradient>
            <linearGradient x1="503" y1="106" x2="503" y2="86" xlink:href="#4963e2bd9a2545abac16cf601decbdaa" gradientUnits="userSpaceOnUse" gradientTransform="translate(-381.00001,-85.899192)" p:name="linearGradient55484" id="9885632ad07048949bb0d48c77285a4f"/>
            <linearGradient p:name="linearGradient2237" id="b16732c75fd641aea022b818e4cfe3cb">
              <stop style="stop-color:#0080c3;stop-opacity:1" offset="0" p:name="stop2239" id="f8a7ab91a9d24e64b76fcd64adafbe28"/>
              <stop style="stop-color:#28a2e1;stop-opacity:1" offset="0.57124019" p:name="stop2268" id="c8fe92c8ea894a9f9366b169b50ccc4b"/>
              <stop style="stop-color:#3cb3f0;stop-opacity:1" offset="0.74453777" p:name="stop2270" id="d2b28ab1f313487eb108fa86fb66f42a"/>
              <stop style="stop-color:#51c4ff;stop-opacity:1" offset="1" p:name="stop2241" id="2450a2157ca4410786fb430025a13f3f"/>
            </linearGradient>
            <linearGradient x1="439.5" y1="104" x2="439.5" y2="88" xlink:href="#b16732c75fd641aea022b818e4cfe3cb" gradientUnits="userSpaceOnUse" gradientTransform="translate(-381.00001,-85.899192)" p:name="linearGradient57450" id="5582cbfb53ce4b3695fc1a7f77addffb"/>
          </defs>
          <path transform="matrix(0.9362048,0,0,0.9362048,-111.82866,-312.21823)" d="M 131.75 339.875 A 5.875 5.875 0 1 1  120,339.875 A 5.875 5.875 0 1 1  131.75 339.875 z" style="opacity: 1; color: rgb(0, 0, 0); fill: rgb(255, 255, 255); fill-opacity: 1; fill-rule: nonzero; stroke: rgb(35, 86, 130); stroke-width: 1.06771; stroke-linecap: round; stroke-linejoin: round; marker: none; stroke-miterlimit: 4; stroke-dasharray: none; stroke-dashoffset: 0; stroke-opacity: 1; visibility: visible; display: inline; overflow: visible;" p:name="outerPath" id="808b86e05c014a1b9a3523515abfb5f9"/>
          <text xml:space="preserve" style="font-size: 11px; font-style: normal; font-weight: normal; text-align: left; text-anchor: start; dominant-baseline: central; fill: rgb(0, 0, 0); font-family: Tahoma; fill-opacity: 1; text-decoration: none;" x="18.016193" y="8.9744911" p:name="text" id="d8f4d86ca3dc4ebeb8ef04084ee3d463" transform="translate(0,-3)">中间2张图片</text>
          <path d="M 3.5201411,9.8478598 C 1.4655909,8.4017026 0.93900042,5.5125628 2.3446111,3.3988028 C 3.7503111,1.2850728 6.5585511,0.74328308 8.6131111,2.1894328 C 9.2297411,2.6234428 9.6679911,3.1221828 10.025751,3.7969628" style="opacity: 0.273632; color: rgb(0, 0, 0); fill: none; fill-opacity: 1; fill-rule: nonzero; stroke: rgb(109, 109, 109); stroke-width: 1; stroke-linecap: round; stroke-linejoin: round; marker: none; stroke-miterlimit: 4; stroke-dashoffset: 0; stroke-opacity: 1; visibility: visible; overflow: visible;" p:name="shadowPath" id="bc63f38c640b4b7899aadad3281500b9"/>
          <path style="opacity: 1; color: rgb(0, 0, 0); fill: rgb(56, 185, 53); fill-opacity: 1; fill-rule: nonzero; stroke: none; stroke-width: 1; stroke-linecap: round; stroke-linejoin: round; marker: none; stroke-miterlimit: 4; stroke-dasharray: none; stroke-dashoffset: 0; stroke-opacity: 1; visibility: hidden; display: none; overflow: visible;" d="M 128.5 363.75 A 2.5 2.75 0 1 1  123.5,363.75 A 2.5 2.75 0 1 1  128.5 363.75 z" transform="matrix(1.2,0,0,1.0909092,-145.18388,-390.84385)" p:name="bullet" id="05be1dd38faf4e0dacb8b740be3d8c6a"/>
        </g><g p:type="Shape" p:def="Evolus.WindowsXP.Widgets:Combo" id="f29a1b926745454a98dde251721216b7" transform="matrix(1,0,0,1,275.0000040978193,325.0000048428774)"><p:metadata><p:property name="box">60,25</p:property><p:property name="buttonText">1</p:property><p:property name="textColor">#000000FF</p:property><p:property name="textFont">Tahoma|normal|normal|11px|none|0</p:property><p:property name="textAlign">0,1</p:property><p:property name="disabled">false</p:property><p:property name="focused">false</p:property></p:metadata>

          <defs p:name="defs2177" id="8d94f65502314773844c337c0bc505fc">
            <linearGradient x1="0%" y1="0%" x2="100%" y2="100%" p:name="linearGradient4129" id="53e1197e031344b6aaae872e905b0d70">
              <stop style="stop-color:#e7eeff;stop-opacity:1" offset="0" p:name="stop1" id="7cef2ff9910a4f7d810503a278f9d6d4"/>
              <stop style="stop-color:#aec8f7;stop-opacity:1" offset="1" p:name="stop2" id="50e6a984e7954b1b909635399f75715e"/>
            </linearGradient>
          </defs>
            <rect width="59" height="24" x="0.5" y="0.5" style="opacity: 1; color: rgb(0, 0, 0); fill: rgb(255, 255, 255); fill-opacity: 1; fill-rule: nonzero; stroke: none;" p:name="bgRect1" id="e78aad2ce7b3478ba7bffabf99e5960a"/>
            <rect width="20" height="25" x="40" y="0" style="opacity:1;color:#000000;fill:#ffffff;fill-opacity:1;fill-rule:nonzero;stroke: none;" p:name="bgRect2" id="f017906d3c21454dad2efd281e4b5b03"/>
            <rect width="59" height="24" x="0.5" y="0.5" style="opacity:1;color:#000000;fill:none;fill-opacity:1;fill-rule:nonzero;stroke:#7f9db9;stroke-width:1;stroke-linecap:square;stroke-linejoin:miter;marker:none;marker-start:none;marker-mid:none;marker-end:none;stroke-miterlimit:4;stroke-dasharray:none;stroke-dashoffset:0;stroke-opacity:1;visibility:visible;display:inline;overflow:visible;enable-background:accumulate" p:name="rect" id="58257390fad94baeb302e4af55953239"/>
            <rect width="16" height="20" rx="1" ry="1" x="41.5" y="2.5" style="opacity:1;color:#000000;fill:url(#53e1197e031344b6aaae872e905b0d70);fill-opacity:1;fill-rule:nonzero;stroke:#c3d3f8;stroke-width:0.96954674;stroke-linecap:square;stroke-linejoin:miter;marker:none;marker-start:none;marker-mid:none;marker-end:none;stroke-miterlimit:4;stroke-dasharray:none;stroke-dashoffset:0;stroke-opacity:1;visibility:visible;display:inline;overflow:visible;enable-background:accumulate" p:name="dropRect" id="79ca4516a20844efb80ed1fe92f0121e"/>
            <path d="M 53.5,10.5 L 49.5,14.5 L 45.5,10.5" style="color:#000000;fill:none;fill-opacity:1;fill-rule:nonzero;stroke:#4d6185;stroke-width:1.99999988;stroke-linecap:butt;stroke-linejoin:miter;marker:none;marker-start:none;marker-mid:none;marker-end:none;stroke-miterlimit:4;stroke-dashoffset:0;stroke-opacity:1;visibility:visible;display:inline;overflow:visible;enable-background:accumulate" p:name="marker" id="5ade76b59bff4c95b11f5e7a0ce7d309"/>
            <rect width="36" height="20" x="2.5" y="2.5" style="opacity: 1; color: rgb(0, 0, 0); fill: rgb(49, 106, 197); fill-opacity: 1; fill-rule: nonzero; stroke: rgb(255, 255, 255); stroke-dasharray: 1, 1; visibility: hidden; display: none;" p:name="focusRect" id="1702c9faf11c42e2af972a270232cf58"/>
            <text x="4.561419" y="14.175718" style="text-align: left; text-anchor: start; dominant-baseline: central; fill: rgb(0, 0, 0); fill-opacity: 1; font-family: Tahoma; font-size: 11px; font-weight: normal; font-style: normal; text-decoration: none;" xml:space="preserve" p:name="text" id="11a48364337e4d15809f52366b20f657" transform="translate(0,-2)">1</text>
          </g><g p:type="Shape" p:def="Evolus.WindowsXP.Widgets:Combo" id="846748b5a6a64b27b4a4be6749a3739d" transform="matrix(1,0,0,1,357.00000531971455,325.0000048428774)"><p:metadata><p:property name="box">60,25</p:property><p:property name="buttonText">2</p:property><p:property name="textColor">#000000FF</p:property><p:property name="textFont">Tahoma|normal|normal|11px|none|0</p:property><p:property name="textAlign">0,1</p:property><p:property name="disabled">false</p:property><p:property name="focused">false</p:property></p:metadata>

          <defs p:name="defs2177" id="6a7fd876bc2c4eec9fc3ed573450632a">
            <linearGradient x1="0%" y1="0%" x2="100%" y2="100%" p:name="linearGradient4129" id="42237a8a4c644a9497d31e30aa02e9c7">
              <stop style="stop-color:#e7eeff;stop-opacity:1" offset="0" p:name="stop1" id="fd3ab920d2234861a6c5de3a1eb48f4c"/>
              <stop style="stop-color:#aec8f7;stop-opacity:1" offset="1" p:name="stop2" id="306d95242ed74477b48fc059ca566b6f"/>
            </linearGradient>
          </defs>
            <rect width="59" height="24" x="0.5" y="0.5" style="opacity: 1; color: rgb(0, 0, 0); fill: rgb(255, 255, 255); fill-opacity: 1; fill-rule: nonzero; stroke: none;" p:name="bgRect1" id="6de6dcea7eae438390588247ee688a03"/>
            <rect width="20" height="25" x="40" y="0" style="opacity:1;color:#000000;fill:#ffffff;fill-opacity:1;fill-rule:nonzero;stroke: none;" p:name="bgRect2" id="378d7d7f5220451aaafe51a4323480b9"/>
            <rect width="59" height="24" x="0.5" y="0.5" style="opacity:1;color:#000000;fill:none;fill-opacity:1;fill-rule:nonzero;stroke:#7f9db9;stroke-width:1;stroke-linecap:square;stroke-linejoin:miter;marker:none;marker-start:none;marker-mid:none;marker-end:none;stroke-miterlimit:4;stroke-dasharray:none;stroke-dashoffset:0;stroke-opacity:1;visibility:visible;display:inline;overflow:visible;enable-background:accumulate" p:name="rect" id="6e3859e2829d4ec28a2f5cfd807ef750"/>
            <rect width="16" height="20" rx="1" ry="1" x="41.5" y="2.5" style="opacity:1;color:#000000;fill:url(#42237a8a4c644a9497d31e30aa02e9c7);fill-opacity:1;fill-rule:nonzero;stroke:#c3d3f8;stroke-width:0.96954674;stroke-linecap:square;stroke-linejoin:miter;marker:none;marker-start:none;marker-mid:none;marker-end:none;stroke-miterlimit:4;stroke-dasharray:none;stroke-dashoffset:0;stroke-opacity:1;visibility:visible;display:inline;overflow:visible;enable-background:accumulate" p:name="dropRect" id="ed381d58d3134941ba891bb402024ea6"/>
            <path d="M 53.5,10.5 L 49.5,14.5 L 45.5,10.5" style="color:#000000;fill:none;fill-opacity:1;fill-rule:nonzero;stroke:#4d6185;stroke-width:1.99999988;stroke-linecap:butt;stroke-linejoin:miter;marker:none;marker-start:none;marker-mid:none;marker-end:none;stroke-miterlimit:4;stroke-dashoffset:0;stroke-opacity:1;visibility:visible;display:inline;overflow:visible;enable-background:accumulate" p:name="marker" id="7c6a88dae822441ebde6aacb9e7b7e1b"/>
            <rect width="36" height="20" x="2.5" y="2.5" style="opacity: 1; color: rgb(0, 0, 0); fill: rgb(49, 106, 197); fill-opacity: 1; fill-rule: nonzero; stroke: rgb(255, 255, 255); stroke-dasharray: 1, 1; visibility: hidden; display: none;" p:name="focusRect" id="58d8ed017a4446e6a642e55c3979fd5d"/>
            <text x="4.561419" y="14.175718" style="text-align: left; text-anchor: start; dominant-baseline: central; fill: rgb(0, 0, 0); fill-opacity: 1; font-family: Tahoma; font-size: 11px; font-weight: normal; font-style: normal; text-decoration: none;" xml:space="preserve" p:name="text" id="5abc6b3661024806931b044f30c03efb" transform="translate(0,-2)">2</text>
          </g><g p:type="Shape" p:def="Evolus.WindowsXP.Widgets:Label" id="5fbb635df8954f2e82b5cba323f0f728" transform="matrix(1,0,0,1,276.0000041127205,310.00000461935997)"><p:metadata><p:property name="disabled">false</p:property><p:property name="label">From</p:property><p:property name="textColor">#000000FF</p:property><p:property name="textFont">Tahoma|normal|normal|11px|none|0</p:property></p:metadata>
              <text y="9.0077095" x="17.999992" style="font-size: 11px; font-style: normal; font-weight: normal; text-align: left; text-anchor: start; dominant-baseline: central; fill: rgb(0, 0, 0); font-family: Tahoma; fill-opacity: 1; text-decoration: none;" xml:space="preserve" p:name="text" id="3b2937841eb14ae9b25224079de65eef" transform="translate(-18,-3)">From</text>
             <path style="fill: none; stroke-width: 1px; visibility: hidden;" p:name="accelIndicator" id="a6c9e342d9054a8c91850517bbdcbeb7"/>
        </g><g p:type="Shape" p:def="Evolus.WindowsXP.Widgets:Label" id="f41e0e7a524b42ec9955ff97fadde2e9" transform="matrix(1,0,0,1,357.00000531971455,310.00000461935997)"><p:metadata><p:property name="disabled">false</p:property><p:property name="label">to</p:property><p:property name="textColor">#000000FF</p:property><p:property name="textFont">Tahoma|normal|normal|11px|none|0</p:property></p:metadata>
              <text y="9.0077095" x="17.999992" style="font-size: 11px; font-style: normal; font-weight: normal; text-align: left; text-anchor: start; dominant-baseline: central; fill: rgb(0, 0, 0); font-family: Tahoma; fill-opacity: 1; text-decoration: none;" xml:space="preserve" p:name="text" id="be56ddbc22ec4c69bb88e89ba4b94a64" transform="translate(-17,-3)">to</text>
             <path style="fill: none; stroke-width: 1px; visibility: hidden;" p:name="accelIndicator" id="b1d1275385964f5fbf6547e844b0bc46"/>
        </g><g p:type="Shape" p:def="Evolus.WindowsXP.Widgets:TextBox" id="49fb6c837d2c4f76b36e68a87352162a" transform="matrix(1,0,0,1,276,191.99999999999997)"><p:metadata><p:property name="box">141,30</p:property><p:property name="textContent">QA3-LCD-00*</p:property><p:property name="textColor">#000000FF</p:property><p:property name="textFont">PencilUI|500|normal|10px|none|0</p:property><p:property name="textAlign">0,1</p:property><p:property name="status">normal</p:property><p:property name="disabled">false</p:property></p:metadata>

            <rect width="140" height="29" x="0.5" y="0.5" style="opacity: 1; color: rgb(0, 0, 0); fill: rgb(255, 255, 255); fill-opacity: 1; fill-rule: nonzero; stroke: rgb(127, 157, 185); stroke-width: 1; stroke-linecap: square; stroke-linejoin: miter; marker: none; stroke-miterlimit: 4; stroke-dasharray: none; stroke-dashoffset: 0; stroke-opacity: 1; visibility: visible; display: inline; overflow: visible;" p:name="rect" id="95616380b7994090984f102421c10ea2"/>
            <text x="4.561419" y="14.175718" style="text-align: left; text-anchor: start; dominant-baseline: central; fill: rgb(0, 0, 0); fill-opacity: 1; font-family: PencilUI; font-size: 10px; font-weight: 500; font-style: normal; text-decoration: none;" xml:space="preserve" p:name="text" id="8d03abbb71ad4c4f803023af6a231011" transform="translate(0,1)">QA3-LCD-00*</text>
          </g><g p:type="Shape" p:def="Evolus.WindowsXP.Widgets:Radio" id="f11d6fe77fd84129852128e678aa5473" transform="matrix(1,0,0,1,246.00000366568565,281.0000041872263)"><p:metadata><p:property name="disabled">false</p:property><p:property name="selected">false</p:property><p:property name="label"/><p:property name="textColor">#000000FF</p:property><p:property name="textFont">Tahoma|normal|normal|11px|none|0</p:property></p:metadata>
        <defs p:name="defs2659" id="c33ed0a89fc74484a577c07c4ee215e9">
            <linearGradient p:name="linearGradient31435" id="4ef2ba881c6e44fb83bf379a28df5f37">
              <stop style="stop-color:#bab4a7;stop-opacity:1" offset="0" p:name="stop31437" id="f74237ce89d14cc2b19693f78e4e8d8b"/>
              <stop style="stop-color:#bab4a8;stop-opacity:1" offset="0.30131003" p:name="stop31439" id="660fb4925476432b95ab0ccaeab4007d"/>
              <stop style="stop-color:#d6cec0;stop-opacity:1" offset="1" p:name="stop31441" id="22ce42e8c89246e2ad91f4d8a4c4dbb5"/>
            </linearGradient>
            <linearGradient x1="503" y1="106" x2="503" y2="86" xlink:href="#4ef2ba881c6e44fb83bf379a28df5f37" gradientUnits="userSpaceOnUse" gradientTransform="translate(-381.00001,-85.899192)" p:name="linearGradient55484" id="42b05132597f4c8c8b04ff75e386b10c"/>
            <linearGradient p:name="linearGradient2237" id="58b5af62699148d7ba369b13412cc1d2">
              <stop style="stop-color:#0080c3;stop-opacity:1" offset="0" p:name="stop2239" id="ea09a728c36644ed82bfd05362dff1dc"/>
              <stop style="stop-color:#28a2e1;stop-opacity:1" offset="0.57124019" p:name="stop2268" id="48334c44cd5e45cdbb3dcc707c1be113"/>
              <stop style="stop-color:#3cb3f0;stop-opacity:1" offset="0.74453777" p:name="stop2270" id="810db5a8656a44fb81fd870e55650928"/>
              <stop style="stop-color:#51c4ff;stop-opacity:1" offset="1" p:name="stop2241" id="99efb604d20a4fb09624ae07f0d5aacf"/>
            </linearGradient>
            <linearGradient x1="439.5" y1="104" x2="439.5" y2="88" xlink:href="#58b5af62699148d7ba369b13412cc1d2" gradientUnits="userSpaceOnUse" gradientTransform="translate(-381.00001,-85.899192)" p:name="linearGradient57450" id="d858e29467ec463aa140cfb17be0c510"/>
          </defs>
          <path transform="matrix(0.9362048,0,0,0.9362048,-111.82866,-312.21823)" d="M 131.75 339.875 A 5.875 5.875 0 1 1  120,339.875 A 5.875 5.875 0 1 1  131.75 339.875 z" style="opacity: 1; color: rgb(0, 0, 0); fill: rgb(255, 255, 255); fill-opacity: 1; fill-rule: nonzero; stroke: rgb(35, 86, 130); stroke-width: 1.06771; stroke-linecap: round; stroke-linejoin: round; marker: none; stroke-miterlimit: 4; stroke-dasharray: none; stroke-dashoffset: 0; stroke-opacity: 1; visibility: visible; display: inline; overflow: visible;" p:name="outerPath" id="850d7e110331497f9c6db6e5c40bdeb9"/>
          <text xml:space="preserve" style="font-size: 11px; font-style: normal; font-weight: normal; text-align: left; text-anchor: start; dominant-baseline: central; fill: rgb(0, 0, 0); font-family: Tahoma; fill-opacity: 1; text-decoration: none;" x="18.016193" y="8.9744911" p:name="text" id="3ff586211ccc429d9f10113fb5e3c6b5" transform="translate(0,-3)"> </text>
          <path d="M 3.5201411,9.8478598 C 1.4655909,8.4017026 0.93900042,5.5125628 2.3446111,3.3988028 C 3.7503111,1.2850728 6.5585511,0.74328308 8.6131111,2.1894328 C 9.2297411,2.6234428 9.6679911,3.1221828 10.025751,3.7969628" style="opacity: 0.273632; color: rgb(0, 0, 0); fill: none; fill-opacity: 1; fill-rule: nonzero; stroke: rgb(109, 109, 109); stroke-width: 1; stroke-linecap: round; stroke-linejoin: round; marker: none; stroke-miterlimit: 4; stroke-dashoffset: 0; stroke-opacity: 1; visibility: visible; overflow: visible;" p:name="shadowPath" id="4dc4a5556db643b19ad71c6ca19ff003"/>
          <path style="opacity: 1; color: rgb(0, 0, 0); fill: rgb(56, 185, 53); fill-opacity: 1; fill-rule: nonzero; stroke: none; stroke-width: 1; stroke-linecap: round; stroke-linejoin: round; marker: none; stroke-miterlimit: 4; stroke-dasharray: none; stroke-dashoffset: 0; stroke-opacity: 1; visibility: hidden; display: none; overflow: visible;" d="M 128.5 363.75 A 2.5 2.75 0 1 1  123.5,363.75 A 2.5 2.75 0 1 1  128.5 363.75 z" transform="matrix(1.2,0,0,1.0909092,-145.18388,-390.84385)" p:name="bullet" id="b20d8c4af3344464b0e3a8f98e2f9a7a"/>
        </g><g p:type="Shape" p:def="Evolus.WindowsXP.Widgets:Radio" id="a792774e35ba465a9583b3c176ffa42b" transform="matrix(1,0,0,1,246.0000036507845,375.00000558793545)"><p:metadata><p:property name="disabled">false</p:property><p:property name="selected">false</p:property><p:property name="label"/><p:property name="textColor">#000000FF</p:property><p:property name="textFont">Tahoma|normal|normal|11px|none|0</p:property></p:metadata>
        <defs p:name="defs2659" id="bd206567faf04bf5944d495d5de94b0e">
            <linearGradient p:name="linearGradient31435" id="0afcb668d24344d19a5b06b531f1d432">
              <stop style="stop-color:#bab4a7;stop-opacity:1" offset="0" p:name="stop31437" id="e82997a4a32d4d80b9e75e019fa103ae"/>
              <stop style="stop-color:#bab4a8;stop-opacity:1" offset="0.30131003" p:name="stop31439" id="4a989a21ea414300b13e7f511eef535e"/>
              <stop style="stop-color:#d6cec0;stop-opacity:1" offset="1" p:name="stop31441" id="405174c89b424753a36ce7223da2aba5"/>
            </linearGradient>
            <linearGradient x1="503" y1="106" x2="503" y2="86" xlink:href="#0afcb668d24344d19a5b06b531f1d432" gradientUnits="userSpaceOnUse" gradientTransform="translate(-381.00001,-85.899192)" p:name="linearGradient55484" id="5010c7e5fc9441cdba50c073fb18606c"/>
            <linearGradient p:name="linearGradient2237" id="f227895184f744f6890e8a4af2ddefc2">
              <stop style="stop-color:#0080c3;stop-opacity:1" offset="0" p:name="stop2239" id="394f93f14c204b7a9343e17d793181a3"/>
              <stop style="stop-color:#28a2e1;stop-opacity:1" offset="0.57124019" p:name="stop2268" id="ef3e1ee8e8e4463c99ce9d1d00c7f0d2"/>
              <stop style="stop-color:#3cb3f0;stop-opacity:1" offset="0.74453777" p:name="stop2270" id="96812916ecda412aa92b49544a089c2e"/>
              <stop style="stop-color:#51c4ff;stop-opacity:1" offset="1" p:name="stop2241" id="65b28cee7b234c7b9f783f3aa8c72d65"/>
            </linearGradient>
            <linearGradient x1="439.5" y1="104" x2="439.5" y2="88" xlink:href="#f227895184f744f6890e8a4af2ddefc2" gradientUnits="userSpaceOnUse" gradientTransform="translate(-381.00001,-85.899192)" p:name="linearGradient57450" id="bc4b8d40210045149c07e73d42c6ddec"/>
          </defs>
          <path transform="matrix(0.9362048,0,0,0.9362048,-111.82866,-312.21823)" d="M 131.75 339.875 A 5.875 5.875 0 1 1  120,339.875 A 5.875 5.875 0 1 1  131.75 339.875 z" style="opacity: 1; color: rgb(0, 0, 0); fill: rgb(255, 255, 255); fill-opacity: 1; fill-rule: nonzero; stroke: rgb(35, 86, 130); stroke-width: 1.06771; stroke-linecap: round; stroke-linejoin: round; marker: none; stroke-miterlimit: 4; stroke-dasharray: none; stroke-dashoffset: 0; stroke-opacity: 1; visibility: visible; display: inline; overflow: visible;" p:name="outerPath" id="45bc6cc7245d4806b69fe46542d84af4"/>
          <text xml:space="preserve" style="font-size: 11px; font-style: normal; font-weight: normal; text-align: left; text-anchor: start; dominant-baseline: central; fill: rgb(0, 0, 0); font-family: Tahoma; fill-opacity: 1; text-decoration: none;" x="18.016193" y="8.9744911" p:name="text" id="66f5476671ce403590b2042f7d7d9954" transform="translate(0,-3)"> </text>
          <path d="M 3.5201411,9.8478598 C 1.4655909,8.4017026 0.93900042,5.5125628 2.3446111,3.3988028 C 3.7503111,1.2850728 6.5585511,0.74328308 8.6131111,2.1894328 C 9.2297411,2.6234428 9.6679911,3.1221828 10.025751,3.7969628" style="opacity: 0.273632; color: rgb(0, 0, 0); fill: none; fill-opacity: 1; fill-rule: nonzero; stroke: rgb(109, 109, 109); stroke-width: 1; stroke-linecap: round; stroke-linejoin: round; marker: none; stroke-miterlimit: 4; stroke-dashoffset: 0; stroke-opacity: 1; visibility: visible; overflow: visible;" p:name="shadowPath" id="7b9ae8597b8e49669f8a62f7b09bc822"/>
          <path style="opacity: 1; color: rgb(0, 0, 0); fill: rgb(56, 185, 53); fill-opacity: 1; fill-rule: nonzero; stroke: none; stroke-width: 1; stroke-linecap: round; stroke-linejoin: round; marker: none; stroke-miterlimit: 4; stroke-dasharray: none; stroke-dashoffset: 0; stroke-opacity: 1; visibility: hidden; display: none; overflow: visible;" d="M 128.5 363.75 A 2.5 2.75 0 1 1  123.5,363.75 A 2.5 2.75 0 1 1  128.5 363.75 z" transform="matrix(1.2,0,0,1.0909092,-145.18388,-390.84385)" p:name="bullet" id="f00cc0834b5645f4b0ca389f9551a75e"/>
        </g><g p:type="Shape" p:def="Evolus.WindowsXP.Widgets:Label" id="6ce3c8a0698e4a0d92d32958d59cfe89" transform="matrix(1,0,0,1,432.00000643730164,207.00000312924382)"><p:metadata><p:property name="disabled">false</p:property><p:property name="label">( for default auto query scan record )</p:property><p:property name="textColor">#000000FF</p:property><p:property name="textFont">PencilUI|500|normal|10px|none|0</p:property></p:metadata>
              <text y="9.0077095" x="17.999992" style="font-size: 10px; font-style: normal; font-weight: 500; text-align: left; text-anchor: start; dominant-baseline: central; fill: rgb(0, 0, 0); font-family: PencilUI; fill-opacity: 1; text-decoration: none;" xml:space="preserve" p:name="text" id="34db3529e0bd4b13a08b2e10c356b9a1" transform="translate(-18,-3)">( for default auto query scan record )</text>
             <path style="fill: none; stroke-width: 1px; visibility: hidden;" p:name="accelIndicator" id="654bfd3aab584144ae47eb0661ca649b"/>
        </g></g></g></svg>