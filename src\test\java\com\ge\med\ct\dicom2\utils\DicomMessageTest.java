package com.ge.med.ct.dicom2.utils;

import com.ge.med.ct.exception.message.DicomMessages;
import com.ge.med.ct.exception.message.DicomMessageBuilder;
import com.ge.med.ct.exception.core.QAToolException;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * DICOM消息系统测试类
 */
public class DicomMessageTest {

    @BeforeEach
    void setUp() {
        // 确保使用默认语言环境
    }

    @Test
    void testNoParamMessage() {
        String message = DicomMessages.MODEL_NULL.format().toStr();
        assertEquals("模型为空", message);
    }

    @Test
    void testSingleParamMessage() {
        String message = DicomMessages.FILE_NOT_FOUND.format("test.dcm").toStr();
        assertEquals("DICOM文件不存在: test.dcm", message);
    }

    @Test
    void testTwoParamMessage() {
        String message = DicomMessages.PROCESSING_ERROR.format("file.dcm", "IO错误").toStr();
        assertEquals("处理错误: file.dcm, IO错误", message);
    }

    @Test
    void testBuilderPattern() {
        String message = DicomMessageBuilder.of(DicomMessages.SCAN_START)
                .param("/path/to/dir")
                .param("递归")
                .build();
        assertEquals("开始扫描目录: /path/to/dir 递归", message);
    }

    @Test
    void testMessageFormatting() {
        // 测试无参数消息
        String noParamMessage = DicomMessages.MODEL_NULL.format().toStr();
        assertEquals(DicomMessages.MODEL_NULL.getDefaultMessage(), noParamMessage);

        // 测试单参数消息
        String singleParamMessage = DicomMessages.FILE_NOT_FOUND.format("/path/to/file").toStr();
        assertEquals("DICOM文件不存在: /path/to/file", singleParamMessage);

        // 测试双参数消息
        String doubleParamMessage = DicomMessages.PROCESSING_ERROR.format("file.dcm", "IO错误").toStr();
        assertEquals("处理错误: file.dcm, IO错误", doubleParamMessage);
    }

    @Test
    void testMessageDescription() {
        // 测试TAG_MISSING消息的描述
        assertEquals("缺少必需的标签: {0}", DicomMessages.TAG_MISSING.getDefaultMessage());

        // 测试格式化消息
        String formattedMessage = DicomMessages.TAG_MISSING.format("PatientID").toStr();
        assertEquals("缺少必需的标签: PatientID", formattedMessage);
    }

    @Test
    void testMessageBuilder() {
        // 测试消息构建器
        String message = DicomMessageBuilder.of(DicomMessages.FILE_NOT_FOUND)
                .param("/path/to/file")
                .build();
        assertEquals("DICOM文件不存在: /path/to/file", message);
    }

    @Test
    void testNullHandling() {
        // 测试空参数处理
        String message = DicomMessages.FILE_NOT_FOUND.format((Object) null).toStr();
        assertEquals("DICOM文件不存在: null", message);

        // 测试空消息对象
        assertThrows(QAToolException.class, () -> DicomMessageBuilder.of(null));
    }

    @Test
    void testMessageBuilderGetters() {
        DicomMessageBuilder builder = DicomMessageBuilder.of(DicomMessages.SCAN_START)
                .param("dir")
                .param("recursive");

        assertEquals(DicomMessages.SCAN_START, builder.getMessage());
        List<Object> params = builder.getParams();
        assertEquals(2, params.size());
        assertEquals("dir", params.get(0));
        assertEquals("recursive", params.get(1));
    }
}