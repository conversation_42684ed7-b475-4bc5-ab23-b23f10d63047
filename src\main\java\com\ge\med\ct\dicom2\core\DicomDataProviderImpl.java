package com.ge.med.ct.dicom2.core;

import java.io.File;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.logging.Logger;
import java.util.stream.Collectors;

import com.ge.med.ct.analysis.service.AnalysisStatusCallback;
import com.ge.med.ct.dicom2.model.DicomExam;
import com.ge.med.ct.dicom2.model.DicomFileModel;
import com.ge.med.ct.dicom2.model.DicomImage;
import com.ge.med.ct.dicom2.model.DicomSeries;
import com.ge.med.ct.dicom2.service.DicomFileValidator;
import com.ge.med.ct.dicom2.tag.DicomTag;
import com.ge.med.ct.dicom2.tag.DicomTagConstants;
import com.ge.med.ct.exception.aspect.HandleException;
import com.ge.med.ct.exception.code.ErrorCode;
import com.ge.med.ct.exception.core.DicomException;
import com.ge.med.ct.exception.message.DicomMessages;
import com.ge.med.ct.exception.util.LambdaExceptionUtil;
import com.ge.med.ct.service.LogManager;

// DICOM数据提供者实现类
@HandleException
public class DicomDataProviderImpl implements DicomDataProvider {
    private static final Logger LOG = LogManager.getInstance().getLogger(DicomDataProviderImpl.class);
    private static volatile DicomDataProviderImpl instance;

    // 数据存储
    private final Map<String, DicomExam> exams;
    private final Map<String, DicomSeries> series;
    private final Map<String, DicomImage> images;
    private final Map<String, Set<String>> examToSeriesMap;
    private final Map<String, Set<String>> seriesToImageMap;
    private final Map<String, DicomFileModel> fileModels;

    // 线程池配置
    private static final int CORE_POOL_SIZE = Runtime.getRuntime().availableProcessors();
    private static final int MAX_POOL_SIZE = CORE_POOL_SIZE * 2;
    private static final long KEEP_ALIVE_TIME = 60L;

    private final ExecutorService executor;

    private DicomDataProviderImpl() {
        this.exams = new ConcurrentHashMap<>();
        this.series = new ConcurrentHashMap<>();
        this.images = new ConcurrentHashMap<>();
        this.examToSeriesMap = new ConcurrentHashMap<>();
        this.seriesToImageMap = new ConcurrentHashMap<>();
        this.fileModels = new ConcurrentHashMap<>();

        // 创建线程池
        this.executor = new ThreadPoolExecutor(
                CORE_POOL_SIZE,
                MAX_POOL_SIZE,
                KEEP_ALIVE_TIME,
                TimeUnit.SECONDS,
                new LinkedBlockingQueue<>(),
                new ThreadPoolExecutor.CallerRunsPolicy());
    }

    public static DicomDataProviderImpl getInstance() {
        if (instance == null) {
            synchronized (DicomDataProviderImpl.class) {
                if (instance == null) {
                    instance = new DicomDataProviderImpl();
                }
            }
        }
        return instance;
    }

    @Override
    public List<DicomExam> getAllExams() {
        return new ArrayList<>(exams.values());
    }

    @Override
    public CompletableFuture<List<DicomExam>> getAllExamsAsync() {
        return CompletableFuture.supplyAsync(this::getAllExams, executor);
    }

    @Override
    public DicomExam getExam(String examId) {
        return exams.get(examId);
    }

    @Override
    public CompletableFuture<DicomExam> getExamAsync(String examId) {
        return CompletableFuture.supplyAsync(() -> getExam(examId), executor);
    }

    @Override
    public List<DicomExam> searchExams(String patientName, String patientId) {
        return exams.values().stream()
                .filter(exam -> matchesExamCriteria(exam, patientName, patientId))
                .collect(Collectors.toList());
    }

    @Override
    public CompletableFuture<List<DicomExam>> searchExamsAsync(String patientName, String patientId) {
        return CompletableFuture.supplyAsync(
                () -> searchExams(patientName, patientId),
                executor);
    }

    private boolean matchesExamCriteria(DicomExam exam, String patientName, String patientId) {
        boolean nameMatch = patientName == null || patientName.trim().isEmpty() ||
                (exam.getPatientName() != null
                        && exam.getPatientName().toLowerCase().contains(patientName.toLowerCase().trim()));

        boolean idMatch = patientId == null || patientId.trim().isEmpty() ||
                patientId.equals(exam.getPatientID());

        return nameMatch && idMatch;
    }

    @Override
    public DicomSeries getSeries(String seriesId) {
        return series.get(seriesId);
    }

    @Override
    public List<DicomSeries> getSeriesForExam(String examId) {
        return Optional.ofNullable(examToSeriesMap.get(examId))
                .map(seriesIds -> seriesIds.stream()
                        .map(this::getSeries)
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList()))
                .orElse(new ArrayList<>());
    }

    @Override
    public DicomImage getImage(String imageId) {
        return images.get(imageId);
    }

    @Override
    public List<DicomImage> getImagesForSeries(String seriesId) {
        return Optional.ofNullable(seriesToImageMap.get(seriesId))
                .map(imageIds -> imageIds.stream()
                        .map(this::getImage)
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList()))
                .orElse(new ArrayList<>());
    }

    @Override
    @HandleException(errorCode = ErrorCode.DICOM_VALIDATION)
    public void addFileModel(DicomFileModel model) throws DicomException {
        if (model == null) {
            throw new DicomException(DicomMessages.MODEL_NULL, ErrorCode.DICOM_VALIDATION);
        }

        String modelId = model.getId();
        if (modelId == null || modelId.trim().isEmpty()) {
            throw new DicomException(DicomMessages.ID_EMPTY, ErrorCode.DICOM_VALIDATION);
        }

        // 验证DICOM数据
        try {
            DicomFileValidator.validateDicomFileModel(model);
        } catch (IllegalArgumentException e) {
            throw new DicomException(DicomMessages.MODEL_NULL, ErrorCode.DICOM_VALIDATION, e.getMessage());
        }
        updateStatus("验证DICOM数据成功: " + new File(model.getFilePath()).getName(), 10);

        // 检查是否已存在
        if (fileModels.containsKey(modelId)) {
            LOG.warning(DicomMessages.MODEL_EXISTS.format(modelId).toStr());
            updateStatus("文件模型已存在: " + modelId, -1);
            return;
        }

        // 添加或更新文件模型
        fileModels.put(modelId, model);
        updateStatus("添加DICOM文件模型: " + new File(model.getFilePath()).getName(), 20);

        // 处理图像数据
        processImageData(model);

        // 处理系列数据
        processSeriesData(model);

        // 处理检查数据
        processExamData(model);

        // 如果是第一个检查，自动选择它
        if (exams.size() == 1) {
            DicomExam firstExam = exams.values().iterator().next();
            try {
                DicomFileValidator.validateDicomStructure(Arrays.asList(firstExam));
            } catch (IllegalArgumentException e) {
                throw new DicomException(DicomMessages.EXAM_EMPTY, ErrorCode.DICOM_VALIDATION, e.getMessage());
            }
        }
    }

    @Override
    public CompletableFuture<Void> addFileModelAsync(DicomFileModel model) {
        return CompletableFuture.runAsync(
                com.ge.med.ct.exception.util.LambdaExceptionUtil.uncheckedRunnable(() -> addFileModel(model)),
                executor);
    }

    @Override
    public String getTagValue(String fileId, String tagId) {
        return (String) Optional.ofNullable(fileModels.get(fileId))
                .map(model -> model.getTagValue(tagId))
                .orElse(null);
    }

    @Override
    public List<DicomFileModel> getAllFileModels() {
        return new ArrayList<>(fileModels.values());
    }

    @Override
    public CompletableFuture<List<DicomFileModel>> getAllFileModelsAsync() {
        return CompletableFuture.supplyAsync(this::getAllFileModels, executor);
    }

    @Override
    public void clearData() throws DicomException {
        exams.clear();
        series.clear();
        images.clear();
        examToSeriesMap.clear();
        seriesToImageMap.clear();
        fileModels.clear();
        LOG.info(DicomMessages.DATA_CLEARED.format().toStr());
    }

    @Override
    public void shutdown() {
        if (!executor.isShutdown()) {
            try {
                executor.shutdown();
                if (!executor.awaitTermination(60, TimeUnit.SECONDS)) {
                    executor.shutdownNow();
                }
            } catch (InterruptedException e) {
                executor.shutdownNow();
                Thread.currentThread().interrupt();
                LOG.severe(DicomMessages.MONITOR_FAILED.format(e.getMessage()).toStr());
            }
            LOG.info(DicomMessages.MONITOR_STOP.format("all").toStr());
        }
    }

    @HandleException(errorCode = ErrorCode.PROCESSING)
    private void processImageData(DicomFileModel model) throws DicomException {
        DicomImage image = model.getImage();
        if (image != null && image.getId() != null) {
            updateImageAttributes(image, model.getTags());
            images.put(image.getId(), image);
        }
    }

    @HandleException(errorCode = ErrorCode.PROCESSING)
    private void processSeriesData(DicomFileModel model) throws DicomException {
        String seriesInstanceUID = model.getSeriesInstanceUID();
        if (seriesInstanceUID != null) {
            DicomSeries series = createOrUpdateSeries(model);
            DicomImage image = model.getImage();
            if (image != null && image.getId() != null) {
                addImageToSeries(series.getId(), image.getId());
                Set<String> imageIds = seriesToImageMap.get(series.getId());
                int count = (imageIds != null) ? imageIds.size() : 0;
                series.setImageCount(count);
            }
        }
    }

    @HandleException(errorCode = ErrorCode.PROCESSING)
    private void processExamData(DicomFileModel model) throws DicomException {
        String studyInstanceUID = model.getStudyInstanceUID();
        if (studyInstanceUID == null) {
            LOG.warning(DicomMessages.EXAM_EMPTY.format("unknown").toStr());
            return;
        }

        try {
            DicomExam exam = createOrUpdateExam(model);
            if (exam != null) {
                String seriesId = model.getSeriesInstanceUID();
                if (seriesId != null) {
                    addSeriesToExam(exam.getId(), seriesId);
                }
            } else {
                throw new DicomException(DicomMessages.EXAM_PROCESSING_ERROR, ErrorCode.PROCESSING,
                        "Failed to create exam");
            }
        } catch (Exception e) {
            throw new DicomException(DicomMessages.PROCESSING_ERROR, ErrorCode.PROCESSING, e,
                    studyInstanceUID, e.getMessage());
        }
    }

    private void addSeriesToExam(String examId, String seriesId) {
        examToSeriesMap.computeIfAbsent(examId, k -> ConcurrentHashMap.newKeySet()).add(seriesId);
    }

    private void addImageToSeries(String seriesId, String imageId) {
        seriesToImageMap.computeIfAbsent(seriesId, k -> ConcurrentHashMap.newKeySet()).add(imageId);
    }

    @HandleException(errorCode = ErrorCode.PROCESSING)
    private DicomSeries createOrUpdateSeries(DicomFileModel model) throws DicomException {
        try {
            String seriesInstanceUID = model.getSeriesInstanceUID();
            return series.computeIfAbsent(seriesInstanceUID,
                    LambdaExceptionUtil.uncheckedFunction(id -> {
                        DicomSeries newSeries = new DicomSeries(id);
                        updateSeriesAttributes(newSeries, model.getTags());
                        return newSeries;
                    }));
        } catch (Exception e) {
            String seriesInstanceUID = model.getSeriesInstanceUID();
            throw new DicomException(DicomMessages.SERIES_PROCESSING_ERROR, ErrorCode.PROCESSING, e,
                    seriesInstanceUID);
        }
    }

    @HandleException(errorCode = ErrorCode.PROCESSING)
    private DicomExam createOrUpdateExam(DicomFileModel model) throws DicomException {
        try {
            String studyInstanceUID = model.getStudyInstanceUID();
            return exams.computeIfAbsent(studyInstanceUID,
                    LambdaExceptionUtil.uncheckedFunction(id -> {
                        DicomExam newExam = new DicomExam(id);
                        updateExamAttributes(newExam, model.getTags());
                        return newExam;
                    }));
        } catch (Exception e) {
            String studyInstanceUID = model.getStudyInstanceUID();
            throw new DicomException(DicomMessages.EXAM_PROCESSING_ERROR, ErrorCode.PROCESSING, e,
                    studyInstanceUID);
        }
    }

    @HandleException(errorCode = ErrorCode.PROCESSING)
    private void updateSeriesAttributes(DicomSeries series, Map<String, DicomTag> attributes) throws DicomException {
        if (attributes == null || attributes.isEmpty()) {
            throw new DicomException(DicomMessages.TAG_MISSING, ErrorCode.INVALID_FILE, "all");
        }

        // 基本序列属性
        if (attributes.containsKey(DicomTagConstants.Series.SERIES_DESCRIPTION)) {
            DicomTag tag = attributes.get(DicomTagConstants.Series.SERIES_DESCRIPTION);
            series.addTag(DicomTagConstants.Series.SERIES_DESCRIPTION, tag);
            series.setSeriesDescription(tag.getValueAsString());
        }
        if (attributes.containsKey(DicomTagConstants.Series.SERIES_NUMBER)) {
            DicomTag tag = attributes.get(DicomTagConstants.Series.SERIES_NUMBER);
            series.addTag(DicomTagConstants.Series.SERIES_NUMBER, tag);
            series.setSeriesNumber(tag.getValueAsString());
        }
        if (attributes.containsKey(DicomTagConstants.Series.MODALITY)) {
            DicomTag tag = attributes.get(DicomTagConstants.Series.MODALITY);
            series.addTag(DicomTagConstants.Series.MODALITY, tag);
            series.setModality(tag.getValueAsString());
        }
        if (attributes.containsKey(DicomTagConstants.Equipment.STATION_NAME)) {
            DicomTag tag = attributes.get(DicomTagConstants.Equipment.STATION_NAME);
            series.addTag(DicomTagConstants.Equipment.STATION_NAME, tag);
            series.setStationName(tag.getValueAsString());
        }
        if (attributes.containsKey(DicomTagConstants.Image.IMAGE_TYPE)) {
            DicomTag tag = attributes.get(DicomTagConstants.Image.IMAGE_TYPE);
            series.addTag(DicomTagConstants.Image.IMAGE_TYPE, tag);
            series.setType(tag.getValueAsString());
        }

        // 添加其他重要序列属性
        if (attributes.containsKey(DicomTagConstants.Image.SLICE_THICKNESS)) {
            DicomTag tag = attributes.get(DicomTagConstants.Image.SLICE_THICKNESS);
            series.addTag(DicomTagConstants.Image.SLICE_THICKNESS, tag);
        }
        if (attributes.containsKey(DicomTagConstants.Equipment.MANUFACTURER)) {
            DicomTag tag = attributes.get(DicomTagConstants.Equipment.MANUFACTURER);
            series.addTag(DicomTagConstants.Equipment.MANUFACTURER, tag);
        }
        if (attributes.containsKey(DicomTagConstants.CT.FIELD_OF_VIEW)) {
            DicomTag tag = attributes.get(DicomTagConstants.CT.FIELD_OF_VIEW);
            series.addTag(DicomTagConstants.CT.FIELD_OF_VIEW, tag);
        }
        if (attributes.containsKey(DicomTagConstants.CT.RECONSTRUCTION_DIAMETER)) {
            DicomTag tag = attributes.get(DicomTagConstants.CT.RECONSTRUCTION_DIAMETER);
            series.addTag(DicomTagConstants.CT.RECONSTRUCTION_DIAMETER, tag);
        }
        if (attributes.containsKey(DicomTagConstants.CT.CONVOLUTION_KERNEL)) {
            DicomTag tag = attributes.get(DicomTagConstants.CT.CONVOLUTION_KERNEL);
            series.addTag(DicomTagConstants.CT.CONVOLUTION_KERNEL, tag);
        }

        // 更新图像数量
        String seriesId = series.getId();
        if (seriesId != null && seriesToImageMap.containsKey(seriesId)) {
            Set<String> imageIds = seriesToImageMap.get(seriesId);
            series.setImageCount(imageIds != null ? imageIds.size() : 0);
        }
    }

    @HandleException(errorCode = ErrorCode.PROCESSING)
    private void updateExamAttributes(DicomExam exam, Map<String, DicomTag> attributes) throws DicomException {
        if (attributes == null || attributes.isEmpty()) {
            throw new DicomException(DicomMessages.TAG_MISSING, ErrorCode.DICOM_VALIDATION, "all");
        }

        if (attributes.containsKey(DicomTagConstants.Patient.PATIENT_ID)) {
            DicomTag tag = attributes.get(DicomTagConstants.Patient.PATIENT_ID);
            String patientId = tag.getValueAsString();
            exam.setPatientID(patientId);
            exam.addTag(DicomTagConstants.Patient.PATIENT_ID, tag);
        }

        if (attributes.containsKey(DicomTagConstants.Patient.PATIENT_NAME)) {
            DicomTag tag = attributes.get(DicomTagConstants.Patient.PATIENT_NAME);
            String patientName = tag.getValueAsString();
            exam.setPatientName(patientName);
            exam.addTag(DicomTagConstants.Patient.PATIENT_NAME, tag);
        }

        if (attributes.containsKey(DicomTagConstants.Study.STUDY_DATE)) {
            DicomTag tag = attributes.get(DicomTagConstants.Study.STUDY_DATE);
            exam.setStudyDate(tag.getValueAsString());
            exam.addTag(DicomTagConstants.Study.STUDY_DATE, tag);
        }

        if (attributes.containsKey(DicomTagConstants.Study.STUDY_TIME)) {
            DicomTag tag = attributes.get(DicomTagConstants.Study.STUDY_TIME);
            exam.setStudyTime(tag.getValueAsString());
            exam.addTag(DicomTagConstants.Study.STUDY_TIME, tag);
        }

        // For attributes without direct setters, use addTag method
        if (attributes.containsKey(DicomTagConstants.Study.STUDY_DESCRIPTION)) {
            DicomTag tag = attributes.get(DicomTagConstants.Study.STUDY_DESCRIPTION);
            exam.addTag(DicomTagConstants.Study.STUDY_DESCRIPTION, tag);
        }

        if (attributes.containsKey(DicomTagConstants.Study.STUDY_ID)) {
            DicomTag tag = attributes.get(DicomTagConstants.Study.STUDY_ID);
            exam.addTag(DicomTagConstants.Study.STUDY_ID, tag);
        }

        if (attributes.containsKey(DicomTagConstants.Study.ACCESSION_NUMBER)) {
            DicomTag tag = attributes.get(DicomTagConstants.Study.ACCESSION_NUMBER);
            exam.addTag(DicomTagConstants.Study.ACCESSION_NUMBER, tag);
        }

        if (attributes.containsKey(DicomTagConstants.Equipment.STATION_NAME)) {
            DicomTag tag = attributes.get(DicomTagConstants.Equipment.STATION_NAME);
            exam.addTag(DicomTagConstants.Equipment.STATION_NAME, tag);
        }

        if (attributes.containsKey(DicomTagConstants.Series.MODALITY)) {
            DicomTag tag = attributes.get(DicomTagConstants.Series.MODALITY);
            exam.addTag(DicomTagConstants.Series.MODALITY, tag);
        }

        LOG.info(String.format("检查信息 - 患者ID: %s, 检查ID: %s, 站点: %s, 患者姓名: %s",
                exam.getTagValue(DicomTagConstants.Patient.PATIENT_ID),
                exam.getTagValue(DicomTagConstants.Study.STUDY_ID),
                exam.getTagValue(DicomTagConstants.Equipment.STATION_NAME),
                exam.getPatientName()));
    }

    @HandleException(errorCode = ErrorCode.PROCESSING)
    private void updateImageAttributes(DicomImage image, Map<String, DicomTag> attributes) throws DicomException {
        // 基本图像属性
        if (attributes.containsKey(DicomTagConstants.Image.INSTANCE_NUMBER)) {
            DicomTag tag = attributes.get(DicomTagConstants.Image.INSTANCE_NUMBER);
            image.addTag(DicomTagConstants.Image.INSTANCE_NUMBER, tag);
        }
        if (attributes.containsKey(DicomTagConstants.Image.SLICE_THICKNESS)) {
            DicomTag tag = attributes.get(DicomTagConstants.Image.SLICE_THICKNESS);
            image.addTag(DicomTagConstants.Image.SLICE_THICKNESS, tag);
        }
        if (attributes.containsKey(DicomTagConstants.Image.PIXEL_SPACING)) {
            DicomTag tag = attributes.get(DicomTagConstants.Image.PIXEL_SPACING);
            image.addTag(DicomTagConstants.Image.PIXEL_SPACING, tag);
        }
        if (attributes.containsKey(DicomTagConstants.Image.SLICE_LOCATION)) {
            DicomTag tag = attributes.get(DicomTagConstants.Image.SLICE_LOCATION);
            image.addTag(DicomTagConstants.Image.SLICE_LOCATION, tag);
        }

        // 图像位置和方向
        if (attributes.containsKey(DicomTagConstants.Image.IMAGE_POSITION_PATIENT)) {
            DicomTag tag = attributes.get(DicomTagConstants.Image.IMAGE_POSITION_PATIENT);
            image.addTag(DicomTagConstants.Image.IMAGE_POSITION_PATIENT, tag);
        }
        if (attributes.containsKey(DicomTagConstants.Image.IMAGE_ORIENTATION_PATIENT)) {
            DicomTag tag = attributes.get(DicomTagConstants.Image.IMAGE_ORIENTATION_PATIENT);
            image.addTag(DicomTagConstants.Image.IMAGE_ORIENTATION_PATIENT, tag);
        }

        // 窗宽窗位
        if (attributes.containsKey(DicomTagConstants.Image.WINDOW_CENTER)) {
            DicomTag tag = attributes.get(DicomTagConstants.Image.WINDOW_CENTER);
            image.addTag(DicomTagConstants.Image.WINDOW_CENTER, tag);
        }
        if (attributes.containsKey(DicomTagConstants.Image.WINDOW_WIDTH)) {
            DicomTag tag = attributes.get(DicomTagConstants.Image.WINDOW_WIDTH);
            image.addTag(DicomTagConstants.Image.WINDOW_WIDTH, tag);
        }

        // 重要的CT特有属性
        if (attributes.containsKey(DicomTagConstants.CT.KVP)) {
            DicomTag tag = attributes.get(DicomTagConstants.CT.KVP);
            image.addTag(DicomTagConstants.CT.KVP, tag);
        }
        if (attributes.containsKey(DicomTagConstants.CT.GANTRY_TILT)) {
            DicomTag tag = attributes.get(DicomTagConstants.CT.GANTRY_TILT);
            image.addTag(DicomTagConstants.CT.GANTRY_TILT, tag);
        }
        if (attributes.containsKey(DicomTagConstants.CT.CONVOLUTION_KERNEL)) {
            DicomTag tag = attributes.get(DicomTagConstants.CT.CONVOLUTION_KERNEL);
            image.addTag(DicomTagConstants.CT.CONVOLUTION_KERNEL, tag);
        }
        if (attributes.containsKey(DicomTagConstants.Image.IMAGE_COMMENTS)) {
            DicomTag tag = attributes.get(DicomTagConstants.Image.IMAGE_COMMENTS);
            image.addTag(DicomTagConstants.Image.IMAGE_COMMENTS, tag);
        }
    }

    private AnalysisStatusCallback statusCallback;

    @Override
    public void setStatusCallback(AnalysisStatusCallback callback) {
        this.statusCallback = callback;
    }

    private void updateStatus(String message, int progress) {
        if (statusCallback != null) {
            statusCallback.updateStatus(message, progress);
        }
    }

    @HandleException(errorCode = ErrorCode.PROCESSING)
    public DicomSeries createSeries(String id, DicomFileModel model) throws DicomException {
        if (id == null || id.trim().isEmpty()) {
            throw new DicomException(DicomMessages.SERIES_EMPTY, ErrorCode.DICOM_VALIDATION, id);
        }
        DicomSeries newSeries = new DicomSeries(id);
        updateSeriesAttributes(newSeries, model.getTags());
        return newSeries;
    }

    @HandleException(errorCode = ErrorCode.PROCESSING)
    public DicomExam createExam(String id, DicomFileModel model) throws DicomException {
        if (id == null || id.trim().isEmpty()) {
            throw new DicomException(DicomMessages.EXAM_EMPTY, ErrorCode.DICOM_VALIDATION, id);
        }
        DicomExam newExam = new DicomExam(id);
        updateExamAttributes(newExam, model.getTags());
        return newExam;
    }
}