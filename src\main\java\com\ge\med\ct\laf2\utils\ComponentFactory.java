package com.ge.med.ct.laf2.utils;

import com.ge.med.ct.laf2.components.CTButton;
import com.ge.med.ct.laf2.components.CTJScrollPane;
import com.ge.med.ct.laf2.components.CTPanel;
import com.ge.med.ct.laf2.components.CTPanel.PanelType;
import com.ge.med.ct.laf2.components.CTTable;
import com.ge.med.ct.laf2.theming.ThemeConstants;

import javax.swing.*;
import javax.swing.table.DefaultTableModel;
import java.awt.*;
import java.awt.event.ActionListener;
import java.util.Vector;

/**
 * UI组件工厂类
 * 提供创建常用UI组件的静态方法，减少代码重复
 */
public class ComponentFactory {
    // 常量定义 - 使用ThemeConstants
    private static Dimension getButtonSize() {
        return new Dimension(90, ThemeConstants.Sizes.getButtonHeight());
    }

    private static Dimension getLabelSize() {
        return new Dimension(80, 25);
    }

    private static Dimension getFieldSize() {
        return new Dimension(150, ThemeConstants.Sizes.getComponentHeight());
    }

    private ComponentFactory() {
        // 私有构造函数，防止实例化
    }

    /**
     * 创建按钮
     * @param text 按钮文本
     * @param listener 点击监听器
     * @return 按钮实例
     */
    public static CTButton createButton(String text, ActionListener listener) {
        return createButton(text, listener, getButtonSize());
    }

    /**
     * 创建按钮并指定尺寸
     * @param text 按钮文本
     * @param listener 点击监听器
     * @param size 按钮尺寸
     * @return 按钮实例
     */
    public static CTButton createButton(String text, ActionListener listener, Dimension size) {
        CTButton button = new CTButton(text);
        button.setPreferredSize(size);
        if (listener != null) {
            button.addActionListener(listener);
        }
        return button;
    }

    /**
     * 创建标签
     * @param text 标签文本
     * @return 标签实例
     */
    public static JLabel createLabel(String text) {
        JLabel label = new JLabel(text);
        label.setPreferredSize(getLabelSize());
        label.setFont(ThemeConstants.Fonts.getDefault());
        return label;
    }

    /**
     * 创建文本输入框
     * @return 文本输入框实例
     */
    public static JTextField createTextField() {
        JTextField field = new JTextField();
        field.setPreferredSize(getFieldSize());
        field.setFont(ThemeConstants.Fonts.getDefault());
        field.setBackground(ThemeConstants.Colors.getFieldBackground());
        return field;
    }

    /**
     * 创建文本输入框并指定列数
     * @param columns 列数
     * @return 文本输入框实例
     */
    public static JTextField createTextField(int columns) {
        JTextField field = new JTextField(columns);
        field.setPreferredSize(new Dimension(columns * 10, getFieldSize().height));
        field.setFont(ThemeConstants.Fonts.getDefault());
        field.setBackground(ThemeConstants.Colors.getFieldBackground());
        return field;
    }

    /**
     * 创建表格
     * @param columnNames 列名
     * @return 表格实例
     */
    public static CTTable createTable(Vector<String> columnNames) {
        DefaultTableModel model = new DefaultTableModel(null, columnNames);
        return new CTTable(model);
    }

    /**
     * 创建表格面板
     * @param title 面板标题
     * @param table 表格实例
     * @return 面板实例
     */
    public static CTPanel createTablePanel(String title, CTTable table) {
        CTPanel panel = new CTPanel(PanelType.HEADER);
        panel.setTitle(title);
        panel.setContent(new CTJScrollPane(table));
        return panel;
    }
}
