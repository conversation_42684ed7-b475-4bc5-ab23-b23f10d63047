package com.ge.med.ct.laf2;

import com.ge.med.ct.dicom2.service.DicomFileService;
import com.ge.med.ct.exception.aspect.HandleException;
import com.ge.med.ct.exception.code.ErrorCode;
import com.ge.med.ct.exception.core.QAToolException;
import com.ge.med.ct.exception.event.ExceptionEvent;
import com.ge.med.ct.exception.message.UIMessages;
import com.ge.med.ct.laf2.base.CTLookAndFeel;
import com.ge.med.ct.laf2.components.CTButton;
import com.ge.med.ct.laf2.components.CTPanel;
import com.ge.med.ct.laf2.components.CTStatusBar;
import com.ge.med.ct.laf2.presenter.AnalysisPresenter;
import com.ge.med.ct.laf2.presenter.ReportPresenter;
import com.ge.med.ct.laf2.theming.ThemeConstants;
import com.ge.med.ct.laf2.utils.ComponentFactory;
import com.ge.med.ct.laf2.utils.EventBus;
import com.ge.med.ct.laf2.utils.MessageDisplayer;
import com.ge.med.ct.laf2.views.AnalysisView;
import com.ge.med.ct.laf2.views.ReportView;
import com.ge.med.ct.service.LogManager;
import com.ge.med.ct.service.TableOperations;

import javax.swing.*;
import java.awt.*;
import java.awt.event.WindowAdapter;
import java.awt.event.WindowEvent;
import java.util.List;
import java.util.logging.Logger;

/**
 * CT质量保证工具主应用程序
 */
public class CTQAssuranceTool extends JFrame {
    private static final Logger LOG = LogManager.getInstance().getLogger(CTQAssuranceTool.class);
    
    private static final long serialVersionUID = 1L;
    private static final int WINDOW_WIDTH = 1150;
    private static final int WINDOW_HEIGHT = 800;
    private static final int MENU_BAR_HEIGHT = 60;
    private static final int BUTTON_WIDTH = 100;
    private static final int BUTTON_HEIGHT = 38;
    private static final int MENU_BAR_PADDING = 30;
    private static final int MENU_BAR_VERTICAL_PADDING = 5;

    private final DicomFileService dicomService;

    private final AnalysisView analysisView;
    private final ReportView reportView;

    private final AnalysisPresenter analysisPresenter;
    private final ReportPresenter reportPresenter;

    private final JPanel directPanel;
    private final CTPanel contentPanel;
    private final CTPanel menuBar;
    private final CTStatusBar statusBar;
    private final CardLayout contentLayout;

    public CTQAssuranceTool() {
        CTLookAndFeel.applyLookAndFeel();
        SwingUtilities.updateComponentTreeUI(this);

        this.analysisView = new AnalysisView();
        this.reportView = new ReportView();

        this.reportPresenter = new ReportPresenter(reportView);
        this.analysisPresenter = new AnalysisPresenter(analysisView, reportPresenter);

        this.contentLayout = new CardLayout();
        this.directPanel = new JPanel(contentLayout);
        this.contentPanel = new CTPanel();

        this.menuBar = new CTPanel(new FlowLayout(FlowLayout.LEFT, MENU_BAR_PADDING, MENU_BAR_VERTICAL_PADDING));
        this.statusBar = new CTStatusBar();

        dicomService = DicomFileService.getInstance();

        initializeUI();
        beginScanDcmDirectory();
    }

    @HandleException(errorCode = ErrorCode.UI_INIT)
    private void initializeUI() {
        setTitle(ThemeConstants.App.getName());
        setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        setLayout(new BorderLayout());
        setSize(WINDOW_WIDTH, WINDOW_HEIGHT);
        setLocationRelativeTo(null);

        setupMenuBar();
        setupContentPanel();
        setupWindowListener();

        add(menuBar, BorderLayout.NORTH);
        add(contentPanel, BorderLayout.CENTER);
        add(statusBar, BorderLayout.SOUTH);
    }

    private void setupMenuBar() {
        menuBar.setPreferredSize(new Dimension(0, MENU_BAR_HEIGHT));
        menuBar.setBackground(ThemeConstants.Colors.getToolbarBackground());

        CTButton analysisButton = createMenuButton("分析", "analysis", BUTTON_WIDTH);
        CTButton reportButton = createMenuButton("报告", "report", BUTTON_WIDTH);

        menuBar.add(analysisButton);
        menuBar.add(reportButton);
    }

    private CTButton createMenuButton(String text, String viewName, int width) {
        return ComponentFactory.createButton(text, e -> switchToView(viewName),
                new Dimension(width, BUTTON_HEIGHT));
    }

    private void setupContentPanel() {
        directPanel.setBackground(ThemeConstants.Colors.getPanelBackground());
        directPanel.add(analysisView, "analysis");
        directPanel.add(reportView, "report");

        contentPanel.setContent(directPanel);
        contentLayout.show(directPanel, "analysis");
        statusBar.setStatus("分析视图已加载");
    }

    private void setupWindowListener() {
        addWindowListener(new WindowAdapter() {
            @Override
            public void windowClosing(WindowEvent e) {
                cleanup();
            }
        });
    }

    private void beginScanDcmDirectory() {
        new SwingWorker<Void, String>() {
            @Override
            protected Void doInBackground() {
                try {
                    publish("正在扫描DICOM目录...");
                    List<String> files = dicomService.scanDirectory(null, true);
                    publish("DICOM数据加载完成，共 " + files.size() + " 个文件");
                } catch (Exception e) {
                    publish("数据初始化失败: " + e.getMessage());
                }
                return null;
            }

            @Override
            protected void process(List<String> chunks) {
                if (!chunks.isEmpty()) {
                    statusBar.setStatus(chunks.get(chunks.size() - 1));
                }
            }

            @Override
            protected void done() {
                // 可根据需要在加载完成后刷新界面或通知presenter
                statusBar.setStatus("DICOM数据加载完成");
            }
        }.execute();
    }

    @HandleException(errorCode = ErrorCode.DISPLAY)
    private void switchToView(String viewName) {
        contentLayout.show(directPanel, viewName);
        updateStatusBar(viewName);
        directPanel.revalidate();
        directPanel.repaint();
    }

    private void updateStatusBar(String viewName) {
        switch (viewName) {
            case "report":
                statusBar.setStatus("已切换到报告视图");
                break;
            case "analysis":
                statusBar.setStatus("已切换到分析视图");
                break;
        }
    }

    @HandleException(errorCode = ErrorCode.OPERATION)
    private void cleanup() {
        destroyViews();
        closeServices();
        TableOperations.shutdown();
    }

    private void destroyViews() {
        if (analysisView != null)
            analysisView.destroy();
        if (reportView != null)
            reportView.destroy();
    }

    private void closeServices() {
        if (dicomService != null)
            dicomService.close();
    }

    private static void showErrorDialog(QAToolException ex) {
        MessageDisplayer.showException(null, ex);
    }

    @HandleException(errorCode = ErrorCode.OPERATION)
    public static void main(String[] args) {
        setupUncaughtExceptionHandler();
        SwingUtilities.invokeLater(() -> {
            try {
                new CTQAssuranceTool().setVisible(true);
            } catch (Exception ex) {
                QAToolException qaEx = new QAToolException(ErrorCode.UI_INIT, UIMessages.DISPLAY_ERROR, ex);
                EventBus.getInstance().post(ExceptionEvent.fromException(qaEx));
                System.exit(1);
            }
        });
    }

    private static void setupUncaughtExceptionHandler() {
        Thread.setDefaultUncaughtExceptionHandler((t, throwable) -> {
            QAToolException qaEx = throwable instanceof QAToolException
                    ? (QAToolException) throwable
                    : new QAToolException(ErrorCode.UI_INIT, UIMessages.UNKNOWN_ERROR, throwable);

            SwingUtilities.invokeLater(() -> showErrorDialog(qaEx));
            EventBus.getInstance().post(ExceptionEvent.fromException(qaEx));
        });
    }
}

