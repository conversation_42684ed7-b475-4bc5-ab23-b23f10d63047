package com.ge.med.ct.laf2.theming;

import javax.swing.*;
import javax.swing.table.DefaultTableCellRenderer;
import javax.swing.table.JTableHeader;
import java.awt.*;

/**
 * 表格样式工具类
 * 提供表格样式设置功能
 */
public class FixTableStyle {
    // 颜色相关常量 - 使用ThemeConstants
    private static Color getHeaderBackground() {
        return ThemeConstants.Colors.getTableHeader();
    }

    private static Color getHeaderHighlight() {
        return ThemeUtils.withAlpha(Color.WHITE, 100);
    }

    private static Color getHeaderShadow() {
        return ThemeUtils.darker(getHeaderBackground(), 0.3f);
    }

    private static Color getSelectedBackground() {
        return ThemeConstants.Colors.getTableSelectedRow();
    }

    private static Color getRowBackground() {
        return ThemeConstants.Colors.getTableAlternateRow();
    }

    private static Color getGridColor() {
        return ThemeUtils.brighter(ThemeConstants.Colors.getBorder(), 0.2f);
    }

    // 尺寸相关常量
    private static final int HEADER_HEIGHT = 26;
    private static final int ROW_HEIGHT = 25;

    public static void setupTableStyle(JTable table) {
        if (table == null) {
            return;
        }

        // 设置表头样式
        JTableHeader header = table.getTableHeader();
        header.setBackground(getHeaderBackground());
        header.setForeground(ThemeConstants.Colors.getTextBright());
        header.setFont(ThemeConstants.Fonts.getTableHeader());
        header.setDefaultRenderer(new DefaultTableCellRenderer() {
            @Override
            public Component getTableCellRendererComponent(JTable table, Object value,
                    boolean isSelected, boolean hasFocus, int row, int column) {
                JPanel headerPanel = new JPanel(new BorderLayout()) {
                    @Override
                    protected void paintComponent(Graphics g) {
                        Graphics2D g2 = (Graphics2D) g.create();
                        g2.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);

                        // 绘制背景
                        g2.setColor(getHeaderBackground());
                        g2.fillRect(0, 0, getWidth(), getHeight());

                        // 绘制左上浮雕效果
                        g2.setColor(getHeaderHighlight());
                        g2.drawLine(0, getHeight() - 1, 0, 0); // 左边
                        g2.drawLine(1, getHeight() - 1, 1, 0); // 左边
                        g2.drawLine(0, 0, getWidth() - 1, 0); // 上边

                        // 绘制右下阴影效果
                        g2.setColor(getHeaderShadow());
                        g2.drawLine(getWidth() - 1, 0, getWidth() - 1, getHeight() - 1); // 右边
                        g2.drawLine(0, getHeight() - 1, getWidth() - 1, getHeight() - 1); // 下边

                        g2.dispose();
                        super.paintComponent(g);
                    }
                };

                headerPanel.setOpaque(false);
                JLabel label = new JLabel(value.toString(), SwingConstants.CENTER);
                label.setFont(ThemeConstants.Fonts.getTableHeader());
                label.setForeground(ThemeConstants.Colors.getTextBright());
                headerPanel.add(label, BorderLayout.CENTER);

                return headerPanel;
            }
        });
        header.setPreferredSize(new Dimension(header.getWidth(), HEADER_HEIGHT));

        table.setRowHeight(ROW_HEIGHT);
        table.setShowGrid(false);
        table.setGridColor(getGridColor());
        table.setSelectionMode(ListSelectionModel.SINGLE_SELECTION);
        table.setFont(ThemeConstants.Fonts.getTableContent());

        // 设置单元格渲染器
        DefaultTableCellRenderer renderer = new DefaultTableCellRenderer() {
            @Override
            public Component getTableCellRendererComponent(JTable table, Object value,
                    boolean isSelected, boolean hasFocus, int row, int column) {
                Component c = super.getTableCellRendererComponent(table, value, isSelected, hasFocus, row, column);

                Color bgColor;
                if (isSelected) {
                    bgColor = getSelectedBackground();
                    c.setForeground(ThemeConstants.Colors.getTextBright());
                } else {
                    bgColor = row % 2 == 0 ? Color.WHITE : getRowBackground();
                    c.setForeground(ThemeConstants.Colors.getText());
                }
                c.setBackground(bgColor);

                // 设置所有边框颜色与背景色相同
                if (column == 0) {
                    setHorizontalAlignment(LEFT);
                    setBorder(BorderFactory.createMatteBorder(1, 1, 1, 1, bgColor));
                } else {
                    setHorizontalAlignment(CENTER);
                    setBorder(BorderFactory.createMatteBorder(1, 1, 1, 1, bgColor));
                }

                return c;
            }
        };

        // 应用渲染器到所有列
        for (int i = 0; i < table.getColumnCount(); i++) {
            table.getColumnModel().getColumn(i).setCellRenderer(renderer);
        }

        table.setAutoResizeMode(JTable.AUTO_RESIZE_OFF);
    }
}
