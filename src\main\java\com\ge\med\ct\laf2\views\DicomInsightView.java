package com.ge.med.ct.laf2.views;

import com.ge.med.ct.dicom2.reader.DicomMetadataReader;
import com.ge.med.ct.dicom2.tag.DicomTag;
import com.ge.med.ct.laf2.base.listeners.IDicomInsightViewListener;
import com.ge.med.ct.laf2.base.views.BaseView;
import com.ge.med.ct.laf2.components.CTTable;
import com.ge.med.ct.laf2.components.FlatTreeView;
import com.ge.med.ct.laf2.theming.FixTableStyle;

import javax.swing.*;
import javax.swing.table.DefaultTableModel;
import javax.swing.tree.DefaultMutableTreeNode;
import javax.swing.tree.DefaultTreeModel;
import javax.swing.tree.TreePath;
import java.awt.*;
import java.io.File;
import java.util.List;
import java.util.logging.Level;

/**
 * DICOM信息查看视图
 * 整合了目录树和DICOM信息显示功能
 */
public class DicomInsightView extends BaseView<IDicomInsightViewListener> {
    private static final String[] COLUMN_NAMES = { "Tag", "TagName", "VR", "Length", "Value" };
    private String dicomScanDir;

    private final DicomMetadataReader metadataReader;

    private FlatTreeView tree;
    private CTTable table;

    private DefaultTreeModel treeModel;
    private DefaultMutableTreeNode rootNode;
    private DefaultTableModel tableModel;

    public DicomInsightView() {

        super(JSplitPane.HORIZONTAL_SPLIT);
        setDividerSize(5);

        this.metadataReader = new DicomMetadataReader();

        initializeComponents();
        setupListeners();
    }

    private void initializeComponents() {
        // 配置目录树
        configureTree();

        this.tableModel = new DefaultTableModel(COLUMN_NAMES, 0) {
            @Override
            public boolean isCellEditable(int row, int column) {
                return false;
            }
        };
        this.table = new CTTable(tableModel);
        FixTableStyle.setupTableStyle(table);

        configureTable();

        JPanel leftPanel = new JPanel(new BorderLayout());
        leftPanel.add(new JScrollPane(tree), BorderLayout.CENTER);

        JPanel rightPanel = new JPanel(new BorderLayout());
        rightPanel.add(new JScrollPane(table), BorderLayout.CENTER);

        setLeftComponent(leftPanel);
        setRightComponent(rightPanel);
        setResizeWeight(0.3);
        setContinuousLayout(true);
    }

    private void configureTree() {

        // 初始化目录树
        this.rootNode = new DefaultMutableTreeNode("Files");
        this.treeModel = new DefaultTreeModel(rootNode);
        this.tree = new FlatTreeView(treeModel);

    }

    private void configureTable() {
        table.setAutoResizeMode(JTable.AUTO_RESIZE_ALL_COLUMNS);
        table.setRowHeight(25);
    }

    private void setupListeners() {
        tree.addTreeSelectionListener(e -> {
            TreePath path = e.getPath();
            if (path != null && path.getLastPathComponent() instanceof DefaultMutableTreeNode) {
                clear();

                File selectedFile = getSelectedFile();
                if (selectedFile != null && selectedFile.isFile()) {
                    loadDicomFile(selectedFile);
                }
            }
        });
    }

    public void setDicomScanDir(String dicomScanDir) {
        this.dicomScanDir = dicomScanDir;
    }

    public void loadDirectory() {
        SwingWorker<Void, Object> worker = new SwingWorker<Void, Object>() {
            @Override
            protected Void doInBackground() {
                try {
                    buildDirectoryHierarchy(new File(dicomScanDir));
                } catch (Exception e) {
                    logger.log(Level.SEVERE, "Failed to load directory", e);
                    SwingUtilities.invokeLater(() -> showErrorNode("Failed to load directory: " + e.getMessage()));
                }
                return null;
            }

            @Override
            protected void done() {
                finishLoading();
            }
        };
        worker.execute();
    }

    private void buildDirectoryHierarchy(File directory) {
        rootNode.removeAllChildren();
        buildDirectoryNode(rootNode, directory);
    }

    private void buildDirectoryNode(DefaultMutableTreeNode parentNode, File directory) {
        File[] files = directory.listFiles();
        if (files != null) {
            for (File file : files) {
                DefaultMutableTreeNode node = new DefaultMutableTreeNode(file.getName(), file.isDirectory());
                node.setAllowsChildren(file.isDirectory());
                parentNode.add(node);

                if (file.isDirectory()) {
                    buildDirectoryNode(node, file);
                }
            }
        }
    }

    private void showErrorNode(String message) {
        rootNode.removeAllChildren();
        rootNode.add(new DefaultMutableTreeNode(message));
        treeModel.reload();
    }

    private void finishLoading() {
        if (rootNode.getChildCount() == 0) {
            rootNode.add(new DefaultMutableTreeNode("No files found"));
        }
        treeModel.reload();
        tree.expandPath(new TreePath(rootNode.getPath()));

        // 通知监听器目录已加载
        if (viewListener != null && dicomScanDir != null) {
            int fileCount = countFiles(rootNode);
            viewListener.onDirectoryLoaded(dicomScanDir, fileCount);
        }
    }

    private int countFiles(DefaultMutableTreeNode node) {
        int count = 0;
        if (!node.isLeaf() && !node.isRoot()) {
            count++; // 计算目录本身
        } else if (node.isLeaf() && !node.isRoot()) {
            count++; // 计算文件
        }

        for (int i = 0; i < node.getChildCount(); i++) {
            DefaultMutableTreeNode child = (DefaultMutableTreeNode) node.getChildAt(i);
            count += countFiles(child);
        }

        return count;
    }

    public void loadDicomFile(File file) {
        clear();

        if (file == null || !file.exists()) {
            logger.warning("Cannot load DICOM file: " + (file == null ? "file is null" : "file does not exist"));
            return;
        }

        try {
            List<DicomTag> tags = metadataReader.readAllTags(file.getAbsolutePath());

            if (tags.isEmpty()) {
                logger.warning("No DICOM tags were loaded");
            } else {
                for (DicomTag tag : tags) {
                    tableModel.addRow(new Object[] {
                            tag.getTagId(),
                            tag.getName(),
                            tag.getVr() != null ? tag.getVr().toString() : "",
                            tag.getValueAsString() != null ? tag.getValueAsString().length() : 0,
                            tag.getValueAsString()
                    });
                }

                // 通知监听器DICOM文件已加载
                if (viewListener != null) {
                    viewListener.onDicomFileLoaded(file.getAbsolutePath(), tags.size());
                }
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "Failed to load DICOM file: " + e.getMessage(), e);
        }
    }

    public void clear() {
        while (tableModel.getRowCount() > 0) {
            tableModel.removeRow(0);
        }
    }

    private File getSelectedFile() {
        TreePath path = tree.getSelectionPath();
        if (path != null) {
            DefaultMutableTreeNode node = (DefaultMutableTreeNode) path.getLastPathComponent();
            if (!node.isRoot()) {
                File file = new File(dicomScanDir, getNodePath(node));
                if (!file.exists() || !file.isFile()) {
                    logger.warning("Selected file does not exist or is not a file: " + file.getAbsolutePath());
                }
                return file;
            }
        }
        logger.warning("No file selected or root node selected");
        return null;
    }

    private String getNodePath(DefaultMutableTreeNode node) {
        StringBuilder path = new StringBuilder();
        while (node != null && !node.isRoot()) {
            path.insert(0, node.getUserObject().toString());
            path.insert(0, File.separator);
            node = (DefaultMutableTreeNode) node.getParent();
        }
        return path.toString();
    }

    public void clearContents() {
        clear();
        rootNode.removeAllChildren();
        treeModel.reload();
    }

    @Override
    public void clearView() {
        clearContents();
    }
}