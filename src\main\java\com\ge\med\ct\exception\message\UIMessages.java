package com.ge.med.ct.exception.message;

/**
 * UI消息枚举
 * 定义UI相关的消息
 */
public enum UIMessages implements Message {
    // 基础错误
    UNKNOWN_ERROR("ui.unknown", "未知UI错误"),
    OPERATION_ERROR("ui.operation.error", "UI操作错误: {0}"),

    // 显示相关错误
    DISPLAY_ERROR("ui.display.error", "UI显示错误: {0}"),
    RENDER_ERROR("ui.render.error", "渲染错误: {0}"),

    // 组件相关错误
    COMPONENT_ERROR("ui.component.error", "组件错误: {0}"),
    COMPONENT_NOT_FOUND("ui.component.not.found", "未找到组件: {0}"),
    COMPONENT_INIT_ERROR("ui.component.init.error", "组件初始化错误: {0}"),

    // 导航相关错误
    NAVIGATION_ERROR("ui.navigation.error", "导航错误: {0}"),
    NAVIGATION_FAILED("ui.navigation.failed", "导航失败: {0}"),

    // 输入相关错误
    INPUT_ERROR("ui.input.error", "输入错误: {0}"),
    INPUT_INVALID("ui.input.invalid", "无效的输入: {0}"),

    // 验证相关错误
    VALIDATION_ERROR("ui.validation.error", "UI验证错误: {0}"),
    VALIDATION_FAILED("ui.validation.failed", "UI验证失败: {0}"),

    // 事件相关错误
    EVENT_ERROR("ui.event.error", "事件错误: {0}"),
    EVENT_HANDLING_ERROR("ui.event.handling.error", "事件处理错误: {0}"),

    // 资源相关错误
    RESOURCE_ERROR("ui.resource.error", "资源错误: {0}"),
    RESOURCE_LOADING_ERROR("ui.resource.loading.error", "资源加载错误: {0}"),

    // 线程相关错误
    THREAD_ERROR("ui.thread.error", "UI线程错误: {0}"),
    EDT_VIOLATION("ui.edt.violation", "EDT违规: {0}"),

    // 对话框相关错误
    DIALOG_ERROR("ui.dialog.error", "对话框错误: {0}"),
    DIALOG_CREATION_ERROR("ui.dialog.creation.error", "对话框创建错误: {0}");

    private final AbstractMessage delegate;

    UIMessages(String key, String defaultMessage) {
        this.delegate = new AbstractMessage(key, defaultMessage) {};
    }

    @Override
    public String getKey() {
        return delegate.getKey();
    }

    @Override
    public String getDefaultMessage() {
        return delegate.getDefaultMessage();
    }

    @Override
    public Message format(Object... args) {
        return delegate.format(args);
    }

    @Override
    public String toStr() {
        return delegate.toStr();
    }
}
