package com.ge.med.ct.dicom2.model;

import com.ge.med.ct.dicom2.tag.DicomTag;
import com.ge.med.ct.dicom2.tag.DicomTagConstants;
import com.ge.med.ct.exception.code.ErrorCode;
import com.ge.med.ct.exception.core.DicomException;
import com.ge.med.ct.exception.factory.ExceptionFactory;
import com.ge.med.ct.exception.message.DicomMessages;

import java.util.Collections;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.logging.Logger;

// DICOM文件模型类
public class DicomFileModel {
    private static final Logger LOG = Logger.getLogger(DicomFileModel.class.getName());

    private final String id;
    private String filePath;
    private String fileName;
    private String fileType;
    private long fileSize;
    private DicomImage image;

    // 使用ConcurrentHashMap提高并发访问性能
    private final Map<String, DicomTag> tags;

    public DicomFileModel(String id) throws DicomException {
        if (id == null || id.trim().isEmpty()) {
            throw ExceptionFactory.createDicomException(ErrorCode.DICOM_VALIDATION, DicomMessages.ID_EMPTY);
        }
        this.id = id;
        this.tags = new ConcurrentHashMap<>();
        try {
            this.image = new DicomImage(id);
        } catch (DicomException e) {
            LOG.warning("创建图像失败: " + e.getMessage());
            throw e;
        }
    }

    public String getId() {
        return id;
    }

    public String getFilePath() {
        return filePath;
    }

    public void setFilePath(String filePath) {
        this.filePath = filePath;
        LOG.fine("设置文件路径: " + id + ": " + filePath);
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
        LOG.fine("设置文件名: " + id + ": " + fileName);
    }

    public long getFileSize() {
        return fileSize;
    }

    public void setFileSize(long fileSize) {
        this.fileSize = fileSize;
        LOG.fine("设置文件大小: " + id + ": " + fileSize);
    }

    public String getFileType() {
        return fileType;
    }

    public void setFileType(String fileType) {
        this.fileType = fileType;
        LOG.fine("设置文件类型: " + id + ": " + fileType);
    }

    public DicomTag getTag(String tagId) {
        if (tagId == null) {
            return null;
        }
        return tags.get(tagId.toUpperCase());
    }

    public void addTag(String tagId, DicomTag tag) {
        if (tagId != null && tag != null) {
            tags.put(tagId.toUpperCase(), tag);
            LOG.fine("添加标签 " + tagId + " 到文件 " + id);

            // 如果是像素数据标签，同时添加到图像对象
            if (tagId.equals(DicomTagConstants.Image.PIXEL_DATA) && image != null) {
                try {
                    // 如果像素数据为二进制数据，添加到图像
                    byte[] pixelData = tag.getValueAsBytes();
                    if (pixelData != null) {
                        image.setPixelData(pixelData);
                    }
                } catch (Exception e) {
                    LOG.warning("设置像素数据失败: " + e.getMessage());
                }
            }
        }
    }

    public void addTag(String tagId, byte[] value) {
        if (tagId != null && value != null) {
            try {
                tagId = tagId.toUpperCase();
                DicomTag tag = new DicomTag(tagId, new String(value));
                tags.put(tagId, tag);
                LOG.fine("添加字节数组值标签 " + tagId + " 到文件 " + id);

                // 特殊处理像素数据，不直接添加到标签映射
                if (tagId.equals(DicomTagConstants.Image.PIXEL_DATA) && image != null) {
                    try {
                        image.setPixelData(value);
                    } catch (Exception e) {
                        LOG.warning("设置像素数据失败: " + e.getMessage());
                    }
                }
            } catch (DicomException e) {
                LOG.warning("添加标签失败 " + tagId + ": " + e.getMessage());
            }
        }
    }

    // 获取标签的字节数组值
    public byte[] getTagBytes(String tagId) {
        DicomTag tag = getTag(tagId);
        if (tag == null) {
            return null;
        }

        // 使用DicomTag的getValueAsBytes方法获取字节值
        byte[] bytes = tag.getValueAsBytes();
        if (bytes == null) {
            LOG.warning("无法获取标签" + tagId + "的字节数组值");
        }
        return bytes;
    }

    public String getTagValueAsString(String tagId) {
        DicomTag tag = getTag(tagId);
        return tag != null ? tag.getValueAsString() : null;
    }

    /**
     * 获取标签值（适用于像素数据等二进制值）
     *
     * @param tagId 标签ID
     * @return 标签值对象（可能是二进制数据或其他类型）
     */
    public Object getTagValue(String tagId) {
        DicomTag tag = getTag(tagId);
        if (tag != null) {
            // 对于特殊的Tag.PixelData标签，返回byte[]值
            if (tagId.equals(DicomTagConstants.Image.PIXEL_DATA)) {
                try {
                    // 尝试获取原始字节值
                    return tag.getRawValue();
                } catch (Exception e) {
                    LOG.warning("获取像素数据失败: " + e.getMessage());
                    return null;
                }
            }
            return tag.getValueAsString();
        }
        return null;
    }

    /**
     * 获取标签的整数值
     */
    public Integer getTagValueAsInteger(String tagId) {
        DicomTag tag = getTag(tagId);
        return tag != null ? tag.getValueAsInteger() : null;
    }

    /**
     * 获取标签的浮点值
     */
    public Float getTagValueAsFloat(String tagId) {
        DicomTag tag = getTag(tagId);
        return tag != null ? tag.getValueAsFloat() : null;
    }

    /**
     * 获取标签的双精度浮点值
     */
    public Double getTagValueAsDouble(String tagId) {
        DicomTag tag = getTag(tagId);
        return tag != null ? tag.getValueAsDouble() : null;
    }

    // DICOM标签访问方法
    /**
     * 获取研究实例UID
     */
    public String getStudyInstanceUID() {
        return getTagValueAsString(DicomTagConstants.Study.STUDY_INSTANCE_UID);
    }

    /**
     * 获取序列实例UID
     */
    public String getSeriesInstanceUID() {
        return getTagValueAsString(DicomTagConstants.Series.SERIES_INSTANCE_UID);
    }

    /**
     * 获取SOP实例UID
     */
    public String getSOPInstanceUID() {
        return getTagValueAsString(DicomTagConstants.Image.SOP_INSTANCE_UID);
    }

    /**
     * 获取患者ID
     */
    public String getPatientId() {
        return getTagValueAsString(DicomTagConstants.Patient.PATIENT_ID);
    }

    /**
     * 获取患者姓名
     */
    public String getPatientName() {
        return getTagValueAsString(DicomTagConstants.Patient.PATIENT_NAME);
    }

    /**
     * 获取研究日期
     */
    public String getStudyDate() {
        return getTagValueAsString(DicomTagConstants.Study.STUDY_DATE);
    }

    /**
     * 获取序列日期
     */
    public String getSeriesDate() {
        return getTagValueAsString(DicomTagConstants.Series.SERIES_DATE);
    }

    /**
     * 获取研究时间
     */
    public String getStudyTime() {
        return getTagValueAsString(DicomTagConstants.Study.STUDY_TIME);
    }

    /**
     * 获取入院号
     */
    public String getAccessionNumber() {
        return getTagValueAsString(DicomTagConstants.Study.ACCESSION_NUMBER);
    }

    /**
     * 获取模态
     */
    public String getModality() {
        return getTagValueAsString(DicomTagConstants.Series.MODALITY);
    }

    /**
     * 获取序列描述
     */
    public String getSeriesDescription() {
        return getTagValueAsString(DicomTagConstants.Series.SERIES_DESCRIPTION);
    }

    /**
     * 获取序列号
     */
    public Integer getSeriesNumber() {
        return getTagValueAsInteger(DicomTagConstants.Series.SERIES_NUMBER);
    }

    /**
     * 获取实例号
     */
    public Integer getInstanceNumber() {
        return getTagValueAsInteger(DicomTagConstants.Image.INSTANCE_NUMBER);
    }

    /**
     * 获取行数
     */
    public Integer getRows() {
        return getTagValueAsInteger(DicomTagConstants.Image.ROWS);
    }

    /**
     * 获取列数
     */
    public Integer getColumns() {
        return getTagValueAsInteger(DicomTagConstants.Image.COLUMNS);
    }

    /**
     * 获取图像对象
     */
    public DicomImage getImage() {
        return image;
    }

    /**
     * 设置图像对象
     */
    public void setImage(DicomImage image) {
        this.image = image;
        LOG.fine("设置图像: " + id);
    }

    /**
     * 获取所有标签（不可修改）
     */
    public Map<String, DicomTag> getTags() {
        return Collections.unmodifiableMap(tags);
    }

    /**
     * 清除像素数据，释放内存
     */
    public void clearPixelData() {
        DicomTag pixelTag = tags.get(DicomTagConstants.Image.PIXEL_DATA);
        if (pixelTag != null) {
            tags.remove(DicomTagConstants.Image.PIXEL_DATA);
        }

        if (image != null) {
            image.clearPixelData();
        }
    }

    @Override
    public String toString() {
        return String.format("%s[id=%s, filePath=%s, fileName=%s, fileSize=%d, fileType=%s, tags=%d]",
                getClass().getSimpleName(), id, filePath, fileName, fileSize, fileType, tags.size());
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null || getClass() != obj.getClass())
            return false;
        DicomFileModel other = (DicomFileModel) obj;
        return id != null && id.equals(other.id);
    }

    @Override
    public int hashCode() {
        return id != null ? id.hashCode() : 0;
    }
}