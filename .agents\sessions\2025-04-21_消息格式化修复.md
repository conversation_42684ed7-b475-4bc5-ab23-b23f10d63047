# 消息格式化修复会话记录

## 第一部分：备用格式化机制

### 会话概述

本次会话主要围绕消息格式化功能进行修复，解决了在异常处理过程中消息占位符未被正确替换的问题。

### 问题描述

在执行分析命令时，当发生 IO 异常时，错误消息显示为 "IO错误: {0}"，其中占位符 "{0}" 没有被实际的错误信息替换。这导致用户无法获知具体的错误原因。

### 问题分析

通过分析代码，发现问题出在 `MessageBuilder.formatMessageText` 方法中。当使用 `MessageFormat.format` 进行格式化时，如果格式化失败（例如，参数类型不匹配），该方法会直接返回原始模板，而不是尝试其他方式替换占位符。

具体来说，在 `CommandInvoker.java` 文件中，当发生 IO 异常时，使用 `CommonMessages.IO_ERROR.format(errorMsg)` 来格式化错误消息，但如果格式化失败，就会导致错误消息中仍然显示 `{0}` 占位符。

### 修复方案

修改 `MessageBuilder.formatMessageText` 方法，添加一个备用的占位符替换机制：

1. 首先尝试使用 `MessageFormat.format` 进行格式化
2. 如果格式化失败，使用简单的字符串替换方法，直接替换占位符 `{0}`, `{1}` 等

修改后的代码：

```java
private String formatMessageText(String template, Object... args) {
    if (args == null || args.length == 0) {
        return template;
    }

    try {
        return MessageFormat.format(template, args);
    } catch (IllegalArgumentException e) {
        // 如果格式化失败，尝试简单替换占位符
        StringBuilder result = new StringBuilder(template);
        for (int i = 0; i < args.length; i++) {
            String placeholder = "{" + i + "}";
            int index = result.indexOf(placeholder);
            if (index != -1) {
                result.replace(index, index + placeholder.length(), String.valueOf(args[i]));
            }
        }
        return result.toString();
    }
}
```

### 修复效果

修复后，即使 `MessageFormat.format` 格式化失败，也能通过备用的替换机制确保占位符被正确替换为实际的错误信息。这样用户就能看到完整的错误消息，例如 "IO错误: 无法访问文件"，而不是 "IO错误: {0}"。

### 结论

通过本次修复，解决了消息格式化过程中的占位符替换问题，提高了错误消息的可读性和有用性。这对于用户理解和解决问题非常重要，特别是在异常处理和错误报告方面。

同时，这个修复也提醒我们在处理格式化和模板替换时，应该考虑各种可能的失败情况，并提供合适的备用方案，以确保用户体验的一致性和可靠性。

## 第二部分：修复双重格式化问题

### 会话概述

本次会话主要围绕消息格式化功能进行进一步修复，解决了在异常处理过程中消息占位符未被正确替换的问题。

### 问题描述

在执行分析命令时，当发生 IO 异常时，错误消息显示为 "IO错误: {0}"，其中占位符 "{0}" 没有被实际的错误信息替换。这导致用户无法获知具体的错误原因。

### 问题分析

通过深入分析代码，发现问题出在异常创建和消息格式化的调用链上。在 `CommandInvoker.java` 和 `RetryStrategy.java` 文件中，我们使用了 `CommonMessages.IO_ERROR.format(errorMsg)` 来格式化消息，然后将格式化后的消息传递给 `ExceptionFactory.createException` 方法。但是，在 `QAToolException.Builder` 类的 `build()` 方法中，它会再次调用 `message.format(messageArgs).toStr()` 来格式化消息，这导致了双重格式化，可能是问题的根源。

### 修复方案

修改 `CommandInvoker.java` 和 `RetryStrategy.java` 文件中的异常创建代码，不再预先格式化消息，而是将原始消息和参数传递给 `ExceptionFactory.createException` 方法，让异常类内部进行格式化。

例如，将：

```java
throw ExceptionFactory.createException(ErrorCode.SYS_EXTERNAL, CommonMessages.IO_ERROR.format(errorMsg), e);
```

修改为：

```java
throw ExceptionFactory.createException(ErrorCode.SYS_EXTERNAL, CommonMessages.IO_ERROR, e, errorMsg);
```

这样，消息格式化只会在 `QAToolException.Builder` 类的 `build()` 方法中进行一次，避免了双重格式化的问题。

### 修复内容

1. 修复了 `CommandInvoker.java` 文件中的异常创建代码：
   - 修复了 IO 异常的处理
   - 修复了线程中断异常的处理
   - 修复了资源不存在异常的处理
   - 修复了权限不足异常的处理

2. 修复了 `RetryStrategy.java` 文件中的异常创建代码：
   - 修复了线程中断异常的处理
   - 修复了处理错误异常的处理

### 修复效果

修复后，当发生 IO 异常时，错误消息将正确显示为 "IO错误: 执行IO异常: xxx"，而不是 "IO错误: {0}"。这样用户就能看到完整的错误消息，更容易理解和解决问题。

### 结论

通过本次修复，解决了消息格式化过程中的占位符替换问题，提高了错误消息的可读性和有用性。这对于用户理解和解决问题非常重要，特别是在异常处理和错误报告方面。

同时，这个修复也提醒我们在处理异常和消息格式化时，应该注意避免双重格式化的问题，确保消息格式化只在一个地方进行，以保持一致性和可靠性。
