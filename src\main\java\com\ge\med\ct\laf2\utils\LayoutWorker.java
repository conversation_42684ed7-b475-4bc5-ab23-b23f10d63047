package com.ge.med.ct.laf2.utils;

import java.awt.*;

/**
 * 布局工作器
 * 使用构建器模式简化GridBagConstraints的设置
 */
public class LayoutWorker {
    // 默认内边距
    public static final Insets DEFAULT_INSETS = new Insets(5, 5, 5, 5);
    
    private final Container container;
    private final GridBagConstraints constraints;
    
    /**
     * 构造函数
     * @param container 要添加组件的容器
     */
    public LayoutWorker(Container container) {
        this.container = container;
        this.constraints = new GridBagConstraints();
        this.constraints.insets = DEFAULT_INSETS;
    }
    
    /**
     * 设置网格位置
     * @param gridx 水平位置
     * @param gridy 垂直位置
     * @return this对象，用于链式调用
     */
    public LayoutWorker position(int gridx, int gridy) {
        constraints.gridx = gridx;
        constraints.gridy = gridy;
        return this;
    }
    
    /**
     * 设置网格大小
     * @param gridwidth 水平跨度
     * @param gridheight 垂直跨度
     * @return this对象，用于链式调用
     */
    public LayoutWorker size(int gridwidth, int gridheight) {
        constraints.gridwidth = gridwidth;
        constraints.gridheight = gridheight;
        return this;
    }
    
    /**
     * 设置权重
     * @param weightx 水平权重
     * @param weighty 垂直权重
     * @return this对象，用于链式调用
     */
    public LayoutWorker weight(double weightx, double weighty) {
        constraints.weightx = weightx;
        constraints.weighty = weighty;
        return this;
    }
    
    /**
     * 设置填充方式
     * @param fill 填充方式
     * @return this对象，用于链式调用
     */
    public LayoutWorker fill(int fill) {
        constraints.fill = fill;
        return this;
    }
    
    /**
     * 设置锚点
     * @param anchor 锚点
     * @return this对象，用于链式调用
     */
    public LayoutWorker anchor(int anchor) {
        constraints.anchor = anchor;
        return this;
    }
    
    /**
     * 设置内边距
     * @param insets 内边距
     * @return this对象，用于链式调用
     */
    public LayoutWorker insets(Insets insets) {
        constraints.insets = insets;
        return this;
    }
    
    /**
     * 添加组件
     * @param component 要添加的组件
     */
    public void add(Component component) {
        container.add(component, constraints);
    }
    
    /**
     * 获取当前约束的副本
     * @return 约束副本
     */
    public GridBagConstraints getConstraints() {
        return (GridBagConstraints) constraints.clone();
    }
}
