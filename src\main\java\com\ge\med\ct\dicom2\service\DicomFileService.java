package com.ge.med.ct.dicom2.service;

import java.io.File;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;
import java.util.logging.Logger;

import com.ge.med.ct.cfg.ConfigManager;
import com.ge.med.ct.cfg.dicom.DicomConfigService;
import com.ge.med.ct.dicom2.model.DicomFileModel;
import com.ge.med.ct.dicom2.reader.DicomMetadataReader;
import com.ge.med.ct.dicom2.utils.DicomValidationHandler;
import com.ge.med.ct.dicom2.utils.PreScanResult;
import com.ge.med.ct.dicom2.utils.ProcessingResult;
import com.ge.med.ct.exception.aspect.HandleException;
import com.ge.med.ct.exception.code.ErrorCode;
import com.ge.med.ct.exception.core.DicomException;
import com.ge.med.ct.exception.message.DicomMessages;
import com.ge.med.ct.service.LogManager;

/** DICOM文件服务 - 负责文件读取、验证和处理 */
@HandleException(errorCode = ErrorCode.PROCESSING)
public class DicomFileService implements AutoCloseable {
    private static final Logger LOG = LogManager.getInstance().getLogger(DicomFileService.class);
    private static volatile DicomFileService instance;

    private final String rootDirectory;
    private final DicomMetadataReader metadataReader;
    private final ExecutorService executor;
    private final DicomValidationHandler validationHandler;
    private final DicomFileScanner fileScanner;

    private final List<Consumer<List<String>>> progressListeners = new ArrayList<>();

    private DicomFileService() {
        ConfigManager configManager = ConfigManager.getInstance();
        DicomConfigService dicomConfig = configManager.getDicomConfig();
        this.rootDirectory = dicomConfig.getRootDirectory();

        this.metadataReader = new DicomMetadataReader();
        int maxThreads = configManager.getInt("dicom.max.threads", 4);
        this.executor = Executors.newFixedThreadPool(maxThreads, r -> {
            Thread thread = new Thread(r, "DicomFileService-Worker");
            thread.setDaemon(true);
            return thread;
        });

        DicomFileValidator validationService = DicomFileValidator.getInstance();
        boolean skipInvalidFiles = configManager.getBoolean("dicom.validation.skip_invalid", true);
        this.validationHandler = new DicomValidationHandler(configManager, validationService, skipInvalidFiles);
        this.fileScanner = new DicomFileScanner();

        LOG.info("DICOM文件服务初始化完成: 根目录=" + rootDirectory);
    }

    public static DicomFileService getInstance() {
        if (instance == null) {
            synchronized (DicomFileService.class) {
                if (instance == null) {
                    instance = new DicomFileService();
                }
            }
        }
        return instance;
    }

    public String getRootDirectory() {
        return rootDirectory;
    }

    public DicomFileModel readFile(String filePath) throws DicomException {
        DicomFileValidator.validateFilePath(filePath);
        try {
            return metadataReader.readMetadata(filePath);
        } catch (Exception e) {
            throw new DicomException(ErrorCode.READ, DicomMessages.FILE_READ_ERROR, e, filePath);
        }
    }

    public List<String> scanDirectory(String directory, boolean recursive) throws DicomException {
        String scanPath = directory != null ? directory : rootDirectory;
        List<String> files = fileScanner.scanDirectory(scanPath, recursive);
        notifyProgressListeners(files);
        return files;
    }

    /**
     * 预扫描目录，按Exam和Series分组
     *
     * @param directory 目录路径
     * @param recursive 是否递归扫描
     * @return 预扫描结果
     * @throws DicomException 如果扫描失败
     */
    public PreScanResult preScanDirectory(String directory, boolean recursive) throws DicomException {
        String scanPath = directory != null ? directory : rootDirectory;
        return fileScanner.preScanDirectory(scanPath, recursive);
    }

    /**
     * 验证DICOM文件
     *
     * @param filePath DICOM文件路径
     * @return DICOM文件模型
     * @throws DicomException 如果验证失败
     */
    public DicomFileModel validateFile(String filePath) throws DicomException {
        // 读取文件
        DicomFileModel model = readFile(filePath);

        // 使用验证处理器进行验证
        validationHandler.validateFile(filePath, model);

        return model;
    }

    /**
     * 批量处理DICOM文件
     *
     * @param filePaths 文件路径列表
     * @return 处理结果
     */
    @HandleException(errorCode = ErrorCode.PROCESSING)
    public ProcessingResult processFiles(List<String> filePaths) {
        if (filePaths == null || filePaths.isEmpty()) {
            return new ProcessingResult(new ArrayList<>(), new ArrayList<>());
        }

        List<DicomFileModel> successfulModels = new ArrayList<>();
        List<String> failedFiles = new ArrayList<>();

        for (String filePath : filePaths) {
            try {
                DicomFileModel model = validateFile(filePath);
                successfulModels.add(model);
            } catch (Exception e) {
                failedFiles.add(filePath);
                String fileName = new File(filePath).getName();
                LOG.warning("处理文件失败: " + fileName + ", 原因: " + e.getMessage());
            }
        }
        return new ProcessingResult(successfulModels, failedFiles);
    }

    /**
     * 异步处理DICOM文件列表
     *
     * @param filePaths        文件路径列表
     * @param progressCallback 进度回调
     * @return 处理结果的Future
     */
    public CompletableFuture<ProcessingResult> processFilesAsync(List<String> filePaths,
            Consumer<String> progressCallback) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                if (progressCallback != null) {
                    progressCallback.accept("开始处理 " + filePaths.size() + " 个文件...");
                }
                ProcessingResult result = processFiles(filePaths);
                if (progressCallback != null) {
                    progressCallback
                            .accept("处理完成: 成功 " + result.getSuccessCount() + ", 失败 " + result.getFailureCount());
                }
                return result;
            } catch (Exception e) {
                LOG.warning("异步处理文件失败: " + e.getMessage());
                throw new RuntimeException(e);
            }
        }, executor);
    }

    public void addProgressListener(Consumer<List<String>> listener) {
        if (listener != null) {
            progressListeners.add(listener);
        }
    }

    public void removeProgressListener(Consumer<List<String>> listener) {
        progressListeners.remove(listener);
    }

    private void notifyProgressListeners(List<String> files) {
        for (Consumer<List<String>> listener : progressListeners) {
            try {
                listener.accept(files);
            } catch (Exception e) {
                LOG.warning("通知监听器失败: " + e.getMessage());
            }
        }
    }

    @Override
    public void close() {
        LOG.info("关闭DICOM文件服务");
        if (executor != null && !executor.isShutdown()) {
            executor.shutdown();
            try {
                if (!executor.awaitTermination(30, TimeUnit.SECONDS)) {
                    executor.shutdownNow();
                }
            } catch (InterruptedException e) {
                executor.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
    }
}
