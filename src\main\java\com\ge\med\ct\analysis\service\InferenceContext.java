package com.ge.med.ct.analysis.service;

import java.util.Map;
import java.util.HashMap;

/**
 * 推断上下文
 * 存储协议推断过程中的所有相关信息
 */
public class InferenceContext {
    private String seriesDescription;
    private String configVariant;
    private Map<String, Object> dicomTags;
    private Map<String, Object> additionalData;
    
    public InferenceContext() {
        this.dicomTags = new HashMap<>();
        this.additionalData = new HashMap<>();
    }
    
    /**
     * 获取序列描述
     * @return 序列描述
     */
    public String getSeriesDescription() {
        return seriesDescription;
    }
    
    /**
     * 设置序列描述
     * @param seriesDescription 序列描述
     */
    public void setSeriesDescription(String seriesDescription) {
        this.seriesDescription = seriesDescription;
    }
    
    /**
     * 获取配置文件变体
     * @return 配置文件变体
     */
    public String getConfigVariant() {
        return configVariant;
    }
    
    /**
     * 设置配置文件变体
     * @param configVariant 配置文件变体
     */
    public void setConfigVariant(String configVariant) {
        this.configVariant = configVariant;
    }
    
    /**
     * 获取DICOM标签映射
     * @return DICOM标签映射
     */
    public Map<String, Object> getDicomTags() {
        return dicomTags;
    }
    
    /**
     * 设置DICOM标签映射
     * @param dicomTags DICOM标签映射
     */
    public void setDicomTags(Map<String, Object> dicomTags) {
        if (dicomTags != null) {
            this.dicomTags = dicomTags;
        }
    }
    
    /**
     * 获取DICOM标签值
     * @param tagName 标签名
     * @return 标签值
     */
    public Object getDicomTag(String tagName) {
        return dicomTags.get(tagName);
    }
    
    /**
     * 设置DICOM标签值
     * @param tagName 标签名
     * @param value 标签值
     */
    public void setDicomTag(String tagName, Object value) {
        dicomTags.put(tagName, value);
    }
    
    /**
     * 获取附加数据值
     * @param key 键
     * @return 值
     */
    public Object getAdditionalData(String key) {
        return additionalData.get(key);
    }
    
    /**
     * 设置附加数据值
     * @param key 键
     * @param value 值
     */
    public void setAdditionalData(String key, Object value) {
        additionalData.put(key, value);
    }
    
    /**
     * 获取大写的序列描述
     * @return 大写的序列描述
     */
    public String getUpperSeriesDescription() {
        return seriesDescription != null ? seriesDescription.toUpperCase() : "";
    }
} 