package com.ge.med.ct.laf2.components;

import com.ge.med.ct.laf2.theming.ThemeConstants;

import javax.swing.*;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;
import java.util.ArrayList;
import java.util.List;

/**
 * 自定义按钮组件
 */
public class CTButton extends JButton {
    private static final long serialVersionUID = 1L;

    // 按钮状态
    private boolean isHovered = false;
    private boolean isPressed = false;

    private Color textColor = Color.WHITE;
    // 按钮类型
    public enum ButtonType {
        PRIMARY, SECONDARY, DANGER, SUCCESS
    }

    // 事件监听器列表
    private final List<ActionListener> actionListeners = new ArrayList<>();

    public CTButton() {
        this("");
    }

    public CTButton(String text) {
        super(text);
        setupButton();
    }

    private void setupButton() {
        setOpaque(false);
        setFocusPainted(false);
        setContentAreaFilled(false);
        setBorderPainted(false);
        setFont(ThemeConstants.Fonts.getDefault());
        setForeground(ThemeConstants.Colors.getText());

        // 设置边距
        setBorder(BorderFactory.createCompoundBorder(
                BorderFactory.createEmptyBorder(1, 1, 1, 1),
                BorderFactory.createEmptyBorder(4, 10, 4, 10)));

        // 添加鼠标事件监听器
        addMouseListener(new MouseAdapter() {
            @Override
            public void mouseEntered(MouseEvent e) {
                isHovered = true;
                repaint();
            }

            @Override
            public void mouseExited(MouseEvent e) {
                isHovered = false;
                repaint();
            }

            @Override
            public void mousePressed(MouseEvent e) {
                isPressed = true;
                repaint();
            }

            @Override
            public void mouseReleased(MouseEvent e) {
                isPressed = false;
                repaint();
            }
        });
    }

    /**
     * 设置按钮类型
     */
    public void setButtonType(ButtonType type) {
        switch (type) {
            case PRIMARY:
                textColor = Color.WHITE;
                break;
            case SECONDARY:
                textColor = ThemeConstants.Colors.getText();
                break;
            case DANGER:
                textColor = Color.WHITE;
                break;
            case SUCCESS:
                textColor = Color.WHITE;
                break;
        }

        setForeground(textColor);
        repaint();
    }

    @Override
    protected void paintComponent(Graphics g) {
        Graphics2D g2 = (Graphics2D) g.create();
        g2.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);

        Color buttonColor;
        if (!isEnabled()) {
            buttonColor = new Color(ThemeConstants.Colors.getButtonBackground().getRed(),
                                    ThemeConstants.Colors.getButtonBackground().getGreen(),
                                    ThemeConstants.Colors.getButtonBackground().getBlue(), 150);
        } else if (isPressed) {
            buttonColor = ThemeConstants.Colors.getSecondary();
        } else if (isHovered) {
            buttonColor = ThemeConstants.Colors.getPrimary();
        } else {
            buttonColor = ThemeConstants.Colors.getButtonBackground();
        }

        g2.setColor(buttonColor);
        g2.fillRect(1, 1, getWidth() - 2, getHeight() - 2);

        g2.setColor(ThemeConstants.Colors.getButtonBorder());
        g2.drawRect(0, 0, getWidth() - 1, getHeight() - 1);

        g2.setColor(isPressed ? ThemeConstants.Colors.getButtonBorder().darker() : Color.WHITE);
        g2.drawLine(1, 1, getWidth() - 2, 1); // 顶部高光
        g2.drawLine(1, 1, 1, getHeight() - 2); // 左侧高光

        g2.setColor(isPressed ? Color.WHITE : ThemeConstants.Colors.getButtonBorder().darker());
        g2.drawLine(getWidth() - 2, 1, getWidth() - 2, getHeight() - 2); // 右侧阴影
        g2.drawLine(1, getHeight() - 2, getWidth() - 2, getHeight() - 2); // 底部阴影

        g2.dispose();

        super.paintComponent(g);
    }

    protected void fireActionPerformed() {
        ActionEvent event = new ActionEvent(this, ActionEvent.ACTION_PERFORMED, getActionCommand());

        for (ActionListener listener : actionListeners) {
            listener.actionPerformed(event);
        }

        super.fireActionPerformed(event);
    }

    @Override
    public void addActionListener(ActionListener listener) {
        super.addActionListener(listener);
        actionListeners.add(listener);
    }

    @Override
    public void removeActionListener(ActionListener listener) {
        super.removeActionListener(listener);
        actionListeners.remove(listener);
    }

    public void setSmall() {
        setPreferredSize(ThemeConstants.Sizes.getSmallButtonSize());
        setFont(ThemeConstants.Fonts.getSmall());
    }

    public void setLarge() {
        setPreferredSize(ThemeConstants.Sizes.getLargeButtonSize());
        setFont(ThemeConstants.Fonts.getTitle());
    }

    @Override
    public Dimension getPreferredSize() {
        Dimension size = super.getPreferredSize();
        return new Dimension(Math.max(size.width, 80), Math.max(size.height, 24));
    }
}