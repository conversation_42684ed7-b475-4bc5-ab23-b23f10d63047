package com.ge.med.ct.laf2.presenter;

import com.ge.med.ct.analysis.model.AnalysisResult;
import com.ge.med.ct.laf2.base.MessageType;
import com.ge.med.ct.laf2.base.listeners.IReportViewListener;
import com.ge.med.ct.laf2.base.views.IReportView;
import com.ge.med.ct.laf2.components.CTListView;
import com.ge.med.ct.laf2.utils.ReportFormatter;
import com.ge.med.ct.service.LogManager;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * 报告视图的Presenter，负责协调Model和View
 */
public class ReportPresenter implements IReportViewListener {
    private static final Logger logger = LogManager.getInstance().getLogger(ReportPresenter.class);

    private final IReportView view;
    private final Map<String, AnalysisResult> responseMap = new HashMap<>();

    /**
     * 创建报告视图Presenter
     * 
     * @param view 报告视图
     */
    public ReportPresenter(IReportView view) {
        this.view = Objects.requireNonNull(view, "View cannot be null");

        // 设置视图监听器
        view.setViewListener(this);
    }

    @Override
    public void onCheckItemSelected(CTListView.ListItem item) {
        if (item == null || !responseMap.containsKey(item.getId())) {
            logger.warning("选中的项目无效或不存在: " + (item != null ? item.getId() : "null"));
            return;
        }

        AnalysisResult response = responseMap.get(item.getId());
        logger.info("选中报告项: " + item.getId() + ", 报告文件: " + response.getReportFile());

        // 设置加载状态
        view.setLoading(true);

        try {
            // 检查报告文件
            File reportFile = new File(response.getReportFile());
            if (!reportFile.exists()) {
                logger.warning("报告文件不存在: " + reportFile.getAbsolutePath());
                view.showMessage(MessageType.ERROR, "报告文件不存在: " + reportFile.getName());
                return;
            }
            
            if (reportFile.length() == 0) {
                logger.warning("报告文件为空: " + reportFile.getAbsolutePath());
                view.showMessage(MessageType.WARNING, "报告文件为空");
                return;
            }
            
            logger.info("报告文件有效，大小: " + reportFile.length() + " 字节");
            
            // 加载报告内容
            String reportContent = loadReportContent(response.getReportFile());
            if (reportContent == null || reportContent.trim().isEmpty()) {
                view.showMessage(MessageType.WARNING, "Report content is empty");
                return;
            }
            
            // 显示报告前记录
            logger.info("准备显示报告，内容长度: " + reportContent.length());
            
            // 显示报告
            view.displayReport(reportContent);
            logger.info("报告已发送到视图显示");

            // 加载图像
            if (response.getImagePaths() != null && !response.getImagePaths().isEmpty()) {
                logger.info("加载图像，数量: " + response.getImagePaths().size());
                view.loadImages(response.getImagePaths());
            }

            // 设置数据状态
            view.setHasData(true);
            logger.info("报告加载完成并显示成功");
            
        } catch (Exception e) {
            logger.log(Level.SEVERE, "加载报告异常", e);
            view.showMessage(MessageType.ERROR, "加载报告失败: " + e.getMessage());
        } finally {
            // 清除加载状态
            view.setLoading(false);
        }
    }

    @Override
    public void onReportExportRequested(String format) {
        // 实现报告导出功能
        view.showMessage(MessageType.INFO, "报告导出功能尚未实现");
    }

    @Override
    public void onMessage(MessageType type, String message) {
        logger.info("Message: " + type + " - " + message);
    }

    @Override
    public void onViewInitialized() {
        logger.info("View initialized");
    }

    @Override
    public void onViewDestroyed() {
        logger.info("View destroyed");
    }

    /**
     * 添加检查项
     * 
     * @param title    标题
     * @param response 分析结果
     */
    public void addCheckItem(String title, AnalysisResult response) {
        String itemId = "item_" + System.currentTimeMillis();
        responseMap.put(itemId, response);
        logger.info("添加检查项: " + itemId + ", 标题: " + title + ", 报告文件: " + response.getReportFile());
        view.addCheckItem(itemId, title, response);
    }

    /**
     * 加载报告内容
     * 
     * @param reportFile 报告文件路径
     * @return 报告内容
     */
    private String loadReportContent(String reportFile) {
        if (reportFile == null || reportFile.trim().isEmpty()) {
            logger.warning("报告文件路径为空");
            return "No report file available";
        }

        StringBuilder content = new StringBuilder();
        try (BufferedReader reader = new BufferedReader(new FileReader(reportFile))) {
            String line;
            while ((line = reader.readLine()) != null) {
                content.append(line).append("\n");
            }
            
            if (content.length() == 0) {
                return "Report file is empty";
            }

            // 获取格式化内容
            String formatted = ReportFormatter.format(content.toString());
            
            if (formatted == null || formatted.trim().isEmpty()) {
                logger.warning("格式化后报告内容为空");
                return content.toString(); // 返回原始内容作为后备
            }
            
            return formatted;
            
        } catch (Exception e) {
            logger.log(Level.WARNING, "加载报告文件失败: " + reportFile, e);
            return "Failed to load report: " + e.getMessage();
        }
    }

    /**
     * 清除所有检查项
     */
    public void clearCheckItems() {
        responseMap.clear();
        view.clearView();
        logger.info("清除所有检查项");
    }
}
