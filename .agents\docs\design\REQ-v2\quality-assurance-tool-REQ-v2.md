# SRS for CT Image Quality Assurance Tool REQ v2

## 1. System Overview

### 1.1 Purpose

本系统用于 CT 设备的质量检查，提供全面的质量评估工具和报告功能。

### 1.2 Core Features

| 编号   | 需求描述                                       | 优先级 |
| ------ | ---------------------------------------------- | ------ |
| CORE-1 | 系统应支持通过扫描图像数据分析评估设备性能指标 | 高     |
| CORE-2 | 系统应支持多种检查标准（GEHC/ISO）和多种协议   | 高     |
| CORE-3 | 系统应提供检查结果分析和报告生成功能           | 高     |
| CORE-4 | 系统应实现检查记录的统一管理和历史追溯         | 中     |

## 2. Functional Requirements

### 2.1 Home Page

| 编号   | 功能模块           | 需求描述                                                                                                                                  |
| ------ | ------------------ | ----------------------------------------------------------------------------------------------------------------------------------------- |
| HOME-1 | 顶部导航栏         | 包含“Home”、“Report”、“Configuration”、“History”四个标签页，风格统一                                                                      |
| HOME-2 | 标题栏             | 左上角显示系统标题“Quality Assurance Tool”，右上角有最小化、还原、关闭按钮                                                                |
| HOME-3 | Test Options       | 左侧操作区，分组框内下拉选择“Test Type”（Acceptance/Regular）、“Test Standards”（GEHC/ISO）                                               |
| HOME-4 | Analysis Protocols | Analysis Protocols 分组框，复选框选择“LCD”、“CT number”、“NOISE”、“UNIFORMITY”协议                                                        |
| HOME-5 | Scan Query         | Scan Query 分组框，输入“Scan Id”、“Scan Record Name”，支持查询，Scan Record Name 格式可在配置页设置，左侧选择项选中后自动拼接 name 并查询 |
| HOME-6 | Scan Records       | Scan Records 分组框，显示检查记录表格（ID、Exam ID、Name），支持选择                                                                      |
| HOME-7 | Series List        | 右侧主内容区，显示序列列表（ID、Description、Image Count），支持相关操作                                                                  |
| HOME-8 | 操作按钮           | Analysis 按钮，位于主内容区下方                                                                                                           |

### 2.2 Report Page

| 编号     | 功能模块       | 需求描述                                                                 |
| -------- | -------------- | ------------------------------------------------------------------------ |
| REPORT-1 | Results List   | 显示分析结果：1. 序列名称 2. PASS / FAIL 状态                            |
| REPORT-2 | Image Display  | 浏览分析图像：使用 Previous/Next 按钮翻页                                |
| REPORT-3 | Report Preview | 预览报告：预览分析报告，右侧大区域显示详细报告内容（支持富文本）         |
| REPORT-4 | Analysis Prot. | Analysis Protocols 分组框，显示协议类型（如 LCD、NOISE、CT number）      |
| REPORT-5 | Series List    | Protocol Series List 分组框，显示序列名称（如 LCD1-Series、LCD2-Series） |
| REPORT-6 | Selected Img   | Selected Images 分组框，展示已选图像缩略图                               |
| REPORT-7 | 操作按钮       | 底部有“<”、“>”翻页按钮，右上角有“Export Pdf”导出按钮                     |

### 2.3 Configuration Page

| 编号     | 功能模块             | 需求描述                                                                                               |
| -------- | -------------------- | ------------------------------------------------------------------------------------------------------ |
| CONFIG-1 | 扫描记录名称格式设置 | "Scan Record Name Format"分组，包含格式设置选项，如"QA3-LCD-00\*"等预设格式                            |
| CONFIG-2 | 图像索引分析设置     | "Image Index 2 Analysis"分组，包含 Option 1 和 Option 2 选项，支持选择"中间 1 张图片"、"中间 2 张图片" |
| CONFIG-3 | 数值范围设置         | 包含 From 和 to 数值输入框，支持设置分析范围（如 1 到 2）                                              |
| CONFIG-4 | 默认自动查询说明     | 显示"( for default auto query scan record )"说明文字                                                   |
| CONFIG-5 | 保存按钮             | 页面右下角有"Save"按钮，点击后保存所有配置参数                                                         |

### 2.4 History Page

| 编号      | 功能模块         | 需求描述                                                                     |
| --------- | ---------------- | ---------------------------------------------------------------------------- |
| HISTORY-1 | 历史记录表格     | 主内容区展示历史报告记录表格，包含 ID、Report Name、Report Time、Action 四列 |
| HISTORY-2 | 表格数据显示     | 表格显示具体记录，如 ID 为 1-6 的记录，报告时间格式为"2025.05.25 10:00"      |
| HISTORY-3 | Display 操作按钮 | 每条历史记录的 Action 列包含"Display"按钮，点击可查看对应报告详细内容        |
| HISTORY-4 | Report Directory | 页面包含"Report Directory"按钮，用于管理报告目录                             |

## 3. UI Prototype Design

> 原型 SVG 图片请参考：
>
> ![Home Page Layout](images/home.svg) > ![Report Page Layout](images/report.svg) > ![Configuration Page Layout](images/config.svg) > ![History Page Layout](images/history.svg)
