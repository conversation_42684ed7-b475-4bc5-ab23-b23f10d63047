# 项目介绍
- 项目是质量保证工具(Quality Assurance Tool)，用于DICOM医学影像质量保证，主要组件包括DICOM数据服务、文件管理器、数据提供者、分析视图和异常处理机制。

# 沟通规则
- 沟通规则：1.使用中文沟通 2.直接无套话 3.不擅自更改设计 4.坚持客观事实
- 用户的目的是依据现有代码设计更新.agents目录下的相关文档及augment memories，而非重构代码。

# 记忆池
- 每次会话初始化后，读取.agents目录下内容，先读取init.md,接着加载core目录内容。
- 项目代码与.agents目录下的开发文档/augment memories不一致，需要重新整理。

# 核心模块
- 异常处理模块：提供统一的异常处理机制，包括异常类、错误码、AOP切面、消息处理和工具类。
- 消息系统：提供统一的消息处理机制，包括Message接口、AbstractMessage类、消息枚举和消息构建器。
- DICOM处理模块：提供DICOM数据的读取、解析和处理功能。
- UI组件模块：基于Swing框架的UI组件库，采用MVP架构。
- 配置管理模块：提供配置的读取、验证和管理功能。

# commands
- @clean code: 依据.agents/core/cc.md描述，分析并优化项目代码。
- @init: 读取.agents/init.md，并依据内容加载相关md。
- @update memo:依据会话内容或者特定topic，总结后更新到core/memory.md
- @update session:依据会话内容或者特定topic，总结后创建或更新今天对应记录文档。
- @update doc: 依据会话内容或者特定topic，总结后更新到.agents/docs相应开发文档。
- @agent command: 读取.agents/core/agent-command.md，以优化augment agent对文件的读写操作。
