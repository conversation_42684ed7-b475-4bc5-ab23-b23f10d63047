package com.ge.med.ct.exception.event;

import com.ge.med.ct.exception.code.ErrorCode;
import com.ge.med.ct.exception.core.QAToolException;

import java.util.EventObject;

/**
 * 异常事件类
 * 用于在异常发生时发布事件
 */
public class ExceptionEvent extends EventObject {
    private static final long serialVersionUID = 1L;

    private final QAToolException exception;
    private final long timestamp;

    /**
     * 创建异常事件
     *
     * @param source    事件源
     * @param exception 异常
     */
    public ExceptionEvent(Object source, QAToolException exception) {
        super(source);
        this.exception = exception;
        this.timestamp = System.currentTimeMillis();
    }

    public static ExceptionEvent fromException(Throwable throwable) {
        return new ExceptionEvent(null, (QAToolException) throwable);
    }

    /**
     * 获取异常
     *
     * @return 异常
     */
    public QAToolException getException() {
        return exception;
    }

    /**
     * 获取时间戳
     *
     * @return 时间戳
     */
    public long getTimestamp() {
        return timestamp;
    }

    /**
     * 获取错误码
     *
     * @return 错误码
     */
    public ErrorCode getErrorCode() {
        return exception.getErrorCode();
    }

    /**
     * 获取异常消息
     *
     * @return 异常消息
     */
    public String getMessage() {
        return exception.getMessage();
    }

    @Override
    public String toString() {
        return "ExceptionEvent [errorCode=" + getErrorCode() + ", message=" + getMessage() + ", timestamp=" + timestamp
                + "]";
    }
}
