# 配置优化

## 1. 配置文件统一

### 1.1 统一配置文件位置
将所有配置文件统一放在`resources/config`目录下，便于管理和维护。

```
resources/
  └── config/
      ├── application.properties  # 应用程序配置
      ├── table_columns.properties  # 表格列配置
      ├── messages.properties  # 消息配置
      └── logging.properties  # 日志配置
```

### 1.2 配置读取类
创建统一的配置读取类，简化配置的读取和使用。

```java
public class ConfigReader {
    private static final String CONFIG_DIR = "config/";
    private static final Properties properties = new Properties();
    
    static {
        try {
            // 加载应用程序配置
            loadProperties(CONFIG_DIR + "application.properties");
            
            // 加载表格列配置
            loadProperties(CONFIG_DIR + "table_columns.properties");
            
            // 加载其他配置...
        } catch (IOException e) {
            throw new RuntimeException("Failed to load configuration", e);
        }
    }
    
    private static void loadProperties(String path) throws IOException {
        try (InputStream is = ConfigReader.class.getClassLoader().getResourceAsStream(path)) {
            if (is != null) {
                properties.load(is);
            } else {
                throw new IOException("Configuration file not found: " + path);
            }
        }
    }
    
    // 获取配置项
    public static String getProperty(String key) {
        return properties.getProperty(key);
    }
    
    // 获取配置项，如果不存在则返回默认值
    public static String getProperty(String key, String defaultValue) {
        return properties.getProperty(key, defaultValue);
    }
}
```

## 2. 表格列配置

### 2.1 表格列配置格式
使用标准的DICOM Tag Name作为列名，添加special属性，用于标记需要特殊处理的列。

```properties
# 列名列表 - 逗号分隔，定义显示顺序
table.exam.columns=PatientID,StudyID,PatientName,StudyDate,Modality

# 患者ID
table.exam.column.PatientID.display-name=患者ID
table.exam.column.PatientID.width=120
table.exam.column.PatientID.visible=true

# 检查ID
table.exam.column.StudyID.display-name=检查ID
table.exam.column.StudyID.width=100
table.exam.column.StudyID.visible=true

# 患者姓名
table.exam.column.PatientName.display-name=患者姓名
table.exam.column.PatientName.width=150
table.exam.column.PatientName.visible=true
table.exam.column.PatientName.special=true

# 检查日期
table.exam.column.StudyDate.display-name=检查日期
table.exam.column.StudyDate.width=120
table.exam.column.StudyDate.visible=true
table.exam.column.StudyDate.special=true
table.exam.column.StudyDate.format=yyyy-MM-dd
```

### 2.2 表格列管理类
创建表格列管理类，统一处理表格列的配置和特殊处理。

```java
public class TableColumnManager {
    private static final String COLUMN_PREFIX = "table.exam.column.";
    private static final String SPECIAL_SUFFIX = ".special";
    private static final String DISPLAY_NAME_SUFFIX = ".display-name";
    private static final String WIDTH_SUFFIX = ".width";
    private static final String VISIBLE_SUFFIX = ".visible";
    private static final String FORMAT_SUFFIX = ".format";
    
    // 获取列配置
    public static List<ColumnConfig> getColumnConfigs() {
        List<ColumnConfig> configs = new ArrayList<>();
        
        // 获取列名列表
        String columnsStr = ConfigReader.getProperty("table.exam.columns");
        if (columnsStr == null || columnsStr.isEmpty()) {
            return configs;
        }
        
        // 解析列名列表
        String[] columns = columnsStr.split(",");
        for (String column : columns) {
            ColumnConfig config = new ColumnConfig();
            config.setName(column);
            
            // 设置显示名称
            String displayName = ConfigReader.getProperty(COLUMN_PREFIX + column + DISPLAY_NAME_SUFFIX);
            config.setDisplayName(displayName != null ? displayName : column);
            
            // 设置宽度
            String widthStr = ConfigReader.getProperty(COLUMN_PREFIX + column + WIDTH_SUFFIX);
            config.setWidth(widthStr != null ? Integer.parseInt(widthStr) : 100);
            
            // 设置可见性
            String visibleStr = ConfigReader.getProperty(COLUMN_PREFIX + column + VISIBLE_SUFFIX);
            config.setVisible(visibleStr != null ? Boolean.parseBoolean(visibleStr) : true);
            
            // 设置特殊标记
            String specialStr = ConfigReader.getProperty(COLUMN_PREFIX + column + SPECIAL_SUFFIX);
            config.setSpecial(specialStr != null ? Boolean.parseBoolean(specialStr) : false);
            
            // 设置格式
            String format = ConfigReader.getProperty(COLUMN_PREFIX + column + FORMAT_SUFFIX);
            config.setFormat(format);
            
            configs.add(config);
        }
        
        return configs;
    }
    
    // 处理特殊列
    public static Object formatCellValue(Object value, ColumnConfig config) {
        if (value == null || !config.isSpecial()) {
            return value;
        }
        
        // 根据列名进行特殊处理
        switch (config.getName()) {
            case "PatientName":
                // 处理患者姓名
                return formatPatientName((String) value);
            case "StudyDate":
                // 处理检查日期
                return formatDate((String) value, config.getFormat());
            default:
                return value;
        }
    }
    
    // 格式化患者姓名
    private static String formatPatientName(String name) {
        // 实现患者姓名的格式化逻辑
        return name;
    }
    
    // 格式化日期
    private static String formatDate(String dateStr, String format) {
        if (dateStr == null || dateStr.isEmpty() || format == null || format.isEmpty()) {
            return dateStr;
        }
        
        try {
            // 解析DICOM日期格式（yyyyMMdd）
            SimpleDateFormat dicomFormat = new SimpleDateFormat("yyyyMMdd");
            Date date = dicomFormat.parse(dateStr);
            
            // 格式化为指定格式
            SimpleDateFormat customFormat = new SimpleDateFormat(format);
            return customFormat.format(date);
        } catch (ParseException e) {
            return dateStr;
        }
    }
}
```

## 3. 常量管理

### 3.1 常量类
创建常量类，集中管理常量值，减少硬编码。

```java
public class AppConstants {
    // 应用程序常量
    public static final String APP_NAME = "质量保证工具";
    public static final String APP_VERSION = "1.0.0";
    
    // 文件路径常量
    public static final String TEMP_DIR = "./temp";
    public static final String EXPORT_DIR = "./export";
    public static final String LOG_DIR = "./logs";
    
    // DICOM常量
    public static final String DICOM_CHARSET = "GB18030";
    public static final String DICOM_TRANSFER_SYNTAX = "1.2.840.10008.1.2.1";
    
    private AppConstants() {
        // 私有构造函数，防止实例化
    }
}
```

### 3.2 配置常量类
创建配置常量类，从配置文件中读取常量值。

```java
public class AppConfig {
    // 应用程序配置
    public static final String APP_NAME = ConfigReader.getProperty("app.name", "质量保证工具");
    public static final String APP_VERSION = ConfigReader.getProperty("app.version", "1.0.0");
    
    // UI配置
    public static final String UI_THEME = ConfigReader.getProperty("ui.theme", "light");
    public static final String UI_LANGUAGE = ConfigReader.getProperty("ui.language", "zh_CN");
    
    // 文件配置
    public static final String TEMP_DIR = ConfigReader.getProperty("file.temp.dir", "./temp");
    public static final String EXPORT_DIR = ConfigReader.getProperty("file.export.dir", "./export");
    public static final String LOG_DIR = ConfigReader.getProperty("file.log.dir", "./logs");
    
    private AppConfig() {
        // 私有构造函数，防止实例化
    }
}
```
