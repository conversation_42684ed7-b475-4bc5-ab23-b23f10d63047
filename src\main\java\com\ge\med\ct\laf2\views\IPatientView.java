package com.ge.med.ct.laf2.views;

import com.ge.med.ct.dicom2.model.DicomExam;
import com.ge.med.ct.dicom2.model.DicomImage;
import com.ge.med.ct.dicom2.model.DicomSeries;

import java.util.List;

/**
 * 患者视图接口
 * 定义患者面板UI相关操作
 */
public interface IPatientView {
    /**
     * 更新检查列表
     * @param exams 检查列表
     */
    void updateExamList(List<DicomExam> exams);
    
    /**
     * 更新序列列表
     * @param series 序列列表
     */
    void updateSeriesList(List<DicomSeries> series);
    
    /**
     * 更新图像列表
     * @param images 图像列表
     */
    void updateImageList(List<DicomImage> images);
    
    /**
     * 更新图像信息面板
     * @param filePath 图像文件路径
     */
    void updateImageInfo(String filePath);
    
    /**
     * 选择检查
     * @param index 检查索引
     */
    void selectExam(int index);
    
    /**
     * 选择序列
     * @param index 序列索引
     */
    void selectSeries(int index);
    
    /**
     * 选择图像
     * @param index 图像索引
     */
    void selectImage(int index);
    
    /**
     * 清空所有内容
     */
    void clearContents();
    
    /**
     * 显示错误信息
     * @param message 错误消息
     * @param error 异常对象
     */
    void showError(String message, Throwable error);
    
    /**
     * 获取当前选中的检查行索引
     * @return 检查行索引
     */
    int getSelectedExamRow();
    
    /**
     * 获取当前选中的序列行索引
     * @return 序列行索引
     */
    int getSelectedSeriesRow();
    
    /**
     * 获取当前选中的图像行索引
     * @return 图像行索引
     */
    int getSelectedImageRow();
} 