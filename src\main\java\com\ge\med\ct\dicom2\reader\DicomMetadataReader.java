package com.ge.med.ct.dicom2.reader;

import com.ge.med.ct.dicom2.service.DicomTagService;
import com.ge.med.ct.dicom2.service.DicomFileValidator;
import com.ge.med.ct.dicom2.model.DicomFileModel;
import com.ge.med.ct.dicom2.tag.DicomTag;
import com.ge.med.ct.exception.aspect.HandleException;
import com.ge.med.ct.exception.code.ErrorCode;
import com.ge.med.ct.exception.core.DicomException;
import com.ge.med.ct.exception.factory.ExceptionFactory;
import com.ge.med.ct.exception.message.DicomMessages;
import org.dcm4che3.data.Attributes;
import org.dcm4che3.data.Tag;
import org.dcm4che3.data.VR;
import org.dcm4che3.io.DicomInputStream;

import java.io.File;
import java.io.IOException;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * DICOM元数据读取器
 * 负责读取DICOM文件的元数据信息，包括标签、像素数据和文件结构
 */
public class DicomMetadataReader extends BasicDicomReader {
    private final DicomFileValidator validationService;

    public DicomMetadataReader() throws DicomException {
        this.validationService = DicomFileValidator.getInstance();
    }

    /**
     * 读取DICOM文件元数据
     */
    @HandleException(errorCode = ErrorCode.PROCESSING)
    public DicomFileModel readMetadata(String filePath) throws DicomException {
        if (filePath == null || filePath.isEmpty()) {
            throw ExceptionFactory.createDicomException(ErrorCode.DICOM_VALIDATION,
                    DicomMessages.VALIDATION_ERROR, "文件路径不能为空");
        }

        Path path = Paths.get(filePath);
        File file = path.toFile();

        if (!file.exists()) {
            throw ExceptionFactory.createDicomException(ErrorCode.READ, DicomMessages.FILE_NOT_FOUND,
                    filePath);
        }

        if (!file.isFile()) {
            throw ExceptionFactory.createDicomException(ErrorCode.DICOM_VALIDATION,
                    DicomMessages.VALIDATION_ERROR, "路径不是文件: " + filePath);
        }

        if (!file.canRead()) {
            throw ExceptionFactory.createDicomException(ErrorCode.READ, DicomMessages.FILE_READ_ERROR,
                    filePath);
        }

        if (!DicomFileValidator.isDicomFile(file)) {
            throw ExceptionFactory.createDicomException(ErrorCode.DICOM_VALIDATION, DicomMessages.FILE_INVALID,
                    filePath);
        }

        try (DicomInputStream dis = new DicomInputStream(file)) {
            Attributes attrs = dis.readDataset();

            // 创建模型并设置基本属性
            DicomFileModel model = createModelFromAttributes(attrs, filePath);

            // 验证必要的DICOM属性
            validateDicomAttributes(model, attrs);

                List<DicomTag> parsedTags = DicomTagService.parseTags(attrs);
                for (DicomTag tag : parsedTags) {
                    model.addTag(tag.getTagId(), tag);
                }

            // 读取像素数据相关的元数据
            readPixelMetadata2Model(model, attrs);

            return model;
        } catch (IOException e) {
            throw ExceptionFactory.createDicomException(ErrorCode.READ, DicomMessages.FILE_READ_ERROR, e,
                    filePath);
        }
    }

    /**
     * 验证DICOM属性是否符合要求
     */
    @HandleException(errorCode = ErrorCode.DICOM_VALIDATION)
    private void validateDicomAttributes(DicomFileModel model, Attributes attributes) throws DicomException {
        if (!validationService.quickValidate(attributes)) {
            throw ExceptionFactory.createDicomException(ErrorCode.DICOM_VALIDATION,
                    DicomMessages.VALIDATION_FAILED, "DICOM属性快速验证失败");
        }
    }

    /**
     * 从DICOM属性创建DicomFileModel
     */
    @HandleException(errorCode = ErrorCode.PROCESSING)
    private DicomFileModel createModelFromAttributes(Attributes attrs, String filePath) throws DicomException {
        // 使用SOPInstanceUID作为ID
        String sopInstanceUid = attrs.getString(Tag.SOPInstanceUID, "unknown");
        DicomFileModel model = new DicomFileModel(sopInstanceUid);

        // 设置文件基本信息
        model.setFilePath(filePath);
        File file = new File(filePath);
        model.setFileName(file.getName());
        model.setFileSize(file.length());
        model.setFileType("DICOM");

        return model;
    }

    /**
     * 读取像素数据相关的元数据
     */
    @HandleException(errorCode = ErrorCode.DICOM_IMAGE_PROCESSING)
    private void readPixelMetadata2Model(DicomFileModel model, Attributes attributes) throws DicomException {
        try {
            // 获取并验证基本图像属性
            Integer rows = attributes.getInt(Tag.Rows, -1);
            Integer columns = attributes.getInt(Tag.Columns, -1);

            // 验证Rows和Columns是否存在且有效
            if (rows == -1 || columns == -1) {
                throw ExceptionFactory.createDicomException(ErrorCode.DICOM_VALIDATION, DicomMessages.TAG_MISSING,
                        "Rows/Columns");
            }

            if (rows <= 0 || columns <= 0) {
                throw ExceptionFactory.createDicomException(ErrorCode.DICOM_VALIDATION,
                        DicomMessages.INVALID_DIMENSION);
            }

            // 设置基本图像属性
            addTagToModel(model, String.format("%08X", Tag.Rows), String.valueOf(rows), VR.US);
            addTagToModel(model, String.format("%08X", Tag.Columns), String.valueOf(columns), VR.US);

            // 其他像素相关的属性
            int bitsAllocated = attributes.getInt(Tag.BitsAllocated, -1);
            if (bitsAllocated == -1) {
                throw ExceptionFactory.createDicomException(ErrorCode.DICOM_VALIDATION, DicomMessages.TAG_MISSING,
                        "BitsAllocated");
            }
            if (bitsAllocated <= 0 || bitsAllocated % 8 != 0) {
                throw ExceptionFactory.createDicomException(ErrorCode.DICOM_VALIDATION,
                        DicomMessages.INVALID_BITS_ALLOCATED);
            }
            addTagToModel(model, String.format("%08X", Tag.BitsAllocated), String.valueOf(bitsAllocated), VR.US);

        } catch (DicomException e) {
            throw e;
        } catch (Exception e) {
            throw ExceptionFactory.createDicomException(ErrorCode.PROCESSING,
                    DicomMessages.IMAGE_PROCESSING_ERROR, e, e.getMessage());
        }
    }

    /**
     * 添加标签到模型
     */
    @HandleException(errorCode = ErrorCode.TAG_PROCESSING)
    private void addTagToModel(DicomFileModel model, String tagId, String value, VR vr) {
        if (tagId == null || value == null) {
            return; // 忽略空标签
        }

        try {
            DicomTag tag = new DicomTag(tagId, value, vr);
            model.addTag(tagId, tag);
        } catch (DicomException e) {
            throw e;
        } catch (Exception e) {
            throw ExceptionFactory.createDicomException(ErrorCode.TAG_PROCESSING,
                    DicomMessages.TAG_CREATE_FAILED, e, tagId);
        }
    }

    /**
     * 读取DICOM文件的所有标签
     *
     * @param filePath DICOM文件路径
     * @return 标签列表
     * @throws DicomException 如果读取失败
     */
    @HandleException(errorCode = ErrorCode.TAG_PROCESSING)
    public List<DicomTag> readAllTags(String filePath) throws DicomException {
        DicomFileModel model = readMetadata(filePath);
        return new ArrayList<>(model.getTags().values());
    }

    /**
     * 快速读取DICOM文件的关键标签
     * 只读取必要的标签，不解析完整的DICOM数据，提高性能
     *
     * @param filePath DICOM文件路径
     * @return 关键标签映射
     * @throws IOException    如果读取失败
     * @throws DicomException 如果文件无效
     */
    @HandleException(errorCode = ErrorCode.READ)
    public Map<String, String> readKeyTags(String filePath) throws IOException, DicomException {
        validateFilePath(filePath);
        Map<String, String> keyTags = new HashMap<>();
        File file = new File(filePath);

        try (DicomInputStream dis = new DicomInputStream(file)) {
            dis.setIncludeBulkData(DicomInputStream.IncludeBulkData.NO);
            Attributes fmi = dis.readFileMetaInformation();

            if (fmi == null) {
                throw new DicomException(ErrorCode.READ, DicomMessages.FILE_INVALID, "不是有效的DICOM文件", filePath);
            }

            Attributes dataset = new Attributes();
            dis.readAttributes(dataset, -1, -1);
            extractKeyTags(dataset, keyTags);
            return keyTags;
        }
    }

    /**
     * 从数据集中提取关键标签
     */
    private void extractKeyTags(Attributes dataset, Map<String, String> keyTags) {
        if (dataset == null || keyTags == null) {
            return;
        }

        // 患者信息
        keyTags.put("PatientID", dataset.getString(Tag.PatientID, ""));
        keyTags.put("PatientName", dataset.getString(Tag.PatientName, ""));

        // 检查信息
        keyTags.put("StudyInstanceUID", dataset.getString(Tag.StudyInstanceUID, ""));
        keyTags.put("StudyID", dataset.getString(Tag.StudyID, ""));
        keyTags.put("StudyDate", dataset.getString(Tag.StudyDate, ""));

        // 序列信息
        keyTags.put("SeriesInstanceUID", dataset.getString(Tag.SeriesInstanceUID, ""));
        keyTags.put("SeriesNumber", dataset.getString(Tag.SeriesNumber, ""));
        keyTags.put("Modality", dataset.getString(Tag.Modality, ""));

        // 图像信息
        keyTags.put("SOPInstanceUID", dataset.getString(Tag.SOPInstanceUID, ""));
        keyTags.put("InstanceNumber", dataset.getString(Tag.InstanceNumber, ""));
    }

    /**
     * 验证文件路径
     */
    @HandleException(errorCode = ErrorCode.DICOM_VALIDATION)
    private void validateFilePath(String filePath) throws DicomException {
        if (filePath == null || filePath.isEmpty()) {
            throw new DicomException(ErrorCode.DICOM_VALIDATION, DicomMessages.VALIDATION_ERROR, "文件路径不能为空");
        }

        File file = new File(filePath);
        if (!file.exists() || !file.isFile() || !file.canRead()) {
            throw new DicomException(ErrorCode.READ, DicomMessages.FILE_NOT_FOUND, filePath);
        }

        if (!DicomFileValidator.isDicomFile(file)) {
            throw new DicomException(ErrorCode.DICOM_VALIDATION, DicomMessages.FILE_INVALID, "不是有效的DICOM文件", filePath);
        }
    }
}