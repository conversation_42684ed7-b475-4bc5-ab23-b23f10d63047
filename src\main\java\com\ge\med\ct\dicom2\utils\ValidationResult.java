package com.ge.med.ct.dicom2.utils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

public class ValidationResult {
    private boolean valid;
    private String errorMessage;
    private final List<String> errors = new ArrayList<>();

    public ValidationResult(boolean valid) {
        this.valid = valid;
    }

    public boolean isValid() {
        return valid;
    }

    public void setValid(boolean valid) {
        this.valid = valid;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    public void addError(String context, String message) {
        this.valid = false;
        String errorMsg = context != null ? (context + ": " + message) : message;
        errors.add(errorMsg);
    }

    public List<String> getErrors() {
        return Collections.unmodifiableList(errors);
    }
}