# 🔍 DICOM 图像 PESI 路径查询指南

> **收件人**: <PERSON><PERSON><PERSON>
> **主题**: 使用 Coreload 工具查询 DICOM 图像物理路径

---

### **原始邮件内容**

> **注**: 以下是原始技术邮件的完整内容，保留原始格式以供参考。

```text

Hi <PERSON>yue.

For the below use case, you can use the Coreload provided utility executequery.sh to connect with image database and query the required attributes.
This script is a released artifact from Coreload and can be used by modalities.

You can write a shell script or python script to find the PESI path(physical system path of DICOM image) of image for the specific patient name or series description or other attributes.

Below I'm showing an example query using patient name and series description.

{ctuser@oc79}executequery.sh -c "select img.patient_id,img.exam_id,img.image_set_id,img.image_id,img.dcm_image_id from v_image img,imageset series, exam study where study.exam_id=series.exam_id and series.image_set_id=img.image_set_id and study.patient_name_unicode='RT_exam_MR' and series.series_description='FIESTA MULTICOUPES';"
warning: iv not used by this cipher
warning: iv not used by this cipher
patient_id | exam_id | image_set_id | image_id | dcm_image_id
------------+---------+--------------+----------+--------------
         12 |      13 |           47 |     2934 | 2
         12 |      13 |           47 |     2935 | 3
         12 |      13 |           47 |     2936 | 4
         12 |      13 |           47 |     2937 | 5
         12 |      13 |           47 |     2938 | 6
         12 |      14 |           48 |     2939 | 2
         12 |      12 |           44 |     2919 | 2
         12 |      12 |           44 |     2920 | 3
         12 |      12 |           44 |     2921 | 4
         12 |      12 |           44 |     2922 | 5
         12 |      12 |           44 |     2923 | 6
(11 rows)

The above query will fetch the matching patient attributes, using which you can find the pesi path.

Iterate through the result of each row in the script and then prepare and execute below query.

find /usr/g/sdc_image_pool/images/p<patient_id>/e<exam_id>/s<image_set_id>/i<image_id>.*.<dcm_image_id>
or
find /usr/g/sdc_image_pool/images/p<patient_id>/e<exam_id>/s<image_set_id>/i<image_id>.*

You can get the respective result PESI Path.
For e.g. In above example result If we use the first row, the query will be like
{ctuser@oc79}find $SDCIMAGEPOOL/images/p12/e13/s47/i2934.*
/usr/g/sdc_image_pool/images/p12/e13/s47/i2934.MRDC.2

You got the PESI path for the specific result row. Similarly iterate through each result row, execute the above query, and get the PESI path.
I hope it helps.

I suggest going with below 2 options.
• executequery.sh -c "select img.patient_id,img.exam_id,img.image_set_id,img.image_id from v_image img,imageset series, exam study where study.exam_id=series.exam_id and series.image_set_id=img.image_set_id and study.patient_name_unicode='RT_exam_MR' and series.series_description='FIESTA MULTICOUPES';"

• iterate each row, prepare find query and execute to get PESI path.
o find /usr/g/sdc_image_pool/images/p<patient_id>/e<exam_id>/s<image_set_id>/i<image_id>.*
```
