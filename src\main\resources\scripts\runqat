#!/bin/csh -f

# CT图像质量QA工具启动脚本
setenv BIN /usr/g/bin
setenv CONFIGDIR /usr/g/config
setenv QAT2DIR /usr/g/bin/qatdemo
setenv JVM_PATH /usr/java64/latest/bin/java

set ADD_PARAMS=""

# 确保logs目录存在，但不单独创建日志文件
if (! -d ${QAT2DIR}/logs) then
    echo "Creating logs directory: ${QAT2DIR}/logs"
    mkdir -p ${QAT2DIR}/logs
    chmod 755 ${QAT2DIR}/logs
endif

# 设置umask确保新创建的文件具有正确权限
umask 022

# 启动Java应用程序（不重定向输出到日志文件，由LogManager处理）
echo "Starting CT Image Quality Tool..."
"$JVM_PATH" $ADD_PARAMS -jar "${QAT2DIR}/ct-image-quality-1.0-SNAPSHOT.jar" -Djava.util.logging.config.file=/usr/g/config/stu/config/logging.properties com.ge.med.ct.laf2.CTQAssuranceTool $1