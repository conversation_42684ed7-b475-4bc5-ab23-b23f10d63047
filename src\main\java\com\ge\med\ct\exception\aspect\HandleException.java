package com.ge.med.ct.exception.aspect;

import com.ge.med.ct.exception.code.ErrorCode;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 异常处理注解
 * 标记需要进行异常处理的方法或类
 *
 * 注意：
 * 1. 类级别注解会应用于类中的所有方法
 * 2. 方法级别注解会覆盖类级别注解
 * 3. 如果既没有方法级别注解也没有类级别注解，则不会应用异常处理
 */
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
public @interface HandleException {
    /**
     * 异常处理的错误码
     * 必须指定错误码
     */
    ErrorCode errorCode() default ErrorCode.UNEXPECTED;

    /**
     * 是否记录异常
     */
    boolean logException() default true;

    /**
     * 是否发布异常事件
     */
    boolean publishEvent() default true;

    /**
     * 异常类型
     * 指定要处理的异常类型
     */
    Class<? extends Throwable>[] value() default {};

    /**
     * 最大重试次数
     * 当方法抛出异常时，尝试重新执行的次数
     * 默认为0，表示不重试
     */
    int maxRetries() default 0;

    /**
     * 重试间隔（毫秒）
     * 在重试之间等待的时间
     */
    long retryDelay() default 0;

    /**
     * 需要重试的异常类型
     * 如果指定，只有这些类型的异常才会触发重试
     * 默认为空数组，表示所有异常都会触发重试
     */
    Class<? extends Throwable>[] retryFor() default {};

    /**
     * 不需要重试的异常类型
     * 如果指定，这些类型的异常不会触发重试
     * 默认为空数组
     */
    Class<? extends Throwable>[] noRetryFor() default {};

    /**
     * 是否继承类级别的属性
     * 如果为true，则方法级别的属性会覆盖类级别的属性
     * 如果为false，则完全使用方法级别的属性，忽略类级别的属性
     */
    boolean inheritClassLevel() default true;
}
