package com.ge.med.ct.dicom2.service;

import com.ge.med.ct.dicom2.utils.ValidationResult;
import com.ge.med.ct.dicom2.model.DicomExam;
import com.ge.med.ct.dicom2.model.DicomFileModel;
import com.ge.med.ct.dicom2.model.DicomSeries;
import com.ge.med.ct.exception.aspect.HandleException;
import com.ge.med.ct.exception.code.ErrorCode;
import com.ge.med.ct.exception.core.DicomException;
import com.ge.med.ct.exception.core.QAToolException;
import com.ge.med.ct.exception.message.DicomMessages;
import com.ge.med.ct.service.IOOperator;
import org.dcm4che3.data.Attributes;
import org.dcm4che3.data.Tag;
import org.dcm4che3.io.DicomInputStream;
import org.dcm4che3.util.TagUtils;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.nio.ByteBuffer;
import java.nio.channels.SeekableByteChannel;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;
import java.util.logging.Logger;

/**
 * DICOM验证服务
 * 负责验证DICOM文件的有效性
 */
public class DicomFileValidator {
    private static final Logger LOG = Logger.getLogger(DicomFileValidator.class.getName());
    private static volatile DicomFileValidator instance;

    // DICOM文件标识常量
    protected static final int DICOM_MAGIC_OFFSET = 128; // DICOM文件前128字节为保留区
    protected static final String DICOM_MAGIC_STRING = "DICM"; // DICOM文件标识
    protected static final int DICOM_MAGIC_LENGTH = 4; // DICOM标识长度

    // 使用整数标签常量
    private static final Set<Integer> REQUIRED_TAGS;
    static {
        Set<Integer> tags = new HashSet<>();
        tags.add(Tag.PatientID);
        tags.add(Tag.PatientName);
        tags.add(Tag.StudyInstanceUID);
        tags.add(Tag.SeriesInstanceUID);
        tags.add(Tag.SOPInstanceUID);
        tags.add(Tag.Modality);
        tags.add(Tag.SeriesNumber);
        tags.add(Tag.InstanceNumber);
        REQUIRED_TAGS = Collections.unmodifiableSet(tags);
    }

    // 修正Collections.unmodifiableSet的用法
    private static final Set<String> DICOM_FILE_PREFIXES;
    static {
        Set<String> prefixes = new HashSet<>();
        prefixes.add("DICM");
        DICOM_FILE_PREFIXES = Collections.unmodifiableSet(prefixes);
    }

    // 核心配置
    private static final boolean STRICT_VALIDATION = true; // 严格验证模式

    private DicomFileValidator() {
    }

    public static DicomFileValidator getInstance() {
        if (instance == null) {
            synchronized (DicomFileValidator.class) {
                if (instance == null) {
                    instance = new DicomFileValidator();
                }
            }
        }
        return instance;
    }

    @HandleException(errorCode = ErrorCode.DICOM_VALIDATION)
    public static boolean isDicomFile(String filePath) {
        return filePath != null && isDicomFile(Paths.get(filePath));
    }

    @HandleException(errorCode = ErrorCode.DICOM_VALIDATION)
    public static boolean isDicomFile(Path path) {
        if (path == null || !Files.exists(path) || !Files.isRegularFile(path) || !Files.isReadable(path)) {
            return false;
        }

        try {
            if (Files.size(path) < DICOM_MAGIC_OFFSET + DICOM_MAGIC_LENGTH) {
                return false;
            }

            try (SeekableByteChannel channel = Files.newByteChannel(path)) {
                channel.position(DICOM_MAGIC_OFFSET);
                ByteBuffer buffer = ByteBuffer.allocate(DICOM_MAGIC_LENGTH);

                if (channel.read(buffer) != DICOM_MAGIC_LENGTH) {
                    return false;
                }

                buffer.flip();
                byte[] magicBytes = new byte[DICOM_MAGIC_LENGTH];
                buffer.get(magicBytes);

                return DICOM_MAGIC_STRING.equals(new String(magicBytes));
            }
        } catch (IOException e) {
            LOG.warning("检查DICOM文件时出错: " + path);
            return false;
        }
    }

    @HandleException(errorCode = ErrorCode.DICOM_VALIDATION)
    public static boolean isDicomFile(File file) {
        return file != null && isDicomFile(file.toPath());
    }

    /**
     * 读取并执行DICOM文件的完整验证
     */
    public ValidationResult validateFile(String filePath, DicomFileModel model) {
        ValidationResult result = new ValidationResult(false);
        if (model == null) {
            result.setErrorMessage("DicomFileModel为空");
            return result;
        }

        if (!IOOperator.fileExists(filePath)) {
            result.setErrorMessage("DICOM文件不存在");
            return result;
        }

        try (DicomInputStream dis = new DicomInputStream(new File(filePath))) {
            validateDicomFile(model, dis.readDataset());
            result.setValid(true);
        } catch (QAToolException e) {
            handleValidationError(result, "验证失败", e.getMessage());
        } catch (IOException e) {
            handleValidationError(result, "读取失败", e.getMessage());
        } catch (Exception e) {
            handleValidationError(result, "验证错误", e.getMessage());
        }

        return result;
    }

    // 核心必需标签
    private static final Set<Integer> CORE_REQUIRED_TAGS = new HashSet<Integer>() {
        {
            add(Tag.SOPInstanceUID);
            add(Tag.Modality);
            add(Tag.StudyInstanceUID);
            add(Tag.SeriesInstanceUID);
        }
    };

    public boolean quickValidate(Attributes attributes) {
        if (attributes == null) {
            return false;
        }
        for (Integer tag : CORE_REQUIRED_TAGS) {
            if (!attributes.contains(tag)) {
                return false;
            }
        }
        return true;
    }

    /**
     * 验证DICOM文件的完整性
     */
    public void validateDicomFile(DicomFileModel model, Attributes attributes) {
        if (model == null || attributes == null) {
            throw new QAToolException(ErrorCode.DICOM_VALIDATION, DicomMessages.VALIDATION_ERROR, "DICOM属性为空");
        }

        List<String> errors = new ArrayList<>();

        // 验证必需标签
        validateRequiredTags(attributes, errors);

        // 验证核心信息
        validateCoreInfo(attributes, errors);

        if (!errors.isEmpty()) {
            throw new QAToolException(ErrorCode.DICOM_VALIDATION, DicomMessages.VALIDATION_ERROR,
                    String.join("; ", errors));
        }
    }

    /**
     * 检查文件头部是否包含DICOM标识
     */
    public boolean hasDicomHeader(File file) {
        try {
            if (file == null) {
                LOG.warning("检查的文件对象为空");
                return false;
            }

            if (!file.exists()) {
                LOG.warning("文件不存在: " + file.getAbsolutePath());
                return false;
            }

            if (!file.isFile()) {
                LOG.warning("路径不是文件: " + file.getAbsolutePath());
                return false;
            }

            if (!file.canRead()) {
                LOG.warning("文件无法读取: " + file.getAbsolutePath());
                return false;
            }

            if (file.length() < DICOM_MAGIC_OFFSET + DICOM_MAGIC_LENGTH) {
                LOG.warning("文件太小，不是DICOM文件: " + file.getAbsolutePath() + ", 大小: " + file.length() + " 字节");
                return false;
            }

            try (FileInputStream fis = new FileInputStream(file)) {
                // 跳过前128字节
                long bytesSkipped = 0;
                long totalToSkip = DICOM_MAGIC_OFFSET;

                while (bytesSkipped < totalToSkip) {
                    long skipped = fis.skip(totalToSkip - bytesSkipped);
                    if (skipped <= 0) {
                        LOG.warning("无法跳过文件头部: " + file.getAbsolutePath());
                        return false;
                    }
                    bytesSkipped += skipped;
                }

                byte[] magicBytes = new byte[DICOM_MAGIC_LENGTH];
                int bytesRead = fis.read(magicBytes);

                if (bytesRead != DICOM_MAGIC_LENGTH) {
                    LOG.warning("无法读取DICOM文件标识: " + file.getAbsolutePath() + ", 实际读取: " + bytesRead + " 字节");
                    return false;
                }

                String magic = new String(magicBytes);
                boolean isDicom = DICOM_MAGIC_STRING.equals(magic);

                if (!isDicom) {
                    LOG.fine("文件不是DICOM格式: " + file.getAbsolutePath() + ", 标识: '" + magic + "'");
                }

                return isDicom;
            } catch (IOException e) {
                LOG.warning("检查DICOM文件头部时出错: " + file.getAbsolutePath() + ", 原因: " + e.getMessage());
                return false;
            }
        } catch (Exception e) {
            LOG.warning("检查DICOM文件头部时发生意外异常: " + (file != null ? file.getAbsolutePath() : "null") + ", 原因: "
                    + e.getMessage());
            return false;
        }
    }

    private void validateRequiredTags(Attributes attributes, List<String> errors) {
        for (int tag : REQUIRED_TAGS) {
            if (!attributes.contains(tag)) {
                errors.add("缺少必需标签: " + TagUtils.toString(tag));
            }
        }
    }

    private void validateCoreInfo(Attributes attributes, List<String> errors) {
        // 验证患者信息
        validateStringTag(attributes, Tag.PatientID, "患者ID不能为空", errors);

        // 验证检查和序列信息
        validateStringTag(attributes, Tag.StudyInstanceUID, "检查实例UID不能为空", errors);
        validateStringTag(attributes, Tag.SeriesInstanceUID, "序列实例UID不能为空", errors);
        validateStringTag(attributes, Tag.SeriesNumber, "序列号不能为空", errors);
        validateStringTag(attributes, Tag.Modality, "模态不能为空", errors);
        validateStringTag(attributes, Tag.SOPInstanceUID, "SOP实例UID不能为空", errors);

        // 验证图像参数
        if (STRICT_VALIDATION) {
            validateImageDimensions(attributes, errors);
        }
    }

    private void validateStringTag(Attributes attributes, int tag, String errorMessage, List<String> errors) {
        String value = attributes.getString(tag);
        if (value == null || value.trim().isEmpty()) {
            errors.add(errorMessage);
        }
    }

    private void validateImageDimensions(Attributes attributes, List<String> errors) {
        int rows = attributes.getInt(Tag.Rows, 0);
        int columns = attributes.getInt(Tag.Columns, 0);

        if (rows <= 0 || columns <= 0) {
            errors.add("无效的图像尺寸");
        }
    }

    private static void handleValidationError(ValidationResult result, String errorType, String message) {
        String errorMsg = errorType + ": " + message;
        LOG.warning(errorMsg);
        result.setErrorMessage(errorMsg);
    }

    /**
     * 验证文件路径
     *
     * @param filePath 文件路径
     * @throws DicomException 如果文件路径无效
     */
    @HandleException(errorCode = ErrorCode.DICOM_VALIDATION)
    public static void validateFilePath(String filePath) throws DicomException {
        if (filePath == null || filePath.isEmpty()) {
            throw new DicomException(ErrorCode.READ, DicomMessages.FILE_NOT_FOUND, "文件路径不能为空");
        }

        File file = new File(filePath);
        if (!file.exists() || !file.isFile() || !file.canRead()) {
            throw new DicomException(ErrorCode.READ, DicomMessages.FILE_NOT_FOUND, filePath);
        }

        // 检查文件是否为DICOM格式
        if (!isDicomFile(file)) {
            throw new DicomException(ErrorCode.DICOM_VALIDATION, DicomMessages.FILE_INVALID, "不是有效的DICOM文件", filePath);
        }
    }

    /**
     * 验证DicomFileModel的基本属性
     * 
     * @param model 要验证的DicomFileModel
     * @throws IllegalArgumentException 如果验证失败
     */
    public static void validateDicomFileModel(DicomFileModel model) {
        if (model == null) {
            throw new IllegalArgumentException("DicomFileModel cannot be null");
        }

        if (model.getTags() == null || model.getTags().isEmpty()) {
            throw new IllegalArgumentException("DicomFileModel tags cannot be null or empty");
        }

        if (model.getStudyInstanceUID() == null || model.getStudyInstanceUID().isEmpty()) {
            throw new IllegalArgumentException("StudyInstanceUID cannot be null or empty");
        }

        if (model.getSeriesInstanceUID() == null || model.getSeriesInstanceUID().isEmpty()) {
            throw new IllegalArgumentException("SeriesInstanceUID cannot be null or empty");
        }

        if (model.getFilePath() == null || model.getFilePath().isEmpty()) {
            throw new IllegalArgumentException("File path cannot be null or empty");
        }

        File file = new File(model.getFilePath());
        if (!file.exists()) {
            throw new IllegalArgumentException("File does not exist: " + model.getFilePath());
        }
    }

    /**
     * 验证DICOM数据结构的完整性
     * 
     * @param exams 检查列表
     * @throws IllegalArgumentException 如果验证失败
     */
    public static void validateDicomStructure(List<DicomExam> exams) {
        if (exams == null || exams.isEmpty()) {
            throw new IllegalArgumentException("Exams list cannot be null or empty");
        }

        DicomExam firstExam = exams.get(0);
        if (firstExam == null) {
            throw new IllegalArgumentException("First exam cannot be null");
        }

        List<DicomSeries> seriesList = firstExam.getSeries();
        if (seriesList == null || seriesList.isEmpty()) {
            throw new IllegalArgumentException("Series list cannot be null or empty");
        }

        DicomSeries firstSeries = seriesList.get(0);
        if (firstSeries == null) {
            throw new IllegalArgumentException("First series cannot be null");
        }

        if (firstSeries.getImages() == null || firstSeries.getImages().isEmpty()) {
            throw new IllegalArgumentException("First series must contain at least one image");
        }
    }
}