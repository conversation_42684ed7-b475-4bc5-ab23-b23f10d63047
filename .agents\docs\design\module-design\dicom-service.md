# DICOM 服务模块设计文档

## 1. 概述

DICOM 服务模块是 CT 质量保证工具的核心基础组件，负责 DICOM 文件的加载、解析、缓存和数据访问。该模块基于 DCM4CHE 库实现，提供了完整的 DICOM 数据处理能力，支持 GE CT 设备的专用标签，并通过 Caffeine 缓存提供高性能的数据访问。

## 2. 模块目标

DICOM 服务模块的主要目标包括：

- 提供统一的 DICOM 文件加载和解析接口
- 支持 DICOM 文件和目录的批量处理
- 实现高效的 DICOM 数据缓存机制
- 支持 GE CT 设备的专用 DICOM 标签
- 提供线程安全的数据访问服务
- 确保 DICOM 数据的完整性和准确性

## 3. 模块架构

### 3.1 包结构

```
com.ge.med.ct.dicom2/
├── model/                  # DICOM数据模型
│   ├── DicomExam.java             # DICOM检查模型
│   ├── DicomSeries.java           # DICOM序列模型
│   ├── DicomImage.java            # DICOM图像模型
│   └── DicomObject.java           # DICOM对象基类
├── service/                # DICOM服务
│   ├── DicomDataService.java      # DICOM数据服务接口
│   ├── DicomDataServiceImpl.java  # DICOM数据服务实现
│   ├── DicomFileService.java      # DICOM文件服务
│   └── DicomDataProvider.java     # DICOM数据提供者
├── tag/                    # DICOM标签
│   ├── DicomTagConstants.java     # DICOM标签常量
│   ├── DicomTag.java              # DICOM标签模型
│   ├── DicomTagParser.java        # DICOM标签解析器
│   └── DicomTagServiceSimplified.java # DICOM标签服务
├── reader/                 # DICOM读取器
│   ├── DicomMetadataReader.java   # DICOM元数据读取器
│   └── DicomImageReader.java      # DICOM图像读取器
└── cache/                  # 缓存管理
    ├── DicomCacheManager.java     # DICOM缓存管理器
    └── CacheConfig.java           # 缓存配置
```

### 3.2 核心组件

DICOM 服务模块包含以下核心组件：

1. **DICOM 数据服务(DicomDataService)**: 提供统一的 DICOM 数据访问接口
2. **DICOM 文件服务(DicomFileService)**: 负责 DICOM 文件的扫描和管理
3. **DICOM 数据提供者(DicomDataProvider)**: 负责 DICOM 文件的加载和解析
4. **DICOM 标签解析器(DicomTagParser)**: 负责 DICOM 标签的解析和处理
5. **DICOM 缓存管理器(DicomCacheManager)**: 负责 DICOM 数据的缓存管理

### 3.3 组件关系图

```
+-------------------+       +-------------------+
| DicomDataService  |------>| DicomDataProvider |
+-------------------+       +-------------------+
| +loadDicomFile()  |       | +loadFile()       |
| +scanDirectory()  |       | +parseMetadata()  |
| +getAllExams()    |       | +extractImages()  |
| +getSeriesByExam()| <---> +-------------------+
| +getImagesBySeries|
+-------------------+       +-------------------+
         |                  | DicomCacheManager |
         v                  +-------------------+
+-------------------+       | +put()            |
| DicomFileService  |       | +get()            |
+-------------------+       | +invalidate()     |
| +scanFiles()      |       | +getStats()       |
| +validateFile()   |       +-------------------+
| +getFileInfo()    |
+-------------------+       +-------------------+
                            | DicomTagParser    |
                            +-------------------+
                            | +parseTag()       |
                            | +getTagValue()    |
                            | +validateTag()    |
                            +-------------------+
```

### 3.4 数据流

DICOM 服务模块的主要数据流如下：

```
DICOM文件 --> DicomFileService --> DicomDataProvider --> DCM4CHE解析 --> DicomTagParser --> DICOM对象模型 --> Caffeine缓存 --> DicomDataService
```

## 4. 主要功能

### 4.1 DICOM 文件加载

#### 4.1.1 单文件加载

```java
/**
 * 加载单个DICOM文件
 * @param filePath DICOM文件路径
 * @return DICOM检查对象
 * @throws QAToolException 加载失败时抛出异常
 */
DicomExam loadDicomFile(String filePath) throws QAToolException;
```

#### 4.1.2 目录扫描

```java
/**
 * 扫描DICOM目录
 * @param directoryPath DICOM目录路径
 * @return DICOM检查列表
 * @throws QAToolException 扫描失败时抛出异常
 */
List<DicomExam> scanDirectory(String directoryPath) throws QAToolException;
```

### 4.2 DICOM 数据查询

#### 4.2.1 检查级别查询

```java
/**
 * 获取所有检查
 * @return 检查列表
 */
List<DicomExam> getAllExams();

/**
 * 根据检查ID获取检查
 * @param examId 检查ID
 * @return DICOM检查对象
 */
DicomExam getExamById(String examId);
```

#### 4.2.2 序列级别查询

```java
/**
 * 获取检查的所有序列
 * @param examId 检查ID
 * @return 序列列表
 */
List<DicomSeries> getSeriesByExamId(String examId);

/**
 * 根据序列ID获取序列
 * @param seriesId 序列ID
 * @return DICOM序列对象
 */
DicomSeries getSeriesById(String seriesId);
```

#### 4.2.3 图像级别查询

```java
/**
 * 获取序列的所有图像
 * @param seriesId 序列ID
 * @return 图像列表
 */
List<DicomImage> getImagesBySeriesId(String seriesId);

/**
 * 根据图像ID获取图像
 * @param imageId 图像ID
 * @return DICOM图像对象
 */
DicomImage getImageById(String imageId);
```

### 4.3 DICOM 标签处理

#### 4.3.1 标准 DICOM 标签

支持所有标准 DICOM 标签，包括：

- **患者信息**: 患者 ID、姓名、性别、年龄等
- **检查信息**: 检查 ID、检查日期、检查时间等
- **序列信息**: 序列号、序列描述、模态等
- **图像信息**: 图像号、图像位置、像素数据等
- **设备信息**: 制造商、型号、软件版本等
- **技术参数**: KVP、mAs、层厚、重建算法等

#### 4.3.2 GE CT 专用标签

支持 GE CT 设备的专用标签，包括：

```java
public class DicomTagConstants {
    // GE CT专用标签
    public static final String CTDIVOL = "(0019,1027)";           // CTDI体积
    public static final String GE_PROTOCOL = "(0019,1030)";       // GE协议
    public static final String GE_SERIES_TYPE = "(0019,1040)";    // GE序列类型
    public static final String GE_RECONSTRUCTION = "(0019,1050)"; // GE重建参数
    public static final String GE_SCAN_OPTIONS = "(0019,1060)";   // GE扫描选项

    // 其他GE专用标签...
}
```

### 4.4 缓存管理

#### 4.4.1 缓存策略

使用 Caffeine 缓存库实现多级缓存：

- **检查级缓存**: 缓存 DicomExam 对象
- **序列级缓存**: 缓存 DicomSeries 对象
- **图像级缓存**: 缓存 DicomImage 对象
- **标签值缓存**: 缓存常用标签值

#### 4.4.2 缓存配置

```java
public class CacheConfig {
    // 缓存大小配置
    public static final int EXAM_CACHE_SIZE = 1000;
    public static final int SERIES_CACHE_SIZE = 5000;
    public static final int IMAGE_CACHE_SIZE = 10000;

    // 缓存过期时间配置
    public static final Duration CACHE_EXPIRE_TIME = Duration.ofHours(2);

    // 缓存刷新时间配置
    public static final Duration CACHE_REFRESH_TIME = Duration.ofMinutes(30);
}
```

#### 4.4.3 缓存操作

```java
/**
 * 清除缓存
 */
void clearCache();

/**
 * 获取缓存统计信息
 * @return 缓存统计
 */
CacheStats getCacheStats();

/**
 * 预热缓存
 * @param examIds 检查ID列表
 */
void warmupCache(List<String> examIds);
```

## 5. DICOM 数据模型

### 5.1 DicomExam（检查模型）

```java
public class DicomExam extends DicomObject {
    private String examId;              // 检查ID
    private String patientId;           // 患者ID
    private String patientName;         // 患者姓名
    private String studyDate;           // 检查日期
    private String studyTime;           // 检查时间
    private String studyDescription;    // 检查描述
    private List<DicomSeries> series;   // 序列列表

    // 标签值访问方法
    public Object getTagValue(String tagName);
    public void setTagValue(String tagName, Object value);

    // 序列管理方法
    public void addSeries(DicomSeries series);
    public DicomSeries getSeriesById(String seriesId);
    public List<DicomSeries> getAllSeries();
}
```

### 5.2 DicomSeries（序列模型）

```java
public class DicomSeries extends DicomObject {
    private String seriesId;            // 序列ID
    private String seriesNumber;        // 序列号
    private String seriesDescription;   // 序列描述
    private String modality;            // 模态
    private String bodyPartExamined;    // 检查部位
    private List<DicomImage> images;    // 图像列表

    // 标签值访问方法
    public Object getTagValue(String tagName);
    public void setTagValue(String tagName, Object value);

    // 图像管理方法
    public void addImage(DicomImage image);
    public DicomImage getImageById(String imageId);
    public List<DicomImage> getAllImages();
}
```

### 5.3 DicomImage（图像模型）

```java
public class DicomImage extends DicomObject {
    private String imageId;             // 图像ID
    private String instanceNumber;      // 实例号
    private String imagePosition;       // 图像位置
    private String imageOrientation;    // 图像方向
    private String sliceThickness;      // 层厚
    private String pixelSpacing;        // 像素间距
    private BufferedImage pixelData;    // 像素数据

    // 标签值访问方法
    public Object getTagValue(String tagName);
    public void setTagValue(String tagName, Object value);

    // 图像数据访问方法
    public BufferedImage getPixelData();
    public void setPixelData(BufferedImage pixelData);
    public int[] getPixelArray();
}
```

## 6. 关键流程

### 6.1 DICOM 文件加载流程

```
1. 用户选择DICOM文件或目录
2. DicomFileService扫描目录，识别DICOM文件
3. DicomDataProvider使用DCM4CHE库加载DICOM文件
4. DicomMetadataReader读取文件元数据
5. DicomTagParser解析DICOM标签（包括GE CT专用标签）
6. 创建DicomExam、DicomSeries和DicomImage对象
7. DicomCacheManager将对象存入Caffeine缓存
8. 通知UI更新显示
```

### 6.2 DICOM 数据查询流程

```
1. 用户在UI上选择查询条件
2. UI通过Presenter调用DicomDataService的查询方法
3. DicomDataService根据条件查询Caffeine缓存
4. 如缓存未命中，通过DicomDataProvider重新加载
5. 返回查询结果给Presenter
6. Presenter更新UI显示结果
```

### 6.3 DICOM 标签解析流程

```
1. DicomDataProvider加载DICOM文件
2. DCM4CHE库解析DICOM数据集
3. DicomTagParser遍历数据集中的标签
4. 根据DicomTagConstants映射标签名称
5. 处理GE CT专用标签的特殊格式
6. 将标签值存储到DICOM对象模型中
7. 缓存常用标签值以提高性能
```

## 7. 异常处理

### 7.1 文件访问异常

```java
// 文件不存在
throw new DicomException(DicomMessages.FILE_NOT_FOUND, filePath);

// 文件格式错误
throw new DicomException(DicomMessages.INVALID_DICOM_FORMAT, filePath);

// 文件读取权限不足
throw new DicomException(DicomMessages.FILE_ACCESS_DENIED, filePath);
```

### 7.2 数据解析异常

```java
// DICOM标签缺失
throw new DicomException(DicomMessages.REQUIRED_TAG_MISSING, tagName);

// 标签值格式错误
throw new DicomException(DicomMessages.INVALID_TAG_VALUE, tagName, value);

// 像素数据损坏
throw new DicomException(DicomMessages.CORRUPTED_PIXEL_DATA, imageId);
```

### 7.3 缓存异常

```java
// 缓存空间不足
throw new DicomException(DicomMessages.CACHE_FULL);

// 缓存数据过期
throw new DicomException(DicomMessages.CACHE_EXPIRED, objectId);
```

## 8. 性能优化

### 8.1 加载优化

- **延迟加载**: 只在需要时加载像素数据
- **并行处理**: 使用多线程并行加载多个文件
- **内存管理**: 及时释放不再使用的大对象
- **文件预检**: 快速验证文件格式避免无效加载

### 8.2 缓存优化

- **分级缓存**: 根据访问频率设置不同的缓存策略
- **智能预取**: 根据访问模式预加载相关数据
- **压缩存储**: 对大对象进行压缩存储
- **异步刷新**: 使用异步方式刷新过期缓存

### 8.3 查询优化

- **索引建立**: 为常用查询字段建立索引
- **结果缓存**: 缓存常用查询的结果
- **批量操作**: 支持批量查询和更新操作
- **连接池**: 使用连接池管理数据库连接

## 9. 配置管理

### 9.1 缓存配置

```properties
# 缓存大小配置
dicom.cache.exam.size=1000
dicom.cache.series.size=5000
dicom.cache.image.size=10000

# 缓存过期时间配置（小时）
dicom.cache.expire.time=2

# 缓存刷新时间配置（分钟）
dicom.cache.refresh.time=30
```

### 9.2 文件处理配置

```properties
# 支持的文件扩展名
dicom.file.extensions=.dcm,.dicom,.ima

# 最大文件大小（MB）
dicom.file.max.size=500

# 并行加载线程数
dicom.loader.thread.count=4
```

### 9.3 标签处理配置

```properties
# 是否启用GE专用标签
dicom.tag.ge.enabled=true

# 标签值缓存大小
dicom.tag.cache.size=10000

# 必需标签列表
dicom.tag.required=PatientID,StudyInstanceUID,SeriesInstanceUID
```

## 10. 使用示例

### 10.1 基本使用

```java
// 创建DICOM数据服务
DicomDataService dicomService = new DicomDataServiceImpl();

// 加载DICOM文件
DicomExam exam = dicomService.loadDicomFile("/path/to/dicom/file.dcm");

// 获取检查信息
String patientId = (String) exam.getTagValue(DicomTagConstants.Patient.PATIENT_ID);
String studyDate = (String) exam.getTagValue(DicomTagConstants.Study.STUDY_DATE);

// 获取序列列表
List<DicomSeries> seriesList = exam.getAllSeries();
for (DicomSeries series : seriesList) {
    String seriesDesc = (String) series.getTagValue(DicomTagConstants.Series.SERIES_DESCRIPTION);
    System.out.println("Series: " + seriesDesc);

    // 获取图像列表
    List<DicomImage> imageList = series.getAllImages();
    for (DicomImage image : imageList) {
        String instanceNumber = (String) image.getTagValue(DicomTagConstants.Image.INSTANCE_NUMBER);
        System.out.println("  Image: " + instanceNumber);
    }
}
```

### 10.2 目录扫描

```java
// 扫描DICOM目录
List<DicomExam> examList = dicomService.scanDirectory("/path/to/dicom/directory");

// 处理所有检查
for (DicomExam exam : examList) {
    String examId = exam.getExamId();
    System.out.println("Processing exam: " + examId);

    // 获取检查的序列
    List<DicomSeries> seriesList = dicomService.getSeriesByExamId(examId);
    for (DicomSeries series : seriesList) {
        // 处理序列数据
        processSeries(series);
    }
}
```

### 10.3 缓存管理

```java
// 获取缓存统计
CacheStats stats = dicomService.getCacheStats();
System.out.println("Cache hit rate: " + stats.hitRate());
System.out.println("Cache size: " + stats.estimatedSize());

// 清除缓存
dicomService.clearCache();

// 预热缓存
List<String> examIds = Arrays.asList("exam1", "exam2", "exam3");
dicomService.warmupCache(examIds);
```

## 11. 扩展和维护

### 11.1 添加新的 DICOM 标签

1. 在`DicomTagConstants`中定义新标签常量
2. 在`DicomTagParser`中添加标签解析逻辑
3. 在相应的 DICOM 对象模型中添加访问方法
4. 更新相关文档和测试用例

### 11.2 支持新的设备类型

1. 研究新设备的专用 DICOM 标签
2. 在`DicomTagConstants`中添加设备专用标签
3. 在`DicomTagParser`中添加设备特定的解析逻辑
4. 更新配置文件和文档

### 11.3 性能调优

1. 监控缓存命中率和性能指标
2. 根据实际使用情况调整缓存配置
3. 优化数据库查询和索引
4. 使用性能分析工具识别瓶颈

## 12. 注意事项

1. **内存管理**: DICOM 图像数据占用大量内存，需要及时释放
2. **线程安全**: 多线程环境下需要确保数据访问的线程安全
3. **异常处理**: 对文件 IO 和数据解析异常进行适当处理
4. **数据完整性**: 验证 DICOM 数据的完整性和有效性
5. **向后兼容**: 保持与旧版本 DICOM 标准的兼容性
