# 字符串硬编码提取会话记录

## 会话概述

本次会话主要围绕提取代码中的字符串硬编码进行，将它们移到统一的地方管理，提高代码的可维护性和国际化能力。

## 问题描述

在 `AnalysisPresenter` 类中，存在大量的字符串硬编码，如错误消息、提示信息、日志关键词等。这些硬编码字符串散布在代码各处，不利于统一管理和维护，也不利于未来的国际化工作。

## 解决方案

1. 创建一个消息枚举 `AnalysisMessages`，集中管理所有消息字符串
2. 将消息定义添加到 `messages.properties` 配置文件中
3. 将 `AnalysisPresenter` 类中的硬编码字符串替换为枚举引用
4. 使用消息枚举的 `format` 和 `toStr` 方法处理带参数的消息格式化

## 实现步骤

### 1. 创建消息枚举

创建了 `AnalysisMessages` 枚举，集中管理所有消息字符串：

```java
package com.ge.med.ct.laf2.message;

import com.ge.med.ct.exception.message.AbstractMessage;
import com.ge.med.ct.exception.message.Message;

/**
 * 分析模块的消息枚举
 * 定义分析模块中使用的所有消息
 */
public enum AnalysisMessages implements Message {
    // 分析状态消息
    ANALYSIS_IN_PROGRESS("analysis.in.progress", "分析已经在进行中，请等待当前分析完成"),
    ANALYSIS_STARTED("analysis.started", "开始分析..."),
    ANALYSIS_COMPLETED("analysis.completed", "分析成功完成，结果报告已生成"),
    ANALYSIS_FAILED("analysis.failed", "分析失败，未能生成有效报告"),
    PROCESS_RESULT_ERROR("analysis.process.result.error", "处理结果时发生错误: {0}"),

    // 协议相关消息
    PROTOCOL_INFO("analysis.protocol.info", "分析协议: {0} - {1}"),
    SERIES_PREFIX("analysis.series.prefix", "Series "),

    // 错误消息
    UNKNOWN_ERROR("analysis.unknown.error", "未知错误"),
    ANALYSIS_ERROR("analysis.error", "分析失败: {0}"),
    EXECUTION_FAILED("analysis.execution.failed", "执行失败: {0}"),

    // 文件相关消息
    OUTPUT_FILE_WARNING("analysis.output.file.warning", "警告: 输出文件未生成"),

    // 日志过滤关键词
    ERROR_KEYWORD("analysis.log.error", "ERROR"),
    ERROR_KEYWORD_CN("analysis.log.error.cn", "错误"),
    WARN_KEYWORD("analysis.log.warn", "WARN"),
    WARN_KEYWORD_CN("analysis.log.warn.cn", "警告"),
    DEBUG_KEYWORD("analysis.log.debug", "DEBUG"),
    CLEANUP_KEYWORD("analysis.log.cleanup", "Cleaning up"),
    PROTOCOL_MGR_KEYWORD("analysis.log.protocol", "ProtocolMgr"),
    IA_PREFIX("analysis.log.ia", "[IA]"),

    // 错误消息提取关键词
    IO_ERROR_PREFIX("analysis.io.error.prefix", "IO错误:"),
    CREATE_PROCESS_ERROR("analysis.create.process.error", "CreateProcess error="),
    ELLIPSIS("analysis.ellipsis", "...");

    private final AbstractMessage delegate;

    AnalysisMessages(String key, String defaultMessage) {
        this.delegate = new AbstractMessage(key, defaultMessage) {};
    }

    @Override
    public String getKey() {
        return delegate.getKey();
    }

    @Override
    public String getDefaultMessage() {
        return delegate.getDefaultMessage();
    }

    @Override
    public Message format(Object... args) {
        return delegate.format(args);
    }

    @Override
    public String toStr() {
        return delegate.toStr();
    }
}
```

### 2. 更新消息配置文件

根据项目的国际化机制，我们需要在两个不同的配置文件中添加消息：

1. 在 `messages.properties` 中添加英文消息（默认语言）：

```properties
# Analysis module messages
analysis.error=Analysis failed: {0}
analysis.timeout=Analysis timeout
analysis.unsupported=Unsupported analysis type: {0}

# Analysis status messages
analysis.in.progress=Analysis is already in progress, please wait for the current analysis to complete
analysis.started=Starting analysis...
analysis.completed=Analysis completed successfully, report generated
analysis.failed=Analysis failed, could not generate valid report
analysis.process.result.error=Error processing result: {0}

# Protocol related messages
analysis.protocol.info=Analysis protocol: {0} - {1}
analysis.series.prefix=Series

# Error messages
analysis.unknown.error=Unknown error
analysis.execution.failed=Execution failed: {0}

# File related messages
analysis.output.file.warning=Warning: Output file not generated

# Error message extraction keywords
analysis.io.error.prefix=IO error:
analysis.create.process.error=CreateProcess error=
analysis.ellipsis=...
```

2. 在 `messages_zh_CN.properties` 中添加中文消息：

```properties
# 分析模块消息
analysis.error=分析失败: {0}
analysis.timeout=分析超时
analysis.unsupported=不支持的分析类型: {0}

# 分析状态消息
analysis.in.progress=分析已经在进行中，请等待当前分析完成
analysis.started=开始分析...
analysis.completed=分析成功完成，结果报告已生成
analysis.failed=分析失败，未能生成有效报告
analysis.process.result.error=处理结果时发生错误: {0}

# 协议相关消息
analysis.protocol.info=分析协议: {0} - {1}
analysis.series.prefix=Series

# 错误消息
analysis.unknown.error=未知错误
analysis.execution.failed=执行失败: {0}

# 文件相关消息
analysis.output.file.warning=警告: 输出文件未生成

# 错误消息提取关键词
analysis.io.error.prefix=IO错误:
analysis.create.process.error=CreateProcess error=
analysis.ellipsis=...
```

这样，系统会根据当前的语言环境自动选择合适的消息。

### 3. 日志过滤关键词处理

对于日志过滤关键词，我们决定不将它们放入配置文件中，因为：

1. 这些关键词是固定的技术标识符，不需要国际化
2. 它们是内部实现细节，不应该暴露给用户
3. 硬编码这些值更加直观和简单

因此，我们直接在代码中使用这些关键词：

```java
// 确定分析状态
if (output.contains("ERROR") || output.contains("错误")) {
    return AnalysisState.FAIL;
}

// 判断是否是重要的日志行
return (line.contains("ERROR") ||
        line.contains("错误") ||
        line.contains("WARN") ||
        line.contains("警告")) &&
       !line.contains("DEBUG") &&
       !line.contains("Cleaning up") &&
       !line.contains("ProtocolMgr") &&
       !line.trim().startsWith("[IA]");
```

### 4. 修改 AnalysisPresenter 类

将 `AnalysisPresenter` 类中的硬编码字符串替换为枚举引用：

1. 添加 `AnalysisMessages` 的导入
2. 修改 `onAnalysisRequested` 方法中的硬编码字符串
3. 修改 `executeAnalysis` 方法中的硬编码字符串
4. 修改 `determineAnalysisState` 方法中的硬编码字符串
5. 修改 `processResult` 方法中的硬编码字符串
6. 修改 `AnalysisWorker` 类中的硬编码字符串
7. 修改 `simplifyErrorMessage` 方法中的硬编码字符串
8. 修改 `isImportantLine` 方法中的硬编码字符串
9. 修改 `checkOutputFile` 方法中的硬编码字符串
10. 修改 `done` 方法中的硬编码字符串

### 4. 使用消息枚举的 format 和 toStr 方法

对于带参数的消息，使用消息枚举的 `format` 和 `toStr` 方法进行格式化，例如：

```java
publish(AnalysisMessages.PROTOCOL_INFO.format(params.getProtocolType(), params.getProtocolName()).toStr());
```

## 修改效果

修改前：
```java
logToView(MessageType.ERROR, "分析已经在进行中，请等待当前分析完成");
```

修改后：
```java
logToView(MessageType.ERROR, AnalysisMessages.ANALYSIS_IN_PROGRESS.toStr());
```

修改前：
```java
publish("分析协议: " + params.getProtocolType() + " - " + params.getProtocolName());
```

修改后：
```java
publish(AnalysisMessages.PROTOCOL_INFO.format(params.getProtocolType(), params.getProtocolName()).toStr());
```

## 结论

通过本次修改，将 `AnalysisPresenter` 类中的字符串硬编码提取到了 `AnalysisMessages` 枚举和消息配置文件中，实现了以下目标：

1. **提高了代码的可维护性**：所有消息字符串集中在一处管理，便于统一修改和维护
2. **完善了国际化支持**：
   - 英文消息放在 `messages.properties`（默认语言）
   - 中文消息放在 `messages_zh_CN.properties`（中文环境）
   - 系统会根据当前语言环境自动选择合适的消息
3. **使用了统一的消息处理机制**：
   - 使用 `Message` 接口和 `AbstractMessage` 类
   - 使用消息枚举的 `format` 和 `toStr` 方法处理带参数的消息
4. **减少了代码重复**：消息定义集中管理，避免了重复定义
5. **提高了代码质量**：与系统现有的消息处理机制保持一致，使代码更加规范

这种模式可以推广到其他类中，逐步消除整个项目中的字符串硬编码，提高代码的可维护性和国际化能力。通过正确使用项目的国际化机制，我们可以轻松支持多语言，提高软件的国际化水平。
