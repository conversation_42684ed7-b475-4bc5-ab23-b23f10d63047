package com.ge.med.ct.laf2.components;

import com.ge.med.ct.laf2.model.ProtocolTypeMode;

import javax.swing.*;
import java.awt.*;

/**
 * 协议类型下拉框组件
 * 用于显示和选择协议类型
 */
public class ProtocolTypeComboBox extends JComboBox<ProtocolTypeMode> {
    private static final long serialVersionUID = 1L;

    /**
     * 协议类型选择监听器接口
     */
    public interface ProtocolTypeSelectionListener {
        /**
         * 当选择协议类型时调用
         * @param selectedType 选中的协议类型
         */
        void onProtocolTypeSelected(ProtocolTypeMode selectedType);
    }

    private ProtocolTypeSelectionListener selectionListener;

    /**
     * 构造函数
     */
    public ProtocolTypeComboBox() {
        super(ProtocolTypeMode.getAllProtocolTypes());
        setupRenderer();
        setupListeners();
    }

    /**
     * 设置自定义渲染器
     */
    private void setupRenderer() {
        setRenderer(new DefaultListCellRenderer() {
            private static final long serialVersionUID = 1L;

            @Override
            public Component getListCellRendererComponent(JList<?> list, Object value,
                    int index, boolean isSelected, boolean cellHasFocus) {
                super.getListCellRendererComponent(list, value, index, isSelected, cellHasFocus);

                if (value instanceof ProtocolTypeMode) {
                    ProtocolTypeMode protocolType = (ProtocolTypeMode) value;

                    // 使用HTML表格来确保列对齐，并将显示分为两行
                    String html = String.format(
                        "<html>" +
                        "<div style='font-family:monospace; padding:2px;'>" +
                        "<div style='font-weight:bold; margin-bottom:3px;'>%s</div>" +
                        "<div style='color:gray; font-size:1em;'> %s,%s,[%s]分析</div>" +
                        "</div>" +
                        "</html>",
                        protocolType.getProtocolString(),
                        protocolType.getObjectType(),
                        protocolType.getDescription(),
                        protocolType.getImageSupport().getDescription()
                    );

                    setText(html);
                }

                // 设置高度以适应两行显示
                setPreferredSize(new Dimension(getPreferredSize().width, 38));

                return this;
            }
        });

        setSelectedIndex(0);
        setMaximumRowCount(12);
        setToolTipText("选择协议类型");
    }

    /**
     * 设置监听器
     */
    private void setupListeners() {
        addActionListener(e -> {
            ProtocolTypeMode selectedType = (ProtocolTypeMode) getSelectedItem();
            if (selectedType != null && selectionListener != null) {
                selectionListener.onProtocolTypeSelected(selectedType);
            }
        });
    }

    /**
     * 设置协议类型选择监听器
     * @param listener 监听器
     */
    public void setSelectionListener(ProtocolTypeSelectionListener listener) {
        this.selectionListener = listener;
    }

    /**
     * 根据协议字符串选择相应的协议类型
     * @param protocolString 协议字符串
     */
    public void selectByProtocolString(String protocolString) {
        if (protocolString == null || protocolString.isEmpty()) {
            return;
        }

        // 如果协议字符串包含"|"，提取第一部分作为协议类型
        String protocolType = protocolString;
        if (protocolString.contains("|")) {
            protocolType = protocolString.split("\\|")[0].trim();
        }

        // 查找匹配的协议类型并选中
        boolean found = false;
        for (int i = 0; i < getItemCount(); i++) {
            ProtocolTypeMode item = getItemAt(i);
            if (item.getProtocolString().equals(protocolType)) {
                setSelectedIndex(i);
                found = true;
                break;
            }
        }
        
        // 如果没有找到匹配项但协议类型不为空，则默认选择第一项
        if (!found && getItemCount() > 0 && !protocolType.equals("N/A")) {
            System.out.println("未找到匹配的协议类型: " + protocolType + "，使用默认项");
            setSelectedIndex(0);
        }
    }
}
