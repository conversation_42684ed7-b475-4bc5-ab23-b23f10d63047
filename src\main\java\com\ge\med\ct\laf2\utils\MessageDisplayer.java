package com.ge.med.ct.laf2.utils;

import com.ge.med.ct.exception.core.QAToolException;
import com.ge.med.ct.laf2.base.MessageType;

import javax.swing.*;
import java.awt.*;
import java.util.Objects;

/**
 * 消息显示器
 * 统一处理所有类型的消息显示，包括错误、警告、信息和确认对话框
 */
public class MessageDisplayer {

    private static final int MAX_STACK_TRACE_ELEMENTS = 10;

    private MessageDisplayer() {
        // 私有构造函数，防止实例化
    }

    /**
     * 显示错误消息
     * @param parent 父组件
     * @param message 错误消息
     */
    public static void showError(Component parent, String message) {
        JOptionPane.showMessageDialog(
            parent,
            message,
            "错误",
            JOptionPane.ERROR_MESSAGE
        );
    }

    /**
     * 显示警告消息
     * @param parent 父组件
     * @param message 警告消息
     */
    public static void showWarning(Component parent, String message) {
        JOptionPane.showMessageDialog(
            parent,
            message,
            "警告",
            JOptionPane.WARNING_MESSAGE
        );
    }

    /**
     * 显示信息消息
     * @param parent 父组件
     * @param message 信息消息
     */
    public static void showInfo(Component parent, String message) {
        JOptionPane.showMessageDialog(
            parent,
            message,
            "信息",
            JOptionPane.INFORMATION_MESSAGE
        );
    }

    /**
     * 显示确认对话框
     * @param parent 父组件
     * @param message 确认消息
     * @param title 标题
     * @return 用户选择结果
     */
    public static int showConfirm(Component parent, String message, String title) {
        return JOptionPane.showConfirmDialog(
            parent,
            message,
            title,
            JOptionPane.YES_NO_OPTION,
            JOptionPane.QUESTION_MESSAGE
        );
    }

    /**
     * 显示异常对话框
     * @param parent 父组件
     * @param title 标题
     * @param message 错误消息
     * @param error 异常对象
     */
    public static void showException(Component parent, String title, String message, Throwable error) {
        StringBuilder detailedMessage = new StringBuilder(message);

        if (error != null) {
            // 添加异常类型和消息
            detailedMessage.append("\n\n异常详情: ").append(error.getClass().getName());
            if (error.getMessage() != null) {
                detailedMessage.append(": ").append(error.getMessage());
            }

            // 添加异常原因
            Throwable cause = error.getCause();
            if (cause != null) {
                detailedMessage.append("\n原因: ").append(cause.getClass().getName());
                if (cause.getMessage() != null) {
                    detailedMessage.append(": ").append(cause.getMessage());
                }
            }

            // 添加堆栈跟踪
            detailedMessage.append("\n\n堆栈跟踪:\n");
            StackTraceElement[] stackTrace = error.getStackTrace();
            int limit = Math.min(MAX_STACK_TRACE_ELEMENTS, stackTrace.length);
            for (int i = 0; i < limit; i++) {
                detailedMessage.append("  ").append(stackTrace[i].toString()).append("\n");
            }
            if (stackTrace.length > limit) {
                detailedMessage.append("  ... ").append(stackTrace.length - limit).append(" more\n");
            }
        }

        JOptionPane.showMessageDialog(
            parent,
            detailedMessage.toString(),
            title,
            JOptionPane.ERROR_MESSAGE
        );
    }

    /**
     * 显示异常对话框
     * @param parent 父组件
     * @param ex QAToolException异常对象
     */
    public static void showException(Component parent, QAToolException ex) {
        StringBuilder message = new StringBuilder();
        message.append("错误: ").append(ex.getMessage()).append("\n");
        message.append("错误代码: ").append(ex.getErrorCode());

        showException(parent, "应用程序错误", message.toString(), ex.getCause() != null ? ex.getCause() : ex);
    }

    /**
     * 根据消息类型显示消息
     * @param parent 父组件
     * @param type 消息类型
     * @param message 消息内容
     */
    public static void showMessage(Component parent, MessageType type, String message) {
        Objects.requireNonNull(message, "消息内容不能为空");

        int messageType;
        String title;

        switch (type) {
            case ERROR:
                messageType = JOptionPane.ERROR_MESSAGE;
                title = "错误";
                break;
            case WARNING:
                messageType = JOptionPane.WARNING_MESSAGE;
                title = "警告";
                break;
            case SUCCESS:
                messageType = JOptionPane.INFORMATION_MESSAGE;
                title = "成功";
                break;
            case INFO:
            default:
                messageType = JOptionPane.INFORMATION_MESSAGE;
                title = "信息";
                break;
        }

        // 创建可滚动的文本区域，以便显示长消息
        JTextArea textArea = new JTextArea(message);
        textArea.setEditable(false);
        textArea.setWrapStyleWord(true);
        textArea.setLineWrap(true);

        // 计算文本区域的大小
        int rows = Math.min(20, message.split("\n").length + 2); // 最多20行
        int columns = 50;
        textArea.setRows(rows);
        textArea.setColumns(columns);

        // 创建滚动面板
        JScrollPane scrollPane = new JScrollPane(textArea);

        JOptionPane.showMessageDialog(
            parent,
            scrollPane,
            title,
            messageType
        );
    }
}
