package com.ge.med.ct.laf2.base;

import com.ge.med.ct.laf2.theming.ThemeConstants;

import javax.swing.*;
import javax.swing.plaf.ColorUIResource;
import javax.swing.plaf.FontUIResource;
import java.awt.*;
import java.util.Enumeration;

/**
 * 自定义外观和感觉，实现CT工作站风格的UI
 */
public class CTLookAndFeel {

    /**
     * 应用CT工作站风格的UI
     */
    public static void applyLookAndFeel() {
        try {
            // 尝试使用系统外观
            UIManager.setLookAndFeel(UIManager.getSystemLookAndFeelClassName());

            // 设置全局字体
            setUIFont(new FontUIResource(ThemeConstants.Fonts.getDefault()));

            // 设置组件颜色
            UIManager.put("Panel.background", new ColorUIResource(ThemeConstants.Colors.getPanelBackground()));
            UIManager.put("OptionPane.background", new ColorUIResource(ThemeConstants.Colors.getPanelBackground()));
            UIManager.put("Window.background", new ColorUIResource(ThemeConstants.Colors.getBackground()));
            UIManager.put("Frame.background", new ColorUIResource(ThemeConstants.Colors.getBackground()));
            UIManager.put("Dialog.background", new ColorUIResource(ThemeConstants.Colors.getPanelBackground()));

            // 设置按钮样式
            UIManager.put("Button.background", new ColorUIResource(ThemeConstants.Colors.getButtonBackground()));
            UIManager.put("Button.foreground", new ColorUIResource(ThemeConstants.Colors.getText()));
            UIManager.put("Button.border", BorderFactory.createCompoundBorder(
                    BorderFactory.createLineBorder(ThemeConstants.Colors.getButtonBorder(), 1),
                    BorderFactory.createEmptyBorder(3, 8, 3, 8)));
            UIManager.put("Button.focus", new ColorUIResource(new Color(0, 0, 0, 0)));
            UIManager.put("Button.select", new ColorUIResource(ThemeConstants.Colors.getSecondary()));

            // 设置文本框样式
            UIManager.put("TextField.background", new ColorUIResource(ThemeConstants.Colors.getFieldBackground()));
            UIManager.put("TextField.foreground", new ColorUIResource(ThemeConstants.Colors.getText()));
            UIManager.put("TextField.caretForeground", new ColorUIResource(ThemeConstants.Colors.getText()));
            UIManager.put("TextField.selectionBackground", new ColorUIResource(ThemeConstants.Colors.getSecondary()));
            UIManager.put("TextField.selectionForeground", new ColorUIResource(Color.WHITE));
            UIManager.put("TextField.border", BorderFactory.createCompoundBorder(
                    BorderFactory.createLineBorder(ThemeConstants.Colors.getBorder(), 1),
                    BorderFactory.createEmptyBorder(2, 5, 2, 5)));

            // 设置标签样式
            UIManager.put("Label.foreground", new ColorUIResource(ThemeConstants.Colors.getText()));
            UIManager.put("Label.background", new ColorUIResource(ThemeConstants.Colors.getPanelBackground()));

            // 设置工具栏样式
            UIManager.put("ToolBar.background", new ColorUIResource(ThemeConstants.Colors.getToolbarBackground()));
            UIManager.put("ToolBar.foreground", new ColorUIResource(Color.WHITE));
            UIManager.put("ToolBar.dockingBackground", new ColorUIResource(ThemeConstants.Colors.getSecondary()));
            UIManager.put("ToolBar.floatingBackground", new ColorUIResource(ThemeConstants.Colors.getToolbarBackground()));

            // 设置表格样式
            UIManager.put("Table.background", new ColorUIResource(Color.WHITE));
            UIManager.put("Table.foreground", new ColorUIResource(ThemeConstants.Colors.getText()));
            UIManager.put("Table.selectionBackground", new ColorUIResource(ThemeConstants.Colors.getTableSelectedRow()));
            UIManager.put("Table.selectionForeground", new ColorUIResource(ThemeConstants.Colors.getText()));
            UIManager.put("Table.gridColor", new ColorUIResource(ThemeConstants.Colors.getBorder()));
            UIManager.put("TableHeader.background", new ColorUIResource(ThemeConstants.Colors.getTableHeader()));
            UIManager.put("TableHeader.foreground", new ColorUIResource(Color.WHITE));
            UIManager.put("TableHeader.font", ThemeConstants.Fonts.getTableHeader());

            // 设置滚动条样式
            UIManager.put("ScrollBar.thumb", new ColorUIResource(ThemeConstants.Colors.getSecondary()));
            UIManager.put("ScrollBar.thumbHighlight", new ColorUIResource(ThemeConstants.Colors.getPrimary()));
            UIManager.put("ScrollBar.thumbDarkShadow", new ColorUIResource(ThemeConstants.Colors.getSecondary()));
            UIManager.put("ScrollBar.thumbShadow", new ColorUIResource(ThemeConstants.Colors.getSecondary()));
            UIManager.put("ScrollBar.track", new ColorUIResource(ThemeConstants.Colors.getPanelBackground()));
            UIManager.put("ScrollBar.trackHighlight", new ColorUIResource(ThemeConstants.Colors.getPanelBackground()));

            // 设置下拉框样式
            UIManager.put("ComboBox.background", new ColorUIResource(ThemeConstants.Colors.getFieldBackground()));
            UIManager.put("ComboBox.foreground", new ColorUIResource(ThemeConstants.Colors.getText()));
            UIManager.put("ComboBox.selectionBackground", new ColorUIResource(ThemeConstants.Colors.getSecondary()));
            UIManager.put("ComboBox.selectionForeground", new ColorUIResource(Color.WHITE));

            // 设置菜单样式
            UIManager.put("Menu.background", new ColorUIResource(ThemeConstants.Colors.getToolbarBackground()));
            UIManager.put("Menu.foreground", new ColorUIResource(Color.WHITE));
            UIManager.put("MenuItem.background", new ColorUIResource(ThemeConstants.Colors.getPanelBackground()));
            UIManager.put("MenuItem.foreground", new ColorUIResource(ThemeConstants.Colors.getText()));
            UIManager.put("MenuItem.selectionBackground", new ColorUIResource(ThemeConstants.Colors.getSecondary()));
            UIManager.put("MenuItem.selectionForeground", new ColorUIResource(Color.WHITE));

            // 设置分隔面板样式
            UIManager.put("SplitPane.background", new ColorUIResource(ThemeConstants.Colors.getPanelBackground()));
            UIManager.put("SplitPane.dividerSize", 8);
            UIManager.put("SplitPaneDivider.border", BorderFactory.createEmptyBorder());
            UIManager.put("SplitPane.dividerFocusColor", new ColorUIResource(ThemeConstants.Colors.getBorder()));

        } catch (Exception e) {
            System.err.println("无法应用CT工作站风格的UI: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 设置全局UI字体
     * @param font 要应用的字体
     */
    private static void setUIFont(FontUIResource font) {
        Enumeration<Object> keys = UIManager.getDefaults().keys();
        while (keys.hasMoreElements()) {
            Object key = keys.nextElement();
            Object value = UIManager.get(key);
            if (value instanceof FontUIResource) {
                UIManager.put(key, font);
            }
        }
    }
}