package com.ge.med.ct.dicom2.tag;

import org.dcm4che3.data.Tag;

// DICOM标签常量定义类
public final class DicomTagConstants {

    // 禁止实例化
    private DicomTagConstants() {
        throw new AssertionError("常量类不应被实例化");
    }

    public enum TagCategory {
        PATIENT, // 患者信息
        STUDY, // 研究信息
        SERIES, // 序列信息
        IMAGE, // 图像信息
        EQUIPMENT, // 设备信息
        GE_CT, // GE CT特有标签
        STANDARD, // 标准DICOM标签
        PRIVATE, // 私有标签
        OTHER // 其他标签
    }

    public static final class Patient {
        public static final String PATIENT_ID = "(0010,0020)";
        public static final int PATIENT_ID_TAG = Tag.PatientID;

        public static final String PATIENT_NAME = "(0010,0010)";
        public static final int PATIENT_NAME_TAG = Tag.PatientName;

        public static final String PATIENT_BIRTH_DATE = "(0010,0030)";
        public static final int PATIENT_BIRTH_DATE_TAG = Tag.PatientBirthDate;

        public static final String PATIENT_SEX = "(0010,0040)";
        public static final int PATIENT_SEX_TAG = Tag.PatientSex;

        public static final String PATIENT_AGE = "(0010,1010)";
        public static final int PATIENT_AGE_TAG = Tag.PatientAge;

        public static final String PATIENT_WEIGHT = "(0010,1030)";
        public static final int PATIENT_WEIGHT_TAG = Tag.PatientWeight;

        public static final String PATIENT_SIZE = "(0010,1020)";
        public static final int PATIENT_SIZE_TAG = Tag.PatientSize;

        public static final String OTHER_PATIENT_IDS = "(0010,1000)";
        public static final int OTHER_PATIENT_IDS_TAG = Tag.OtherPatientIDs;

        public static final String ADDITIONAL_PATIENT_HISTORY = "(0010,21B0)";
        public static final int ADDITIONAL_PATIENT_HISTORY_TAG = Tag.AdditionalPatientHistory;

        private Patient() {
        }
    }

    public static final class Study {
        public static final String STUDY_INSTANCE_UID = "(0020,000D)";
        public static final int STUDY_INSTANCE_UID_TAG = Tag.StudyInstanceUID;

        public static final String STUDY_ID = "(0020,0010)";
        public static final int STUDY_ID_TAG = Tag.StudyID;

        public static final String STUDY_DATE = "(0008,0020)";
        public static final int STUDY_DATE_TAG = Tag.StudyDate;

        public static final String STUDY_TIME = "(0008,0030)";
        public static final int STUDY_TIME_TAG = Tag.StudyTime;

        public static final String STUDY_DESCRIPTION = "(0008,1030)";
        public static final int STUDY_DESCRIPTION_TAG = Tag.StudyDescription;

        public static final String ACCESSION_NUMBER = "(0008,0050)";
        public static final int ACCESSION_NUMBER_TAG = Tag.AccessionNumber;

        public static final String REFERRING_PHYSICIAN_NAME = "(0008,0090)";
        public static final int REFERRING_PHYSICIAN_NAME_TAG = Tag.ReferringPhysicianName;

        public static final String PROTOCOL_NAME = "(0018,1030)";
        public static final int PROTOCOL_NAME_TAG = Tag.ProtocolName;

        private Study() {
        }
    }

    /**
     * 序列相关标签
     */
    public static final class Series {
        public static final String SERIES_INSTANCE_UID = "(0020,000E)";
        public static final int SERIES_INSTANCE_UID_TAG = Tag.SeriesInstanceUID;

        public static final String SERIES_NUMBER = "(0020,0011)";
        public static final int SERIES_NUMBER_TAG = Tag.SeriesNumber;

        public static final String SERIES_DESCRIPTION = "(0008,103E)";
        public static final int SERIES_DESCRIPTION_TAG = Tag.SeriesDescription;

        public static final String SERIES_DATE = "(0008,0021)";
        public static final int SERIES_DATE_TAG = Tag.SeriesDate;

        public static final String SERIES_TIME = "(0008,0031)";
        public static final int SERIES_TIME_TAG = Tag.SeriesTime;

        public static final String MODALITY = "(0008,0060)";
        public static final int MODALITY_TAG = Tag.Modality;

        public static final String BODY_PART_EXAMINED = "(0018,0015)";
        public static final int BODY_PART_EXAMINED_TAG = Tag.BodyPartExamined;

        // 扩展标签
        public static final String SERIES_STATUS = "(0040,0020)";
        public static final int SERIES_STATUS_TAG = 0x00400020;

        public static final String SERIES_PRIORITY = "(0040,0022)";
        public static final int SERIES_PRIORITY_TAG = 0x00400022;

        public static final String SERIES_COMPLETION_DATE = "(0040,0251)";
        public static final int SERIES_COMPLETION_DATE_TAG = 0x00400251;

        public static final String SERIES_COMPLETION_TIME = "(0040,0252)";
        public static final int SERIES_COMPLETION_TIME_TAG = 0x00400252;

        public static final String SERIES_VERIFIED_DATE = "(0040,0253)";
        public static final int SERIES_VERIFIED_DATE_TAG = 0x00400253;

        public static final String SERIES_VERIFIED_TIME = "(0040,0254)";
        public static final int SERIES_VERIFIED_TIME_TAG = 0x00400254;

        private Series() {
        }
    }

    /**
     * 图像相关标签
     */
    public static final class Image {
        public static final String SOP_INSTANCE_UID = "(0008,0018)";
        public static final int SOP_INSTANCE_UID_TAG = Tag.SOPInstanceUID;

        public static final String INSTANCE_NUMBER = "(0020,0013)";
        public static final int INSTANCE_NUMBER_TAG = Tag.InstanceNumber;

        public static final String IMAGE_TYPE = "(0008,0008)";
        public static final int IMAGE_TYPE_TAG = Tag.ImageType;

        public static final String ROWS = "(0028,0010)";
        public static final int ROWS_TAG = Tag.Rows;

        public static final String COLUMNS = "(0028,0011)";
        public static final int COLUMNS_TAG = Tag.Columns;

        public static final String BITS_ALLOCATED = "(0028,0100)";
        public static final int BITS_ALLOCATED_TAG = Tag.BitsAllocated;

        public static final String BITS_STORED = "(0028,0101)";
        public static final int BITS_STORED_TAG = Tag.BitsStored;

        public static final String HIGH_BIT = "(0028,0102)";
        public static final int HIGH_BIT_TAG = Tag.HighBit;

        public static final String SAMPLES_PER_PIXEL = "(0028,0002)";
        public static final int SAMPLES_PER_PIXEL_TAG = Tag.SamplesPerPixel;

        public static final String PHOTOMETRIC_INTERPRETATION = "(0028,0004)";
        public static final int PHOTOMETRIC_INTERPRETATION_TAG = Tag.PhotometricInterpretation;

        public static final String WINDOW_CENTER = "(0028,1050)";
        public static final int WINDOW_CENTER_TAG = Tag.WindowCenter;

        public static final String WINDOW_WIDTH = "(0028,1051)";
        public static final int WINDOW_WIDTH_TAG = Tag.WindowWidth;

        public static final String RESCALE_INTERCEPT = "(0028,1052)";
        public static final int RESCALE_INTERCEPT_TAG = Tag.RescaleIntercept;

        public static final String RESCALE_SLOPE = "(0028,1053)";
        public static final int RESCALE_SLOPE_TAG = Tag.RescaleSlope;

        public static final String PIXEL_SPACING = "(0028,0030)";
        public static final int PIXEL_SPACING_TAG = Tag.PixelSpacing;

        public static final String IMAGE_POSITION_PATIENT = "(0020,0032)";
        public static final int IMAGE_POSITION_PATIENT_TAG = Tag.ImagePositionPatient;

        public static final String IMAGE_ORIENTATION_PATIENT = "(0020,0037)";
        public static final int IMAGE_ORIENTATION_PATIENT_TAG = Tag.ImageOrientationPatient;

        public static final String PIXEL_DATA = "(7FE0,0010)";
        public static final int PIXEL_DATA_TAG = Tag.PixelData;

        public static final String SLICE_LOCATION = "(0020,1041)";
        public static final int SLICE_LOCATION_TAG = Tag.SliceLocation;

        public static final String IMAGE_COMMENTS = "(0020,4000)";
        public static final int IMAGE_COMMENTS_TAG = Tag.ImageComments;

        public static final String SLICE_THICKNESS = "(0018,0050)";
        public static final int SLICE_THICKNESS_TAG = Tag.SliceThickness;

        public static final String SPACING_BETWEEN_SLICES = "(0018,0088)";
        public static final int SPACING_BETWEEN_SLICES_TAG = Tag.SpacingBetweenSlices;

        public static final String PIXEL_REPRESENTATION = "(0028,0103)";
        public static final int PIXEL_REPRESENTATION_TAG = Tag.PixelRepresentation;

        public static final String RESCALE_TYPE = "(0028,1054)";
        public static final int RESCALE_TYPE_TAG = Tag.RescaleType;

        public static final String ACQUISITION_DATE = "(0008,0022)";
        public static final int ACQUISITION_DATE_TAG = Tag.AcquisitionDate;

        public static final String ACQUISITION_TIME = "(0008,0032)";
        public static final int ACQUISITION_TIME_TAG = Tag.AcquisitionTime;

        public static final String FRAME_OF_REFERENCE_UID = "(0020,0052)";
        public static final int FRAME_OF_REFERENCE_UID_TAG = Tag.FrameOfReferenceUID;

        public static final String POSITION_REFERENCE_INDICATOR = "(0020,1040)";
        public static final int POSITION_REFERENCE_INDICATOR_TAG = Tag.PositionReferenceIndicator;

        // 特殊标签
        public static final String PHASE = "00204000";

        private Image() {
        }
    }

    /**
     * 设备相关标签
     */
    public static final class Equipment {
        public static final String MANUFACTURER = "(0008,0070)";
        public static final int MANUFACTURER_TAG = Tag.Manufacturer;

        public static final String MANUFACTURER_MODEL_NAME = "(0008,1090)";
        public static final int MANUFACTURER_MODEL_NAME_TAG = Tag.ManufacturerModelName;

        public static final String DEVICE_SERIAL_NUMBER = "(0018,1000)";
        public static final int DEVICE_SERIAL_NUMBER_TAG = Tag.DeviceSerialNumber;

        public static final String SOFTWARE_VERSIONS = "(0018,1020)";
        public static final int SOFTWARE_VERSIONS_TAG = Tag.SoftwareVersions;

        public static final String STATION_NAME = "(0008,1010)";
        public static final int STATION_NAME_TAG = Tag.StationName;

        public static final String INSTITUTION_NAME = "(0008,0080)";
        public static final int INSTITUTION_NAME_TAG = Tag.InstitutionName;

        public static final String INSTITUTION_ADDRESS = "(0008,0081)";
        public static final int INSTITUTION_ADDRESS_TAG = Tag.InstitutionAddress;

        public static final String INSTITUTIONAL_DEPARTMENT_NAME = "(0008,1040)";
        public static final int INSTITUTIONAL_DEPARTMENT_NAME_TAG = Tag.InstitutionalDepartmentName;

        public static final String STATION_AE_TITLE = "(0008,0054)";
        public static final int STATION_AE_TITLE_TAG = Tag.RetrieveAETitle;

        private Equipment() {
        }
    }

    /**
     * CT相关标签
     */
    public static final class CT {
        public static final String KVP = "(0018,0060)";
        public static final int KVP_TAG = Tag.KVP;

        public static final String FIELD_OF_VIEW = "(0018,0090)";
        public static final int FIELD_OF_VIEW_TAG = Tag.DataCollectionDiameter;

        public static final String RECONSTRUCTION_DIAMETER = "(0018,1100)";
        public static final int RECONSTRUCTION_DIAMETER_TAG = Tag.ReconstructionDiameter;

        public static final String CONVOLUTION_KERNEL = "(0018,1210)";
        public static final int CONVOLUTION_KERNEL_TAG = Tag.ConvolutionKernel;

        public static final String GANTRY_TILT = "(0018,1120)";
        public static final int GANTRY_TILT_TAG = Tag.GantryDetectorTilt;

        public static final String XRAY_TUBE_CURRENT = "(0018,1151)";
        public static final int XRAY_TUBE_CURRENT_TAG = Tag.XRayTubeCurrent;

        public static final String EXPOSURE_TIME = "(0018,1150)";
        public static final int EXPOSURE_TIME_TAG = Tag.ExposureTime;

        public static final String PATIENT_POSITION = "(0018,5100)";
        public static final int PATIENT_POSITION_TAG = Tag.PatientPosition;

        public static final String ACQUISITION_NUMBER = "(0020,0012)";
        public static final int ACQUISITION_NUMBER_TAG = Tag.AcquisitionNumber;

        private CT() {
        }
    }

    /**
     * GE CT特有标签
     */
    public static final class GECT {
        // GE私有标签组
        public static final String CT_TUBE_CURRENT_IN_MA = "(0019,10BB)";
        public static final int CT_TUBE_CURRENT_IN_MA_TAG = 0x001910BB;

        public static final String CT_TUBE_VOLTAGE_IN_KV = "(0019,10BC)";
        public static final int CT_TUBE_VOLTAGE_IN_KV_TAG = 0x001910BC;

        public static final String RECONSTRUCTION_ALGORITHM = "(0043,1060)";
        public static final int RECONSTRUCTION_ALGORITHM_TAG = 0x00431060;

        public static final String ACQUISITION_WORKLIST_NUMBER = "(0045,1001)";
        public static final int ACQUISITION_WORKLIST_NUMBER_TAG = 0x00451001;

        public static final String ACTUAL_SLICE_THICKNESS = "(0019,10DF)";
        public static final int ACTUAL_SLICE_THICKNESS_TAG = 0x001910DF;

        // GEMS_IDEN_01 组 (0009,xxxx)
        public static final String PRIVATE_CREATOR_GEMS_IDEN_01 = "(0009,0010)";
        public static final String CT_LIGHTSPEED = "(0009,1001)";
        public static final String STATION_ID = "(0009,1002)";
        public static final String DEVICE_MODEL_NAME = "(0009,1004)";
        public static final String TIMESTAMP_SECONDS_SINCE_EPOCH = "(0009,1027)";

        // GEMS_ACQU_01 组 (0019,xxxx)
        public static final String PRIVATE_CREATOR_GEMS_ACQU_01 = "(0019,0010)";
        public static final String VIEW_OFFSET = "(0019,1002)";
        public static final String VIEW_CENTER_POINT = "(0019,1003)";
        public static final String HORIZONTAL_FRAME_OF_REFERENCE = "(0019,100F)";
        public static final String SERIES_CONTRAST = "(0019,1011)";
        public static final String FIRST_SCAN_RAS = "(0019,1018)";
        public static final String LAST_SCAN_RAS = "(0019,101A)";
        public static final String TABLE_SPEED = "(0019,1023)";
        public static final String MID_SCAN_TIME = "(0019,1024)";
        public static final String MID_SCAN_FLAG = "(0019,1025)";
        public static final String EFF_DURATION = "(0019,1026)";
        public static final String CTDIVOL = "(0019,1027)";
        public static final String NUM_OF_REF_CHANNELS = "(0019,102C)";
        public static final String PHANTOM_TYPE = "(0019,102E)";
        public static final String SOURCE_CURRENT = "(0019,102F)";
        public static final String VIEW_MATRIX_SIZE = "(0019,1039)";
        public static final String DATA_TYPE = "(0019,1042)";
        public static final String PADDING = "(0019,1043)";
        public static final String INTERPOLATION_TYPE = "(0019,1047)";
        public static final String RECON_FILTER_TYPE = "(0019,1052)";
        public static final String SCAN_MODE = "(0019,106A)";

        private GECT() {
        }
    }
}