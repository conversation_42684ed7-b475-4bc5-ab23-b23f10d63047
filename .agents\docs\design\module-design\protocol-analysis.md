# 协议分析模块设计文档

## 1. 概述

协议分析模块是 CT 质量保证工具的核心组件之一，负责从 DICOM 序列数据中自动推断和识别质量保证协议类型。该模块通过分析 DICOM 标签信息、序列描述和技术参数，智能确定适合的分析协议，为后续的图像质量分析提供基础。

## 2. 模块目标

协议分析模块的主要目标包括：

- 自动识别 DICOM 序列的质量保证协议类型
- 支持多种 CT 质量保证测试协议（MTF、LCD、MEANS、CTDIw 等）
- 提供灵活的协议推断规则和配置
- 支持不同 CT 设备型号的协议变体
- 确保协议识别的准确性和可靠性

## 3. 模块架构

### 3.1 核心组件

协议分析模块包含以下核心组件：

1. **协议推断服务(ProtocolInferService)**: 负责协议推断的核心引擎
2. **协议类型模式(ProtocolTypeMode)**: 负责协议类型定义和管理
3. **推断上下文(InferenceContext)**: 负责封装推断所需的上下文信息
4. **协议类型组合框(ProtocolTypeComboBox)**: 负责 UI 中的协议选择

### 3.2 组件关系图

```
+----------------------+       +----------------------+
| ProtocolInferService |------>| ProtocolTypeMode     |
+----------------------+       +----------------------+
| -inferFromSeries()   |       | -getAllProtocolTypes()|
| -inferProtocolString()|       | -protocolTypes       |
| -inferProtocolName() |       +----------------------+
| -createContext()     |
+----------------------+       +----------------------+
         |                     | InferenceContext     |
         v                     +----------------------+
+----------------------+       | -seriesDescription   |
| ProtocolTypeComboBox |       | -dicomTags          |
+----------------------+       | -configVariant      |
| -updateProtocolTypes()|       +----------------------+
| -getSelectedProtocol()|
+----------------------+
```

### 3.3 数据流

协议分析模块的主要数据流如下：

```
DICOM序列数据 --> 创建推断上下文 --> 协议字符串推断 --> 协议名称推断 --> 格式化协议结果
```

## 4. 支持的协议类型

### 4.1 图像质量协议

- **MTF10_50**: 调制传递函数测试（10%-50%）
- **MTF4_50**: 调制传递函数测试（4%-50%）
- **LCD**: 低对比度分辨率测试
- **MEANS**: 均匀性和噪声测试
- **SERIES_MEANS**: 序列均匀性测试

### 4.2 几何和定位协议

- **BEAM_WIDTH**: 射束宽度测试
- **SSP**: 切片敏感性轮廓测试
- **CTDIw**: CT 剂量指数测试

### 4.3 质量保证协议

- **QA1**: 质量保证测试 1
- **QA2**: 质量保证测试 2
- **QA3**: 质量保证测试 3

### 4.4 伪影检测协议

- **STREAK**: 条纹伪影检测
- **RING**: 环形伪影检测
- **CLUMP**: 团块伪影检测
- **CENTER_SMUDGE**: 中心污迹检测
- **LARGE_CENTER_SMUDGE**: 大中心污迹检测
- **CENTER_ARTIFACT**: 中心伪影检测
- **CENTER_SPOT**: 中心斑点检测
- **BAND**: 带状伪影检测

### 4.5 性能测试协议

- **GE_PERF1**: GE 性能测试 1
- **GE_PERF2**: GE 性能测试 2
- **VISIBLE_LINES**: 可见线条测试
- **VISIBLE_HOLES**: 可见孔洞测试
- **P35_NOISE**: P35 噪声测试
- **MANUAL_ROI**: 手动 ROI 测试
- **ROW_COMB_SERIES_MEANS**: 行梳序列均值测试

## 5. 协议推断逻辑

### 5.1 推断策略

协议推断采用多层次策略：

1. **序列描述匹配**: 优先通过 DICOM 序列描述进行精确匹配
2. **技术参数推断**: 基于 KVP、层厚等技术参数进行推断
3. **默认协议**: 当无法确定时使用默认协议（MTF）

### 5.2 推断规则

#### 5.2.1 基于序列描述的规则

```java
// LCD 相关
if (upperDesc.contains("LCD")) return PROTOCOL_LCD;

// MTF 相关
if (upperDesc.contains("MTF")) {
    if (upperDesc.contains("MTF4") || upperDesc.contains("MTF 4"))
        return PROTOCOL_MTF4_50;
    return PROTOCOL_MTF;
}

// QA 相关
if (upperDesc.contains("QA3")) return PROTOCOL_QA3;
if (upperDesc.contains("QA2")) return PROTOCOL_QA2;
if (upperDesc.contains("QA1")) return PROTOCOL_QA1;
```

#### 5.2.2 基于技术参数的规则

```java
// 基于KVP推断
if (kv <= 80) return PROTOCOL_LCD;
if (kv >= 140) return PROTOCOL_MEANS;

// 基于层厚推断
if (thickness <= 0.625f) return PROTOCOL_MTF;
if (thickness >= 10.0f) return PROTOCOL_BEAM_WIDTH;
if (thickness <= 1.25f) return PROTOCOL_QA3;
if (thickness >= 5.0f) return PROTOCOL_MEANS;
```

### 5.3 配置变体支持

模块支持不同 CT 设备型号的配置变体：

- **revo_evo**: Revolution EVO 系列
- **vcthd64**: VCT HD 64 层
- **vct64**: VCT 64 层
- **vct32**: VCT 32 层
- **hl16**: LightSpeed 16 层

## 6. 协议类型管理

### 6.1 协议类型定义

每个协议类型包含以下属性：

- **协议字符串**: 用于内部识别的协议标识
- **对象类型**: 对应的分析对象类型
- **描述**: 协议的中文描述
- **图像支持模式**: 支持的图像类型

### 6.2 协议类型示例

```java
// MTF协议定义
protocolTypes.add(new ProtocolType("MTF10_50", "MTFContrast2",
    "调制传递函数(MTF)分析", ImageSupportMode.SINGLE_IMAGE));

// LCD协议定义
protocolTypes.add(new ProtocolType("LCD", "Lcd",
    "低对比度分辨率分析", ImageSupportMode.SINGLE_IMAGE));

// CTDIw协议定义
protocolTypes.add(new ProtocolType("CTDIw", "DoseAnalysis",
    "分析CT剂量指数（CTDIw）", ImageSupportMode.SINGLE_IMAGE));
```

## 7. 接口定义

### 7.1 ProtocolInferService 接口

```java
/**
 * 协议推断服务
 * 提供从DICOM序列推断IA协议参数的功能
 */
public class ProtocolInferService {
    /**
     * 从序列中推断协议参数
     * @param series DICOM序列
     * @param exam 相关检查
     * @param images 序列图像列表
     * @return 格式化的协议字符串 (格式：协议字符串|协议名称|协议ID)
     */
    public String inferFromSeries(DicomSeries series, DicomExam exam,
                                 List<DicomImage> images);

    /**
     * 创建推断上下文
     * @param series DICOM序列
     * @param exam 相关检查
     * @param images 序列图像列表
     * @return 推断上下文
     */
    private InferenceContext createContext(DicomSeries series, DicomExam exam,
                                          List<DicomImage> images);

    /**
     * 推断协议字符串
     * @param context 推断上下文
     * @return 协议字符串
     */
    private String inferProtocolString(InferenceContext context);

    /**
     * 推断协议名称
     * @param context 推断上下文
     * @return 协议名称
     */
    private String inferProtocolName(InferenceContext context);
}
```

### 7.2 ProtocolTypeMode 接口

```java
/**
 * 协议类型模式
 * 负责协议类型定义和管理
 */
public class ProtocolTypeMode {
    /**
     * 获取所有协议类型
     * @return 协议类型列表
     */
    public static List<ProtocolType> getAllProtocolTypes();

    /**
     * 根据协议字符串查找协议类型
     * @param protocolString 协议字符串
     * @return 协议类型，未找到返回null
     */
    public static ProtocolType findByProtocolString(String protocolString);
}
```

## 8. 使用示例

### 8.1 协议推断示例

```java
// 创建协议推断服务
ProtocolInferService inferService = new ProtocolInferService();

// 从DICOM序列推断协议
String protocolResult = inferService.inferFromSeries(series, exam, images);

// 解析结果
String[] parts = protocolResult.split("\\|");
String protocolString = parts[0];  // 协议字符串
String protocolName = parts[1];    // 协议名称
String protocolId = parts[2];      // 协议ID
```

### 8.2 协议类型查询示例

```java
// 获取所有协议类型
List<ProtocolType> allTypes = ProtocolTypeMode.getAllProtocolTypes();

// 查找特定协议类型
ProtocolType mtfType = ProtocolTypeMode.findByProtocolString("MTF10_50");
if (mtfType != null) {
    String objectType = mtfType.getObjectType();  // "MTFContrast2"
    String description = mtfType.getDescription(); // "调制传递函数(MTF)分析"
}
```

## 9. 扩展和维护

### 9.1 添加新协议类型

1. 在`ProtocolInferService`中定义新的协议常量
2. 在`ProtocolTypeMode.getAllProtocolTypes()`中添加协议定义
3. 在`inferProtocolString()`方法中添加推断规则
4. 更新相关文档和测试用例

### 9.2 修改推断规则

1. 修改`inferProtocolString()`方法中的匹配逻辑
2. 调整技术参数的阈值设置
3. 更新配置变体的映射关系
4. 验证修改对现有协议识别的影响

### 9.3 性能优化

1. 缓存常用的推断结果
2. 优化字符串匹配算法
3. 减少不必要的 DICOM 标签访问
4. 使用更高效的数据结构

## 10. 注意事项

1. **大小写敏感性**: 序列描述匹配时统一转换为大写进行比较
2. **默认处理**: 当无法确定协议类型时，使用 MTF 作为默认协议
3. **异常处理**: 对数值转换等操作进行异常捕获和处理
4. **向后兼容**: 新增协议类型时保持与现有系统的兼容性
5. **国际化支持**: 协议描述支持中英文，考虑英式和美式拼写差异
