package com.ge.med.ct.laf2.base.listeners;

import com.ge.med.ct.laf2.base.MessageType;

/**
 * 视图监听器基础接口，定义所有视图监听器共有的方法
 */
public interface IViewListener {
    /**
     * 处理消息事件
     * @param type 消息类型
     * @param message 消息内容
     */
    void onMessage(MessageType type, String message);

    /**
     * 处理视图初始化完成事件
     * 默认实现为空，子类可根据需要覆盖
     */
    default void onViewInitialized() {
        // 默认不执行任何操作
    }

    /**
     * 处理视图销毁事件
     * 默认实现为空，子类可根据需要覆盖
     */
    default void onViewDestroyed() {
        // 默认不执行任何操作
    }
}