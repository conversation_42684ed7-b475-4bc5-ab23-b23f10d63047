# 表格列配置设计文档

## 1. 概述

本文档描述了CT质量保证工具中表格列配置的设计与实现逻辑。

CT质量保证工具需要展示不同类型的表格数据，如检查、序列和图像等。为了实现灵活的表格展示和数据提取，采用配置驱动的方式来定义表格列的结构和数据映射关系。通过配置文件，可以轻松调整表格列名、顺序以及与DICOM标签的映射关系，从而满足不同用户和场景的需求。

表格列配置是CT质量保证工具中表格数据展示的核心配置，用于定义不同类型表格(检查/序列/图像)的列结构和数据映射关系。本文档详细说明表格列配置的设计和实现逻辑。

## 2. 设计目标

- 提供灵活的表格列配置机制，支持自定义列名和顺序
- 建立列名与DICOM标签的映射关系，实现数据的自动提取
- 支持配置文件驱动，便于修改和扩展
- 确保配置的一致性和可维护性

## 3. 配置架构

### 3.1 核心组件

1. **TableConfig**: 表格配置类，负责加载和管理表格列配置
2. **ConfigReader**: 配置读取器，负责从配置文件加载配置
3. **table_columns.properties**: 表格列配置文件，定义列名和标签映射

### 3.2 配置模型

```
+------------------+       +------------------+
| ConfigReader     |------>| TableConfig      |
+------------------+       +------------------+
| -instance        |       | -columnConfigs   |
| -tableConfig     |       | -tagIdMappings   |
+------------------+       +------------------+
| +getInstance()   |       | +getColumnNames()|
| +getTableConfig()|       | +getColumnTagId()|
+------------------+       +------------------+
```

### 3.3 配置流程

```
配置文件 --> ConfigReader --> TableConfig --> TableDataEngine
```

## 4. 配置文件结构

表格列配置文件(table_columns.properties)采用Java属性文件格式，主要包含以下配置项：

### 4.1 列名定义

为每种表格类型定义列名列表，列名之间用逗号分隔：

```properties
# 检查表格列配置
exam.columns=PatientID,Name,Date,StudyID,Modality,Description,StationName

# 序列表格列配置
series.columns=Series,Type,Images,Description,Manufacturer

# 图像表格列配置
image.columns=image,imgctr,thick,tilt,sfov,dfov,matrix,kv,alg,ph
```

### 4.2 标签映射定义

为每个列名定义对应的DICOM标签ID：

```properties
# 检查表格标签映射
exam.PatientID.tagId=00100020
exam.Name.tagId=00100010
exam.Date.tagId=00080020
exam.StudyID.tagId=00200010
exam.Modality.tagId=00080060
exam.Description.tagId=00081030
exam.StationName.tagId=00081010

# 图像表格标签映射
image.image.tagId=00200013
image.thick.tagId=00180050
image.tilt.tagId=00181120
image.sfov.tagId=00181100
image.dfov.tagId=00181100
image.matrix.tagId=00280010
image.kv.tagId=00180060
image.alg.tagId=00181210
image.ph.tagId=00541300
```

### 4.3 列显示名定义(可选)

为列名定义显示名，用于表格头部显示：

```properties
# 检查表格列显示名
exam.PatientID.displayName=患者ID
exam.Name.displayName=姓名
exam.Date.displayName=日期

# 图像表格列显示名
image.image.displayName=图像
image.thick.displayName=层厚
image.tilt.displayName=倾角
image.ph.displayName=相位
```

## 5. TableConfig类设计

TableConfig类负责加载和管理表格列配置，主要包含以下功能：

### 5.1 配置加载

从配置文件加载表格列配置，包括列名列表和标签映射：

```java
public void loadConfig(Properties props) {
    // 加载列名配置
    loadColumnNames(props);

    // 加载标签映射配置
    loadTagIdMappings(props);

    // 加载列显示名配置(可选)
    loadDisplayNames(props);
}
```

### 5.2 列名获取

根据表格类型获取列名列表：

```java
public List<String> getColumnNames(String tableType) {
    List<String> columns = columnConfigs.get(tableType);
    return columns != null ? columns : new ArrayList<>();
}
```

### 5.3 标签ID获取

根据表格类型和列名获取对应的DICOM标签ID：

```java
public String getColumnTagId(String tableType, String columnName) {
    String key = tableType + "." + columnName;
    return tagIdMappings.getOrDefault(key, "");
}
```

### 5.4 列显示名获取

根据表格类型和列名获取对应的显示名：

```java
public String getColumnDisplayName(String tableType, String columnName) {
    String key = tableType + "." + columnName;
    return displayNames.getOrDefault(key, columnName);
}
```

## 6. 配置使用

### 6.1 在TableDataEngine中使用配置

TableDataEngine通过TableConfig获取列名和标签ID：

```java
// 获取表格列名
public String[] getTableColumnNames(String tableType) {
    List<String> columns = tableConfig.getColumnNames(tableType);
    return columns.toArray(new String[0]);
}

// 获取列对应的标签ID
private String getColumnTagId(String tableType, String columnName) {
    return tableConfig.getColumnTagId(tableType, columnName);
}
```

### 6.2 在DataConverter中使用配置

DataConverter根据列名和标签ID从DICOM对象中提取数据：

```java
private DataConverter<DicomImage> createImageConverter() {
    return (image, columnName, tableType) -> {
        if (image == null) return "";

        // 使用配置文件中定义的列名
        switch (columnName.toLowerCase()) {
            case "image":
                return image.getTagValue(INSTANCE_NUMBER);
            // ...其他列处理...
            case "ph":
                return image.getTagValue(PHASE);
            default:
                String tagId = getColumnTagId(tableType, columnName);
                return !tagId.isEmpty() ? convertTagValue(tagId, image.getTagValue(tagId)) : "";
        }
    };
}
```

## 7. 配置扩展

### 7.1 添加新列

要添加新列，只需在配置文件中添加列名和标签映射：

```properties
# 添加新列
image.columns=image,imgctr,thick,tilt,sfov,dfov,matrix,kv,alg,ph,newColumn
image.newColumn.tagId=00181315
image.newColumn.displayName=新列名
```

### 7.2 修改列顺序

修改列顺序只需调整columns配置中的列名顺序：

```properties
# 修改列顺序
image.columns=image,newColumn,imgctr,thick,tilt,sfov,dfov,matrix,kv,alg,ph
```

### 7.3 添加新表格类型

添加新表格类型需要在配置文件中添加新的表格类型配置：

```properties
# 添加新表格类型
newType.columns=column1,column2,column3
newType.column1.tagId=00100020
newType.column2.tagId=00100010
newType.column3.tagId=00080020
```

## 8. 配置示例

完整的表格列配置示例：

```properties
# 表格类型常量
table.type.exam=exam
table.type.series=series
table.type.image=image

# 检查表格列配置
exam.columns=PatientID,Name,Date,StudyID,Modality,Description,StationName
exam.PatientID.tagId=00100020
exam.PatientID.displayName=患者ID
exam.Name.tagId=00100010
exam.Name.displayName=姓名
exam.Date.tagId=00080020
exam.Date.displayName=日期
exam.StudyID.tagId=00200010
exam.StudyID.displayName=检查ID
exam.Modality.tagId=00080060
exam.Modality.displayName=模态
exam.Description.tagId=00081030
exam.Description.displayName=描述
exam.StationName.tagId=00081010
exam.StationName.displayName=设备名称

# 序列表格列配置
series.columns=Series,Type,Images,Description,Manufacturer
series.Series.tagId=00200011
series.Series.displayName=序列号
series.Type.tagId=00080060
series.Type.displayName=类型
series.Images.tagId=00201209
series.Images.displayName=图像数
series.Description.tagId=0008103E
series.Description.displayName=描述
series.Manufacturer.tagId=00080070
series.Manufacturer.displayName=制造商

# 图像表格列配置
image.columns=image,imgctr,thick,tilt,sfov,dfov,matrix,kv,alg,ph
image.image.tagId=00200013
image.image.displayName=图像
image.imgctr.displayName=位置
image.thick.tagId=00180050
image.thick.displayName=层厚
image.tilt.tagId=00181120
image.tilt.displayName=倾角
image.sfov.tagId=00181100
image.sfov.displayName=扫描视野
image.dfov.tagId=00181100
image.dfov.displayName=重建视野
image.matrix.tagId=00280010
image.matrix.displayName=矩阵
image.kv.tagId=00180060
image.kv.displayName=电压
image.alg.tagId=00181210
image.alg.displayName=算法
image.ph.tagId=00541300
image.ph.displayName=相位
```

## 9. 列配置逻辑分析

### 9.1 配置加载逻辑

表格列配置的加载过程如下：

1. **配置文件路径获取**：从configuration.properties中获取table.config.file配置项
2. **配置文件加载**：使用Java Properties API加载配置文件
3. **列名解析**：解析各表格类型的columns配置项，存入columnConfigs集合
4. **标签映射解析**：解析各列的tagId配置项，存入tagIdMappings集合
5. **显示名解析**：解析各列的displayName配置项，存入displayNames集合

### 9.2 列名解析逻辑

列名解析的具体逻辑如下：

```java
private void loadColumnNames(Properties props) {
    // 遍历所有表格类型
    for (String tableType : TABLE_TYPES) {
        String columnsKey = tableType + ".columns";
        String columnsValue = props.getProperty(columnsKey);

        if (columnsValue != null && !columnsValue.isEmpty()) {
            // 将逗号分隔的列名字符串转换为列表
            String[] columnArray = columnsValue.split(",");
            List<String> columns = new ArrayList<>();

            for (String column : columnArray) {
                columns.add(column.trim());
            }

            // 存入列名集合
            columnConfigs.put(tableType, columns);
        }
    }
}
```

### 9.3 标签映射解析逻辑

标签映射解析的具体逻辑如下：

```java
private void loadTagIdMappings(Properties props) {
    // 遍历所有表格类型
    for (String tableType : TABLE_TYPES) {
        List<String> columns = columnConfigs.get(tableType);

        if (columns != null) {
            // 遍历每个列名
            for (String column : columns) {
                String tagIdKey = tableType + "." + column + ".tagId";
                String tagId = props.getProperty(tagIdKey);

                if (tagId != null && !tagId.isEmpty()) {
                    // 存入标签映射集合
                    String mappingKey = tableType + "." + column;
                    tagIdMappings.put(mappingKey, tagId.trim());
                }
            }
        }
    }
}
```

### 9.4 列配置使用逻辑

在TableDataEngine中，列配置的使用逻辑如下：

1. **获取列名列表**：
   ```java
   String[] columns = getTableColumnNames(tableType);
   ```

2. **遍历列名处理数据**：
   ```java
   for (String column : columns) {
       row.add(converter.getTagValue(item, column, tableType));
   }
   ```

3. **获取列对应的标签ID**：
   ```java
   String tagId = getColumnTagId(tableType, columnName);
   ```

4. **使用标签ID获取数据**：
   ```java
   return !tagId.isEmpty() ? convertTagValue(tagId, image.getTagValue(tagId)) : "";
   ```

### 9.5 列配置灵活性分析

表格列配置设计的灵活性体现在以下几个方面：

1. **列名和顺序的灵活配置**：可以通过配置文件调整列名和显示顺序，而无需修改代码

2. **标签映射的灵活配置**：可以为任何列名定义对应的DICOM标签ID，支持自定义数据映射

3. **显示名的灵活配置**：可以为列定义友好的显示名，支持国际化和本地化

4. **表格类型的扩展性**：可以轻松添加新的表格类型，而无需修改核心代码

### 9.6 特殊列处理逻辑

对于特殊列，如PHASE列，其处理逻辑如下：

1. **配置定义**：在配置文件中定义列名和标签ID
   ```properties
   image.columns=...,ph
   image.ph.tagId=00541300
   image.ph.displayName=相位
   ```

2. **代码处理**：在DataConverter中添加特殊处理逻辑
   ```java
   case "ph":
       return image.getTagValue(PHASE);
   ```

3. **值格式化**：如需要特殊格式化，可以添加专用的TagValueConverter

## 10. 总结

表格列配置采用了配置驱动的设计方式，通过配置文件定义表格结构和数据映射关系，实现了表格展示的灵活配置。这种设计方式使得表格结构可以在不修改代码的情况下进行调整，提高了系统的可维护性和扩展性。

通过TableConfig类统一管理配置，并与TableDataEngine紧密集成，确保了配置的一致性和有效性。同时，通过标签映射机制，建立了列名与DICOM标签的映射关系，实现了数据的自动提取和转换。
