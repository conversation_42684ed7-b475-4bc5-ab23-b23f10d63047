package com.ge.med.ct.laf2.theming;

import java.awt.Color;
import java.awt.Dimension;
import java.awt.Font;

/**
 * 主题常量类
 * 提供对当前主题常量的静态访问
 */
public class ThemeConstants {
    private static final ThemeManager themeManager = ThemeManager.getInstance();

    public static Theme getCurrentTheme() {
        return themeManager.getCurrentTheme();
    }

    public static class App {
        public static String getName() {
            return getCurrentTheme().getAppName();
        }

        public static String getVersion() {
            return getCurrentTheme().getAppVersion();
        }
    }

    public static class Colors {
        // 基础颜色
        public static Color getPrimary() {
            return getCurrentTheme().getPrimaryColor();
        }

        public static Color getSecondary() {
            return getCurrentTheme().getSecondaryColor();
        }

        public static Color getBackground() {
            return getCurrentTheme().getBackgroundColor();
        }

        public static Color getHeaderBackground() {
            return getCurrentTheme().getHeaderBackgroundColor();
        }

        public static Color getPanelBackground() {
            return getCurrentTheme().getPanelBackgroundColor();
        }

        public static Color getText() {
            return getCurrentTheme().getTextColor();
        }

        public static Color getTextBright() {
            return getCurrentTheme().getTextBrightColor();
        }

        public static Color getTextLight() {
            return getCurrentTheme().getTextLightColor();
        }

        public static Color getBorder() {
            return getCurrentTheme().getBorderColor();
        }

        // 状态颜色
        public static Color getSuccess() {
            return getCurrentTheme().getSuccessColor();
        }

        public static Color getWarning() {
            return getCurrentTheme().getWarningColor();
        }

        public static Color getError() {
            return getCurrentTheme().getErrorColor();
        }

        // 表格颜色
        public static Color getTableHeader() {
            return getCurrentTheme().getTableHeaderColor();
        }

        public static Color getTableSelectedRow() {
            return getCurrentTheme().getTableSelectedRowColor();
        }

        public static Color getTableAlternateRow() {
            return getCurrentTheme().getTableAlternateRowColor();
        }

        // 界面元素颜色
        public static Color getToolbarBackground() {
            return getCurrentTheme().getToolbarBackgroundColor();
        }

        public static Color getMenubar() {
            return getCurrentTheme().getMenubarColor();
        }

        public static Color getButtonBackground() {
            return getCurrentTheme().getButtonBackgroundColor();
        }

        public static Color getButtonBorder() {
            return getCurrentTheme().getButtonBorderColor();
        }

        public static Color getFieldBackground() {
            return getCurrentTheme().getFieldBackgroundColor();
        }

        // 列表颜色
        public static Color getListHeader() {
            return getCurrentTheme().getListHeaderColor();
        }

        public static Color getListSelectedRow() {
            return getCurrentTheme().getListSelectedRowColor();
        }

        public static Color getListAlternateRow() {
            return getCurrentTheme().getListAlternateRowColor();
        }

        public static Color getListBackground() {
            return getCurrentTheme().getListBackgroundColor();
        }
    }

    public static class Fonts {
        public static Font getHeader() {
            return getCurrentTheme().getHeaderFont();
        }

        public static Font getTitle() {
            return getCurrentTheme().getTitleFont();
        }

        public static Font getNormal() {
            return getCurrentTheme().getNormalFont();
        }

        public static Font getSmall() {
            return getCurrentTheme().getSmallFont();
        }

        public static Font getDefault() {
            return getCurrentTheme().getDefaultFont();
        }

        public static Font getLarge() {
            return getCurrentTheme().getLargeFont();
        }

        public static Font getReport() {
            return getCurrentTheme().getReportFont();
        }

        public static Font getTableHeader() {
            return getCurrentTheme().getTableHeaderFont();
        }

        public static Font getTableContent() {
            return getCurrentTheme().getTableContentFont();
        }

        public static Font getImageIndex() {
            return getCurrentTheme().getImageIndexFont();
        }

        public static Font getListItem() {
            return getCurrentTheme().getListItemFont();
        }
    }

    public static class Sizes {
        public static int getMargin() {
            return getCurrentTheme().getDefaultMargin();
        }

        public static int getPadding() {
            return getCurrentTheme().getDefaultPadding();
        }

        public static int getBorderRadius() {
            return getCurrentTheme().getDefaultBorderRadius();
        }

        public static int getButtonHeight() {
            return getCurrentTheme().getDefaultButtonHeight();
        }

        public static int getComponentHeight() {
            return getCurrentTheme().getDefaultComponentHeight();
        }

        public static int getIconSize() {
            return getCurrentTheme().getDefaultIconSize();
        }

        public static Dimension getButtonSize() {
            return getCurrentTheme().getDefaultButtonSize();
        }

        public static Dimension getSmallButtonSize() {
            return getCurrentTheme().getSmallButtonSize();
        }

        public static Dimension getLargeButtonSize() {
            return getCurrentTheme().getLargeButtonSize();
        }

        public static int getIconSmall() {
            return getCurrentTheme().getIconSmall();
        }

        public static int getIconMedium() {
            return getCurrentTheme().getIconMedium();
        }

        public static int getIconLarge() {
            return getCurrentTheme().getIconLarge();
        }

        public static Dimension getWindowSize() {
            return getCurrentTheme().getDefaultWindowSize();
        }

        public static Dimension getDialogSize() {
            return getCurrentTheme().getDialogSize();
        }
    }

    public static class Animation {
        public static int getDuration() {
            return getCurrentTheme().getAnimationDuration();
        }
    }

    public static class ThemeOps {
        public static String getCurrentThemeName() {
            return getCurrentTheme().getName();
        }

        public static String getCurrentThemeDisplayName() {
            return getCurrentTheme().getDisplayName();
        }

        public static boolean switchTheme(String themeName) {
            return themeManager.switchTheme(themeName);
        }

        public static void addThemeChangeListener(ThemeManager.ThemeChangeListener listener) {
            themeManager.addThemeChangeListener(listener);
        }

        public static void removeThemeChangeListener(ThemeManager.ThemeChangeListener listener) {
            themeManager.removeThemeChangeListener(listener);
        }

        public static String[] getAllThemeNames() {
            return themeManager.getAllThemes().stream()
                    .map(Theme::getName)
                    .toArray(String[]::new);
        }

        public static String[] getAllThemeDisplayNames() {
            return themeManager.getAllThemes().stream()
                    .map(Theme::getDisplayName)
                    .toArray(String[]::new);
        }
    }

    private ThemeConstants() {
        // 防止实例化
    }
}
