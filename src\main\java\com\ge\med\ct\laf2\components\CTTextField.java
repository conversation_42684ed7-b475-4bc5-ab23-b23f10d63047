package com.ge.med.ct.laf2.components;

import com.ge.med.ct.laf2.theming.ThemeConstants;

import javax.swing.*;
import javax.swing.border.EmptyBorder;
import java.awt.*;
import java.awt.event.FocusAdapter;
import java.awt.event.FocusEvent;

/**
 * 自定义文本输入框组件
 */
public class CTTextField extends JTextField {
    private static final long serialVersionUID = 1L;

    private boolean isFocused = false;
    private String placeholder = null;
    private Color placeholderColor = ThemeConstants.Colors.getTextLight();
    private int borderRadius = ThemeConstants.Sizes.getBorderRadius();
    private Color borderColor = ThemeConstants.Colors.getBorder();
    private Color focusedBorderColor = ThemeConstants.Colors.getPrimary();

    public CTTextField() {
        this("");
    }

    public CTTextField(String text) {
        super(text);
        configureTextField();
    }

    public CTTextField(int columns) {
        super(columns);
        configureTextField();
    }

    public CTTextField(String text, int columns) {
        super(text, columns);
        configureTextField();
    }

    private void configureTextField() {
        setOpaque(false);
        setBorder(new EmptyBorder(3, 5, 3, 5));
        setFont(ThemeConstants.Fonts.getNormal());
        setForeground(ThemeConstants.Colors.getText());
        setBackground(ThemeConstants.Colors.getFieldBackground());
        setPreferredSize(new Dimension(150, ThemeConstants.Sizes.getButtonHeight()));

        addFocusListener(new FocusAdapter() {
            @Override
            public void focusGained(FocusEvent e) {
                isFocused = true;
                repaint();
            }

            @Override
            public void focusLost(FocusEvent e) {
                isFocused = false;
                repaint();
            }
        });
    }

    public void setPlaceholder(String placeholder) {
        this.placeholder = placeholder;
        repaint();
    }

    public String getPlaceholder() {
        return placeholder;
    }

    /**
     * 设置占位文本颜色
     */
    public void setPlaceholderColor(Color color) {
        this.placeholderColor = color;
        repaint();
    }

    public void setBorderColor(Color color) {
        this.borderColor = color;
        repaint();
    }

    public void setFocusedBorderColor(Color color) {
        this.focusedBorderColor = color;
        repaint();
    }

    public void setBorderRadius(int radius) {
        this.borderRadius = radius;
        repaint();
    }

    @Override
    public Insets getInsets() {
        return getBorder().getBorderInsets(this);
    }

    @Override
    protected void paintComponent(Graphics g) {
        Graphics2D g2 = (Graphics2D) g.create();
        g2.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);

        // 绘制背景
        g2.setColor(getBackground());
        g2.fillRoundRect(0, 0, getWidth(), getHeight(), borderRadius, borderRadius);

        // 绘制边框
        g2.setColor(isFocused ? focusedBorderColor : borderColor);
        g2.drawRoundRect(0, 0, getWidth() - 1, getHeight() - 1, borderRadius, borderRadius);

        // 调用父类方法绘制文本
        super.paintComponent(g2);

        // 绘制占位文本
        if (placeholder != null && getText().isEmpty() && !isFocused) {
            g2.setColor(placeholderColor);
            g2.setFont(getFont());
            Insets insets = getInsets();
            g2.drawString(placeholder, insets.left, getHeight() / 2 + g2.getFontMetrics().getAscent() / 2 - 2);
        }

        g2.dispose();
    }
}