package com.ge.med.ct.exception.message;

/**
 * 常见错误消息枚举
 * 提供系统中常见的错误消息定义
 */
public enum CommonMessages implements Message {
    // 通用错误
    UNKNOWN_ERROR("common.unknown", "未知错误: {0}"),
    UNEXPECTED_ERROR("common.unexpected", "发生未预期的错误: {0}"),
    PROCESSING_ERROR("common.processing", "处理错误: {0}"),
    SERVICE_UNAVAILABLE("common.service.unavailable", "服务不可用: {0}"),
    SYSTEM_ERROR("common.system", "系统错误: {0}"),

    // 参数验证错误
    INVALID_PARAMETER("common.param.invalid", "无效的参数 {0}: {1}"),
    MISSING_PARAMETER("common.param.missing", "缺少必要参数: {0}"),
    INVALID_FORMAT("common.format.invalid", "无效的格式: {0}"),

    // 文件操作错误
    FILE_NOT_FOUND("common.file.not.found", "文件未找到: {0}"),
    READ_FILE_ERROR("common.file.read.error", "读取文件失败: {0}"),
    WRITE_FILE_ERROR("common.file.write.error", "写入文件失败: {0}"),
    IO_ERROR("common.io.error", "IO错误: {0}"),

    // 分析处理错误
    ANALYSIS_FAILED("common.analysis.failed", "分析失败: {0}"),
    ANALYSIS_TIMEOUT("common.analysis.timeout", "分析超时: {0}"),
    UNSUPPORTED_ANALYSIS("common.analysis.unsupported", "不支持的分析类型: {0}"),

    // 配置错误
    CONFIG_ERROR("common.config.error", "配置错误: {0}"),
    MISSING_CONFIG("common.config.missing", "缺失配置项: {0}"),

    // 安全错误
    AUTHENTICATION_FAILED("common.auth.failed", "认证失败: {0}"),
    UNAUTHORIZED_ACCESS("common.auth.unauthorized", "未授权访问: {0}"),
    PERMISSION_DENIED("common.permission.denied", "权限不足: {0}"),

    // 业务操作错误
    RESOURCE_NOT_FOUND("common.resource.not.found", "未找到{0}资源: {1}"),
    OPERATION_FAILED("common.operation.failed", "操作 {0} 失败: {1}"),
    BUSINESS_RULE_VIOLATION("common.business.rule.violation", "违反业务规则: {0}"),
    DATA_ALREADY_EXISTS("common.data.exists", "数据已存在: {0}"),

    // 网络相关错误
    TIMEOUT("common.timeout", "操作超时: {0}"),
    CONNECTION_ERROR("common.connection.error", "连接错误: {0}"),
    RESPONSE_ERROR("common.response.error", "响应错误: {0}"),

    // 线程相关错误
    THREAD_INTERRUPTED("common.thread.interrupted", "线程被中断: {0}");

    private final AbstractMessage delegate;

    CommonMessages(String key, String defaultMessage) {
        this.delegate = new AbstractMessage(key, defaultMessage) {};
    }

    @Override
    public String getKey() {
        return delegate.getKey();
    }

    @Override
    public String getDefaultMessage() {
        return delegate.getDefaultMessage();
    }

    @Override
    public Message format(Object... args) {
        return delegate.format(args);
    }

    @Override
    public String toStr() {
        return delegate.toStr();
    }
}
