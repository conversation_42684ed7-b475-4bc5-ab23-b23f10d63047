package com.ge.med.ct.service.converter;

import com.ge.med.ct.cfg.table.TableColumnManager;
import com.ge.med.ct.dicom2.model.DicomSeries;
import com.ge.med.ct.exception.core.QAToolException;

import java.util.logging.Logger;

/**
 * 序列数据转换器
 * 负责将DicomSeries对象的数据转换为表格显示所需的字符串格式
 */
public class SeriesDataConverter implements DataConverter<DicomSeries> {
    private static final Logger LOG = Logger.getLogger(SeriesDataConverter.class.getName());
    private final TagValueFormatter tagValueFormatter;
    private final TableColumnManager tableColumnManager;

    public SeriesDataConverter(TagValueFormatter tagValueFormatter, TableColumnManager tableColumnManager) {
        this.tagValueFormatter = tagValueFormatter;
        this.tableColumnManager = tableColumnManager;
    }

    @Override
    public String getTagValue(DicomSeries series, String columnName, String tableType) throws QAToolException {
        if (series == null) return "";

        // 获取标签ID
        String tagId = tableColumnManager.resolveTagId(tableType, columnName);

        // 处理特殊列
        if ("special".equals(tagId)) {
            // 根据列名判断具体的特殊列类型
            if ("ImageCount".equals(columnName)) {
                return String.valueOf(series.getImageCount());
            }
            return "";
        }

        if (tagId.isEmpty()) {
            LOG.warning("列 " + columnName + " 未找到对应的标签ID");
            return "";
        }

        // 获取标签值
        String tagValue = series.getTagValue(tagId);

        // 格式化值
        return tagValueFormatter.format(tagId, tagValue);
    }
}