# LAF 组件优化

## 1. 组件工厂模式

### 1.1 ComponentFactory类
创建ComponentFactory类，提取公共UI组件创建逻辑，减少代码重复。

```java
public class ComponentFactory {
    // 常量定义
    private static final Dimension BUTTON_SIZE = new Dimension(80, 30);
    private static final Dimension LABEL_SIZE = new Dimension(80, 25);
    private static final Dimension FIELD_SIZE = new Dimension(150, 25);
    
    private ComponentFactory() {
        // 私有构造函数，防止实例化
    }
    
    // 创建按钮
    public static CTButton createButton(String text, ActionListener listener) {
        CTButton button = new CTButton(text);
        button.setPreferredSize(BUTTON_SIZE);
        if (listener != null) {
            button.addActionListener(listener);
        }
        return button;
    }
    
    // 创建标签
    public static JLabel createLabel(String text) {
        JLabel label = new JLabel(text);
        label.setPreferredSize(LABEL_SIZE);
        return label;
    }
    
    // 创建文本输入框
    public static JTextField createTextField() {
        JTextField field = new JTextField();
        field.setPreferredSize(FIELD_SIZE);
        return field;
    }
    
    // 创建表格
    public static CTTable createTable(Vector<String> columnNames) {
        DefaultTableModel model = new DefaultTableModel(null, columnNames);
        return new CTTable(model);
    }
}
```

### 1.2 使用示例

```java
// 创建按钮
CTButton queryButton = ComponentFactory.createButton("查询", e -> performSearch());
CTButton clearButton = ComponentFactory.createButton("清理", e -> clearSearchFields());

// 创建标签
JLabel patientNameLabel = ComponentFactory.createLabel("患者姓名:");

// 创建文本输入框
JTextField patientNameField = ComponentFactory.createTextField();
```

## 2. 布局工具类

### 2.1 LayoutWorker类
创建LayoutWorker类，使用构建器模式简化GridBagConstraints的设置。

```java
public class LayoutWorker {
    // 默认内边距
    public static final Insets DEFAULT_INSETS = new Insets(5, 5, 5, 5);
    
    private final Container container;
    private final GridBagConstraints constraints;
    
    public LayoutWorker(Container container) {
        this.container = container;
        this.constraints = new GridBagConstraints();
        this.constraints.insets = DEFAULT_INSETS;
    }
    
    // 设置网格位置
    public LayoutWorker position(int gridx, int gridy) {
        constraints.gridx = gridx;
        constraints.gridy = gridy;
        return this;
    }
    
    // 设置网格大小
    public LayoutWorker size(int gridwidth, int gridheight) {
        constraints.gridwidth = gridwidth;
        constraints.gridheight = gridheight;
        return this;
    }
    
    // 设置权重
    public LayoutWorker weight(double weightx, double weighty) {
        constraints.weightx = weightx;
        constraints.weighty = weighty;
        return this;
    }
    
    // 设置填充方式
    public LayoutWorker fill(int fill) {
        constraints.fill = fill;
        return this;
    }
    
    // 添加组件
    public void add(Component component) {
        container.add(component, constraints);
    }
}
```

### 2.2 使用示例

```java
// 使用LayoutWorker添加组件
LayoutWorker helper = new LayoutWorker(searchPanel);
helper.position(0, 0).add(patientNameLabel);
helper.position(1, 0).weight(1.0, 0).size(2, 1).fill(GridBagConstraints.HORIZONTAL).add(patientNameField);
helper.position(3, 0).add(queryButton);
helper.position(4, 0).add(clearButton);
```

## 3. 优化效果

### 3.1 代码量减少
使用ComponentFactory和LayoutWorker后，创建和布局UI组件的代码量显著减少。

### 3.2 样式一致性
使用ComponentFactory确保所有UI组件具有一致的样式，避免不同地方创建的组件样式不一致。

### 3.3 维护性提高
修改UI样式只需要修改ComponentFactory中的常量定义，而不需要修改多处代码。

## 4. 实施建议

### 4.1 渐进式迁移
1. 创建ComponentFactory和LayoutWorker类
2. 在新代码中使用这些工具类
3. 逐步重构现有代码，使用这些工具类替换原有的UI组件创建和布局代码

### 4.2 保持向后兼容
1. 保留原有的方法，但将其实现委托给新的工具类
2. 逐步迁移代码，避免一次性大规模修改
