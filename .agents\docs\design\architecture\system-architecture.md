# CT 质量保证工具系统架构设计

## 1. 引言

### 1.1 目的

本文档描述了 CT 质量保证工具的系统架构设计，包括系统的整体架构、组件关系、数据流和技术选择。该文档旨在为开发团队提供系统架构的详细视图，确保系统设计的一致性和完整性。

### 1.2 范围

本架构设计文档涵盖 CT 质量保证工具的以下方面：

- 系统整体架构
- 组件分解和关系
- 数据流设计
- 技术架构
- 部署架构

### 1.3 定义和缩略语

- **DICOM**: Digital Imaging and Communications in Medicine，医学数字成像和通信标准
- **CT**: Computed Tomography，计算机断层扫描
- **QA**: Quality Assurance，质量保证
- **UI**: User Interface，用户界面
- **MVP**: Model-View-Presenter，模型-视图-表示者架构模式
- **AOP**: Aspect-Oriented Programming，面向切面编程

## 2. 架构概述

### 2.1 架构目标

CT 质量保证工具的架构设计目标包括：

1. **模块化**: 系统分为多个独立模块，降低耦合度，提高可维护性
2. **可扩展性**: 支持新功能和分析工具的扩展
3. **可配置性**: 支持通过配置文件调整系统行为，无需修改代码
4. **高性能**: 高效处理大量 DICOM 数据
5. **可靠性**: 健壮的异常处理机制，确保系统稳定运行

### 2.2 架构原则

系统架构遵循以下原则：

1. **关注点分离**: 将系统分为不同的关注点，如 UI、业务逻辑、数据访问等
2. **单一职责**: 每个组件只负责一个功能
3. **开闭原则**: 对扩展开放，对修改关闭
4. **依赖倒置**: 高层模块不应依赖低层模块，两者都应依赖抽象
5. **接口隔离**: 客户端不应依赖它不使用的接口

### 2.3 架构概览

CT 质量保证工具采用分层架构，主要包括以下层次：

1. **表示层**: 用户界面组件，采用 MVP 架构模式
2. **业务逻辑层**: 核心业务逻辑和数据处理
3. **数据访问层**: DICOM 数据访问和文件系统交互
4. **基础设施层**: 日志、异常处理、配置管理等基础服务

## 3. 系统分解

### 3.1 系统组件

系统主要包括以下组件：

1. **UI 组件**: 用户界面组件，包括主窗口、图像显示、数据浏览等
2. **DICOM 数据服务**: 负责 DICOM 文件的加载、解析和管理
3. **表格数据模块**: 负责将 DICOM 数据转换为表格可显示的格式
4. **分析服务**: 提供各种图像分析和质量评估功能
5. **报告服务**: 负责生成质量评估报告
6. **配置管理**: 负责系统参数和分析标准的配置
7. **异常处理**: 提供统一的异常处理机制

### 3.2 组件关系图

```
+------------------+       +------------------+
|    UI组件        |------>|   DICOM数据服务  |
+------------------+       +------------------+
        |                          |
        |                          |
        v                          v
+------------------+       +------------------+
|   分析服务       |<----->|   表格数据模块   |
+------------------+       +------------------+
        |                          |
        |                          |
        v                          v
+------------------+       +------------------+
|   报告服务       |       |   配置管理       |
+------------------+       +------------------+
        |                          |
        |                          |
        v                          v
+------------------------------------------+
|              异常处理                     |
+------------------------------------------+
```

### 3.3 组件职责

#### 3.3.1 UI 组件

**技术选择**: 基于 Swing 框架 + 自定义 Look and Feel

- 提供用户界面，包括主窗口、菜单、工具栏等
- 显示 DICOM 图像和数据
- 提供用户交互功能，如图像浏览、分析工具选择等
- 显示分析结果和报告
- 采用 MVP（Model-View-Presenter）架构模式
- 通过自定义 Look and Feel 实现现代化界面风格
- 支持主题切换和界面定制

#### 3.3.2 DICOM 数据服务

**包路径**: `com.ge.med.ct.dicom2`（版本 2 实现）

- 加载和解析 DICOM 文件
- 管理 DICOM 数据模型（DicomExam、DicomSeries、DicomImage）
- 提供 DICOM 数据查询功能
- 支持 DICOM 数据缓存
- 基于 DCM4CHE 库实现 DICOM 标准支持
- 提供标签解析和验证服务
- 支持 GE CT 专用标签处理

#### 3.3.3 表格数据模块

- 将 DICOM 数据转换为表格可显示的格式
- 管理表格列配置
- 提供数据格式化功能
- 支持特殊列处理，如相位列

#### 3.3.4 分析服务

- 提供各种图像分析工具
- 执行图像质量评估
- 管理分析结果
- 支持自定义分析算法

#### 3.3.5 报告服务

- 生成质量评估报告
- 管理报告模板
- 支持报告导出
- 提供报告预览功能

#### 3.3.6 配置管理

- 加载和管理配置文件
- 提供配置访问接口
- 支持配置更新
- 管理系统参数和分析标准

#### 3.3.7 异常处理

- 提供统一的异常处理机制
- 管理异常日志
- 显示用户友好的错误消息
- 支持异常重试策略
- 提供统一的消息处理系统

## 4. 数据流

### 4.1 主要数据流

系统的主要数据流包括：

1. **DICOM 数据加载流**
2. **图像显示流**
3. **数据分析流**
4. **报告生成流**

### 4.2 数据流图

#### 4.2.1 DICOM 数据加载流

```
+----------+     +------------+     +-------------+     +------------+
| 用户界面 | --> | DICOM加载器 | --> | DICOM解析器 | --> | DICOM数据库 |
+----------+     +------------+     +-------------+     +------------+
                                                              |
                                                              v
                                                        +------------+
                                                        | 数据浏览器 |
                                                        +------------+
```

#### 4.2.2 图像显示流

```
+----------+     +------------+     +-------------+     +------------+
| 数据浏览器| --> | DICOM数据库 | --> | 图像加载器  | --> | 图像显示器 |
+----------+     +------------+     +-------------+     +------------+
                                                              |
                                                              v
                                                        +------------+
                                                        | 图像处理器 |
                                                        +------------+
```

#### 4.2.3 数据分析流

```
+----------+     +------------+     +-------------+     +------------+
| 图像显示器| --> | 分析工具   | --> | 分析执行器  | --> | 分析结果  |
+----------+     +------------+     +-------------+     +------------+
                                                              |
                                                              v
                                                        +------------+
                                                        | 结果显示器 |
                                                        +------------+
```

#### 4.2.4 报告生成流

```
+----------+     +------------+     +-------------+     +------------+
| 分析结果 | --> | 报告生成器 | --> | 报告模板   | --> | 报告文档   |
+----------+     +------------+     +-------------+     +------------+
                                                              |
                                                              v
                                                        +------------+
                                                        | 报告导出器 |
                                                        +------------+
```

## 5. 技术架构

### 5.1 技术选择

系统采用以下技术：

- **编程语言**: Java 1.8
- **UI 框架**: Swing
- **DICOM 库**: dcm4che
- **图像处理**: ImageJ
- **日志框架**: java.util.logging
- **构建工具**: Maven
- **单元测试**: JUnit

### 5.2 技术架构图

```
+----------------------------------+
|            应用层                |
|  +----------------------------+  |
|  |        Swing UI           |  |
|  +----------------------------+  |
+----------------------------------+
                 |
+----------------------------------+
|           框架层                 |
|  +----------------------------+  |
|  |        MVP框架             |  |
|  +----------------------------+  |
|  |        AOP框架             |  |
|  +----------------------------+  |
+----------------------------------+
                 |
+----------------------------------+
|           库层                   |
|  +----------------------------+  |
|  |        dcm4che             |  |
|  +----------------------------+  |
|  |        ImageJ              |  |
|  +----------------------------+  |
+----------------------------------+
                 |
+----------------------------------+
|           平台层                 |
|  +----------------------------+  |
|  |        Java 1.8            |  |
|  +----------------------------+  |
|  |        JRE                 |  |
|  +----------------------------+  |
+----------------------------------+
```

### 5.3 关键技术实现

#### 5.3.1 MVP 架构实现

系统采用 MVP 架构模式实现 UI，主要包括：

- **Model**: 数据模型，如 DicomExam、DicomSeries 等
- **View**: 视图接口和实现，如 MainFrame、ImagePanel 等
- **Presenter**: 表示者，负责连接 Model 和 View，处理用户操作

```java
// View接口
public interface ImageView {
    void displayImage(BufferedImage image);
    void setWindowLevel(int window, int level);
    void showError(String message);
}

// Presenter
public class ImagePresenter {
    private ImageView view;
    private DicomService dicomService;

    public ImagePresenter(ImageView view, DicomService dicomService) {
        this.view = view;
        this.dicomService = dicomService;
    }

    public void loadImage(String imageId) {
        try {
            DicomImage image = dicomService.getImage(imageId);
            BufferedImage bufferedImage = image.getBufferedImage();
            view.displayImage(bufferedImage);
            view.setWindowLevel(image.getDefaultWindow(), image.getDefaultLevel());
        } catch (Exception e) {
            view.showError("Failed to load image: " + e.getMessage());
        }
    }
}
```

#### 5.3.2 AOP 异常处理实现

系统采用 AOP 方式实现异常处理，主要包括：

- **HandleException 注解**: 标记需要异常处理的方法
- **ExceptionAspect**: 异常处理切面，捕获和处理异常
- **ExceptionHandler**: 异常处理器，处理特定类型的异常
- **RetryStrategy**: 重试策略，处理可能暂时失败的操作

```java
// HandleException注解
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.METHOD, ElementType.TYPE})
public @interface HandleException {
    ErrorCode errorCode() default ErrorCode.UNEXPECTED;
    boolean logException() default true;
    boolean publishEvent() default true;
    int maxRetries() default 0;
    long retryDelay() default 0;
    Class<? extends Throwable>[] retryFor() default {};
    Class<? extends Throwable>[] noRetryFor() default {};
}

// ExceptionAspect
@Aspect
public class ExceptionAspect {
    @Around("@annotation(handleException)")
    public Object handleException(ProceedingJoinPoint joinPoint, HandleException handleException) throws Throwable {
        // 获取重试配置
        int maxRetries = handleException.maxRetries();
        long retryDelay = handleException.retryDelay();

        // 如果不需要重试，直接执行
        if (maxRetries <= 0) {
            return executeWithExceptionHandling(joinPoint, handleException);
        }

        // 需要重试的情况
        return executeWithRetry(joinPoint, handleException, maxRetries, retryDelay);
    }

    private Object executeWithExceptionHandling(ProceedingJoinPoint joinPoint, HandleException handleException) throws Throwable {
        try {
            return joinPoint.proceed();
        } catch (Exception e) {
            ErrorCode errorCode = handleException.errorCode();
            ExceptionHandler handler = ExceptionHandlerFactory.getHandler(errorCode);
            return handler.handleException(e, errorCode);
        }
    }
}

// RetryStrategy
@HandleException(errorCode = ErrorCode.OPERATION)
public final class RetryStrategy {
    /**
     * 使用默认参数执行重试操作
     */
    public static <T> T retry(Callable<T> operation, String operationName) throws Exception {
        return retry(operation, operationName, 3, 1000, e -> true);
    }

    /**
     * 执行重试操作
     */
    public static <T> T retry(
            Callable<T> operation,
            String operationName,
            int maxAttempts,
            long delayMs,
            Predicate<Exception> retryOn) throws Exception {
        // 重试实现...
    }

    /**
     * 执行不返回值的重试操作
     */
    public static void retryVoid(RunnableWithException operation, String operationName) throws Exception {
        retry(() -> {
            operation.run();
            return null;
        }, operationName);
    }

    /**
     * 可能抛出异常的Runnable接口
     */
    @FunctionalInterface
    public interface RunnableWithException {
        void run() throws Exception;
    }
}
```

## 6. 部署架构

### 6.1 部署模型

系统采用单机部署模式，所有组件部署在同一台机器上。

```
+----------------------------------+
|            用户工作站             |
|  +----------------------------+  |
|  |     CT质量保证工具          |  |
|  +----------------------------+  |
|  |        JRE                 |  |
|  +----------------------------+  |
|  |     操作系统(Windows/Linux) |  |
|  +----------------------------+  |
+----------------------------------+
```

### 6.2 部署要求

- **硬件要求**:

  - CPU: 双核或更高
  - 内存: 最小 4GB，推荐 8GB
  - 磁盘: 最小 1GB，取决于 DICOM 数据量
  - 显示器: 分辨率 1920x1080 或更高

- **软件要求**:
  - 操作系统: Windows 7/10/11, Linux
  - JRE: Java 1.8 或更高版本

### 6.3 部署步骤

1. 安装 JRE 1.8 或更高版本
2. 解压 CT 质量保证工具安装包
3. 配置 config 目录下的配置文件
4. 运行 bin 目录下的启动脚本
5. 系统启动并显示主界面

## 7. 质量属性

### 7.1 性能

- **响应时间**: 用户操作响应时间不超过 1 秒
- **加载时间**: DICOM 文件加载时间不超过 5 秒/100MB
- **内存使用**: 内存使用不超过 2GB（标准数据集）

### 7.2 可靠性

- **平均无故障时间**: 不少于 100 小时
- **错误处理**: 所有异常都有适当的处理和恢复机制
- **数据完整性**: 确保 DICOM 数据的完整性和一致性

### 7.3 可用性

- **系统可用性**: 99.9%
- **用户界面**: 符合人机交互原则，易于使用
- **错误提示**: 用户友好的错误提示和帮助信息

### 7.4 安全性

- **数据安全**: 确保患者数据的安全性和隐私性
- **访问控制**: 基于角色的访问控制
- **审计日志**: 记录关键操作的审计日志

### 7.5 可维护性

- **模块化**: 系统分为多个独立模块，便于维护
- **文档化**: 完整的设计文档和代码注释
- **测试覆盖**: 单元测试覆盖率不低于 80%

### 7.6 可扩展性

- **插件机制**: 支持通过插件扩展系统功能
- **配置驱动**: 支持通过配置文件调整系统行为
- **开放接口**: 提供开放的 API，便于集成和扩展

## 8. 架构决策

### 8.1 使用 Java 和 Swing

**决策**: 使用 Java 作为开发语言，Swing 作为 UI 框架。

**理由**:

- Java 提供跨平台能力，支持 Windows 和 Linux
- Swing 是 Java 标准 UI 库，稳定可靠
- 开发团队熟悉 Java 和 Swing
- 符合医疗软件的稳定性和可靠性要求

### 8.2 采用 MVP 架构模式

**决策**: 采用 MVP 架构模式实现 UI。

**理由**:

- 分离视图和业务逻辑，提高可测试性
- 降低 UI 和业务逻辑的耦合度
- 便于团队协作开发
- 支持 UI 的灵活变更

### 8.3 使用 AOP 进行异常处理和重试策略

**决策**: 采用 AOP 方式实现异常处理，并使用 `RetryStrategy` 类提供重试功能。

**理由**:

- 集中管理异常处理逻辑，避免代码重复
- 分离业务逻辑和异常处理，提高代码可读性
- 统一异常处理策略，确保一致性
- 便于添加新的异常处理逻辑
- 通过 `RetryStrategy` 提供统一的重试机制，处理可能暂时失败的操作
- 支持有返回值和无返回值的重试操作，增强灵活性

### 8.4 使用配置文件驱动

**决策**: 采用配置文件驱动系统行为。

**理由**:

- 支持不同环境的配置调整，无需修改代码
- 便于系统参数的调整和优化
- 支持用户自定义配置
- 降低代码和配置的耦合度

## 9. 参考文档

- [设计概要](../design-overview.md)
- [详细设计](../detailed-design.md)
- [表格数据模块设计](../module-design/table-data-module.md)
- [表格数据引擎设计](../module-design/table-data-engine.md)
- [表格配置设计](../module-design/table-config.md)
- [相位列实现设计](../module-design/phase-column.md)
