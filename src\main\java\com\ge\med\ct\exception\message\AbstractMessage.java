package com.ge.med.ct.exception.message;

/**
 * 消息抽象基类
 * 简化消息枚举的实现
 */
public abstract class AbstractMessage implements Message {
    private final String key;
    private final String defaultMessage;
    private String formattedMessage;

    protected AbstractMessage(String key, String defaultMessage) {
        this.key = key;
        this.defaultMessage = defaultMessage;
        this.formattedMessage = defaultMessage;
    }

    @Override
    public String getKey() {
        return key;
    }

    @Override
    public String getDefaultMessage() {
        return defaultMessage;
    }

    @Override
    public Message format(Object... args) {
        Message formattedMessage = MessageBuilder.of(this, args).build();
        return formattedMessage;
    }

    @Override
    public String toStr() {
        return formattedMessage != null ? formattedMessage : defaultMessage;
    }

    /**
     * 设置格式化后的消息
     * 由MessageBuilder调用
     *
     * @param formattedMessage 格式化后的消息
     */
    void setFormattedMessage(String formattedMessage) {
        this.formattedMessage = formattedMessage;
    }
}
