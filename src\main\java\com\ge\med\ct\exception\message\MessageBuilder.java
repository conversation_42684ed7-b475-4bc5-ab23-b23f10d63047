package com.ge.med.ct.exception.message;

import java.text.MessageFormat;
import java.util.Locale;
import java.util.MissingResourceException;
import java.util.ResourceBundle;

/**
 * 消息构建器
 * 用于构建和格式化消息
 */
public class MessageBuilder {
    private static final String DEFAULT_BUNDLE_NAME = "messages";
    private static ResourceBundle bundle;

    static {
        try {
            bundle = ResourceBundle.getBundle(DEFAULT_BUNDLE_NAME, Locale.getDefault());
        } catch (MissingResourceException e) {
            // 如果找不到资源包，使用空的资源包
            bundle = null;
        }
    }

    private final Message message;
    private final Object[] args;

    /**
     * 创建消息构建器
     *
     * @param message 消息
     * @param args 消息参数
     */
    private MessageBuilder(Message message, Object... args) {
        this.message = message;
        this.args = args;
    }

    /**
     * 创建消息构建器
     *
     * @param message 消息
     * @param args 消息参数
     * @return 消息构建器
     */
    public static MessageBuilder of(Message message, Object... args) {
        return new MessageBuilder(message, args);
    }

    /**
     * 构建消息
     *
     * @return 构建的消息
     */
    public Message build() {
        if (message == null) {
            return new AbstractMessage("unknown", "Unknown message") {};
        }

        String template = getMessageTemplate(message.getKey(), message.getDefaultMessage());
        String formattedText = formatMessageText(template, args);

        // 如果消息是AbstractMessage的实例，设置格式化后的消息
        if (message instanceof AbstractMessage) {
            ((AbstractMessage) message).setFormattedMessage(formattedText);
        }

        return message;
    }

    /**
     * 获取消息模板
     *
     * @param key 消息键
     * @param defaultMessage 默认消息
     * @return 消息模板
     */
    private String getMessageTemplate(String key, String defaultMessage) {
        if (bundle == null) {
            return defaultMessage;
        }

        try {
            return bundle.getString(key);
        } catch (MissingResourceException e) {
            return defaultMessage;
        }
    }

    /**
     * 格式化消息文本
     *
     * @param template 消息模板
     * @param args 消息参数
     * @return 格式化后的消息文本
     */
    private String formatMessageText(String template, Object... args) {
        if (args == null || args.length == 0) {
            return template;
        }

        try {
            return MessageFormat.format(template, args);
        } catch (IllegalArgumentException e) {
            // 如果格式化失败，尝试简单替换占位符
            StringBuilder result = new StringBuilder(template);
            for (int i = 0; i < args.length; i++) {
                String placeholder = "{" + i + "}";
                int index = result.indexOf(placeholder);
                if (index != -1) {
                    result.replace(index, index + placeholder.length(), String.valueOf(args[i]));
                }
            }
            return result.toString();
        }
    }
}
