package com.ge.med.ct.laf2.base.views;

import com.ge.med.ct.dicom2.core.DicomDataProvider;
import com.ge.med.ct.laf2.base.MessageType;
import com.ge.med.ct.laf2.base.listeners.IViewListener;
import com.ge.med.ct.laf2.components.CTJSplitPane;

import java.awt.*;
import java.util.logging.Logger;

/**
 * 基础视图抽象类实现
 *
 * @param <T> 视图监听器类型
 */
public abstract class BaseView<T extends IViewListener> extends CTJSplitPane implements IBaseView<T> {
    /**
     * 日志记录器
     */
    protected final Logger logger = Logger.getLogger(getClass().getName());
    protected static final double LEFT_RIGHT_RATIO = 1.0 / 3.0;
    /**
     * 视图监听器
     */
    protected T viewListener;

    /**
     * DICOM数据提供者
     */
    protected DicomDataProvider dataProvider;

    /**
     * 构造函数
     */
    public BaseView() {
        setLayout(new BorderLayout());
    }
    
    public BaseView(int orientation) {
        super(orientation);
    }

    @Override
    public void showMessage(MessageType type, String message) {
        // 记录日志
        switch (type) {
            case ERROR:
                logger.severe(message);
                break;
            case WARNING:
                logger.warning(message);
                break;
            case SUCCESS:
            case INFO:
            default:
                logger.info(message);
                break;
        }

        // 通知监听器
        if (viewListener != null) {
            viewListener.onMessage(type, message);
        }
    }

    @Override
    public void clearView() {
        // 子类实现具体清理逻辑
    }

    @Override
    public void setViewListener(T listener) {
        this.viewListener = listener;
    }

    @Override
    public T getViewListener() {
        return viewListener;
    }

    @Override
    public void setDataProvider(DicomDataProvider provider) {
        this.dataProvider = provider;
    }

    @Override
    public DicomDataProvider getDataProvider() {
        return dataProvider;
    }

    public void destroy() {
        if (viewListener != null) {
            viewListener.onViewDestroyed();
        }
    }
}
