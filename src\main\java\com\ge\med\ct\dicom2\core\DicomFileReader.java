package com.ge.med.ct.dicom2.core;

import java.util.Map;
import java.util.logging.Logger;

import com.ge.med.ct.dicom2.model.DicomFileModel;
import com.ge.med.ct.dicom2.reader.DicomMetadataReader;
import com.ge.med.ct.exception.aspect.HandleException;
import com.ge.med.ct.exception.code.ErrorCode;
import com.ge.med.ct.exception.core.DicomException;
import com.ge.med.ct.exception.message.DicomMessages;
import com.ge.med.ct.service.LogManager;

/**
 * DICOM文件读取器 - 核心层组件
 * 负责DICOM文件的基础读取操作
 */
@HandleException(errorCode = ErrorCode.READ)
public class DicomFileReader {
    private static final Logger LOG = LogManager.getInstance().getLogger(DicomFileReader.class);
    private final DicomMetadataReader metadataReader;

    public DicomFileReader() {
        this.metadataReader = new DicomMetadataReader();
    }

    /**
     * 读取DICOM文件完整元数据
     * 
     * @param filePath DICOM文件路径
     * @return DICOM文件模型
     * @throws DicomException 如果读取失败
     */
    public DicomFileModel readFile(String filePath) throws DicomException {
        validateFilePath(filePath);

        try {
            LOG.fine("读取DICOM文件: " + filePath);
            return metadataReader.readMetadata(filePath);
        } catch (Exception e) {
            LOG.warning("读取DICOM文件失败: " + filePath + ", 原因: " + e.getMessage());
            throw new DicomException(ErrorCode.READ, DicomMessages.FILE_READ_ERROR, e, filePath);
        }
    }

    /**
     * 读取DICOM文件关键标签
     * 
     * @param filePath DICOM文件路径
     * @return 关键标签映射
     * @throws DicomException 如果读取失败
     */
    public Map<String, String> readKeyTags(String filePath) throws DicomException {
        validateFilePath(filePath);

        try {
            LOG.fine("读取DICOM关键标签: " + filePath);
            return metadataReader.readKeyTags(filePath);
        } catch (Exception e) {
            LOG.warning("读取DICOM关键标签失败: " + filePath + ", 原因: " + e.getMessage());
            throw new DicomException(ErrorCode.READ, DicomMessages.FILE_READ_ERROR, e, filePath);
        }
    }

    /**
     * 验证文件路径
     * 
     * @param filePath 文件路径
     * @throws DicomException 如果路径无效
     */
    private void validateFilePath(String filePath) throws DicomException {
        if (filePath == null || filePath.trim().isEmpty()) {
            throw new DicomException(ErrorCode.VALIDATION, DicomMessages.VALIDATION_ERROR, "文件路径不能为空");
        }
    }
}