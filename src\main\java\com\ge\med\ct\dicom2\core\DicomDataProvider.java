package com.ge.med.ct.dicom2.core;

import com.ge.med.ct.analysis.service.AnalysisStatusCallback;
import com.ge.med.ct.dicom2.model.DicomExam;
import com.ge.med.ct.dicom2.model.DicomFileModel;
import com.ge.med.ct.dicom2.model.DicomImage;
import com.ge.med.ct.dicom2.model.DicomSeries;
import com.ge.med.ct.exception.core.DicomException;
import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * DICOM数据提供者接口
 */
public interface DicomDataProvider {
    List<DicomExam> getAllExams();

    CompletableFuture<List<DicomExam>> getAllExamsAsync();

    DicomExam getExam(String examId);

    CompletableFuture<DicomExam> getExamAsync(String examId);

    List<DicomExam> searchExams(String patientName, String patientId);

    CompletableFuture<List<DicomExam>> searchExamsAsync(String patientName, String patientId);

    DicomSeries getSeries(String seriesId);

    List<DicomSeries> getSeriesForExam(String examId);

    DicomImage getImage(String imageId);

    List<DicomImage> getImagesForSeries(String seriesId);

    void addFileModel(DicomFileModel model) throws DicomException;

    CompletableFuture<Void> addFileModelAsync(DicomFileModel model);

    String getTagValue(String fileId, String tagId);

    List<DicomFileModel> getAllFileModels();

    CompletableFuture<List<DicomFileModel>> getAllFileModelsAsync();

    void setStatusCallback(AnalysisStatusCallback callback);

    void clearData() throws DicomException;

    void shutdown();
}