# System error messages
error.system.unexpected=系统发生未预期的错误
error.system.config=系统配置错误: {0}
error.system.io=IO操作错误: {0}
error.system.database=数据库操作错误: {0}

# Retry related messages
system.retry.attempt=重试操作 (第{0}次，共{1}次)
system.retry.failed=重试失败 (第{0}次): {1}
system.retry.exhausted=重试次数已用尽 ({0}次): {1}
system.retry.interrupted=重试操作被中断

# Business error messages
error.business.validation=数据验证失败: {0}
error.business.processing=业务处理失败: {0}
error.business.not.found=未找到请求的数据: {0}
error.business.not.allowed=不允许的操作: {0}

# UI error messages
error.ui.operation=UI操作错误: {0}
error.ui.display=UI显示错误: {0}

# DICOM validation messages
dicom.error.missing.required.tag=缺少必需的标签 {0} (0x{1})
dicom.error.invalid.tag=无效的标签 {0}: {1}
dicom.error.empty.pixel.data=像素数据为空
dicom.error.invalid.dimension=无效的图像维度
dicom.error.invalid.bits.allocated=无效的位分配
dicom.error.invalid.samples=无效的采样数
dicom.error.size.limit.exceeded=超出大小限制
dicom.error.size.mismatch=大小不匹配
dicom.error.invalid.image.params=无效的图像参数: 行={0}, 列={1}, 分配位={2}, 存储位={3}, 高位={4}
dicom.error.empty.patient.id=患者ID为空
dicom.error.empty.patient.name=患者姓名为空
dicom.error.empty.series.id=序列ID为空
dicom.error.empty.exam.id=检查ID为空
dicom.error.empty.file.id=DICOM文件ID为空
dicom.quick.validation.failed=快速验证失败

# DICOM file operation messages
dicom.file.not.found=DICOM文件不存在: {0}
dicom.file.read.failed=读取DICOM文件失败: {0}
dicom.error.invalid.path=无效的文件路径
dicom.provider.directory.invalid=无效的目录: {0}
dicom.provider.scan.start=开始扫描目录: {0} {1}
dicom.provider.scan.complete=扫描完成，找到 {0} 个DICOM文件
dicom.provider.scan.failed=扫描目录失败: {0}
dicom.provider.scan.directory=扫描目录: {0}
dicom.provider.scan.no.files=扫描目录未找到DICOM文件: {0}
dicom.provider.scan.found.files=找到 {0} 个DICOM文件
dicom.provider.processing.file=处理DICOM文件: {0} ({1}/{2})
dicom.provider.processing.file.error=处理文件失败: {0}
dicom.provider.processing.start=开始处理新文件
dicom.provider.processing.summary=DICOM处理完成: 成功={0}, 失败={1}

# DICOM basic error messages
dicom.error.invalid.data=无效的DICOM数据: {0}
dicom.error.file.not.found=DICOM文件未找到: {0}
dicom.error.processing=DICOM处理错误: {0}
dicom.error.image.processing=DICOM图像处理错误: {0}
dicom.error.series.processing=DICOM序列处理错误: {0}
dicom.error.exam.processing=DICOM检查处理错误: {0}
dicom.error.tag=DICOM标签错误: {0}
dicom.error.validation=DICOM验证错误: {0}

# DICOM async operation messages
dicom.scan.async.failed=异步扫描失败: {0}
dicom.read.async.failed=异步读取失败: {0}
dicom.count.failed=计数操作失败
dicom.check.failed=检查操作失败
dicom.monitor.close.failed=关闭监控失败: {0}

# DICOM provider basic messages
dicom.provider.model.null=DICOM文件模型不能为空
dicom.provider.model.id.invalid=DICOM文件模型必须有有效ID
dicom.provider.file.not.found=DICOM文件路径无效或文件不存在: {0}
dicom.provider.model.exists=具有ID为 {0} 的DICOM文件模型已存在并将被更新
dicom.provider.no.images=序列 {0} 没有可用的图像
dicom.provider.no.series=检查 {0} 没有可用的序列
dicom.provider.data.cleared=所有数据已清除
dicom.provider.shutdown=DicomDataProvider已关闭
dicom.provider.create.exam.failed=创建检查对象失败: {0}
dicom.provider.create.series.failed=创建序列对象失败: {0}
dicom.provider.update.exam.failed=更新检查属性失败: {0}
dicom.provider.update.image.failed=更新图像属性失败: {0}
dicom.provider.update.series.failed=更新序列属性失败: {0}
dicom.provider.clear.failed=清除DICOM数据失败: {0}
dicom.provider.shutdown.failed=关闭DicomDataProvider失败: {0}
dicom.provider.validation.failed=DICOM文件验证失败: {0}
dicom.provider.tags.empty=DICOM标签数据为空
dicom.provider.study.uid.empty=检查实例UID为空
dicom.provider.series.uid.empty=序列实例UID为空
dicom.provider.init=系统初始化完成，根目录: {0}
dicom.provider.listener.error=监听器错误: {0}
dicom.provider.monitor.start=开始监控目录: {0}
dicom.provider.monitor.stopped=文件监控已停止
dicom.provider.monitor.failed=文件监控失败: {0}
dicom.provider.file.changed=检测到DICOM文件变化: {0}
dicom.provider.first.exam=检测到第一个检查，自动选择: {0}
dicom.provider.first.series=自动选择第一个序列: {0}
dicom.provider.images.loaded=成功加载序列图像，数量: {0}
dicom.provider.exam.attributes.updated=成功更新检查属性: 患者ID={0}, 患者姓名={1}
dicom.provider.validate.first.exam.failed=验证首个检查失败

# Configuration Messages
config.file.not.found=配置文件未找到: {0}
config.file.loaded=成功加载配置文件
config.key.not.found=配置键未找到: {0}
config.invalid.number=无效的数字配置值: {0} = {1}
config.invalid.log.level=无效的日志级别: {0}

# System Messages
dicom.provider.system.init=系统初始化完成，根目录: {0}
dicom.provider.system.shutdown=系统已关闭

# Tag parsing messages
dicom.tag.create.failed=创建标签失败: {0}
dicom.tag.parse.failed=解析DICOM标签失败: {0}
dicom.tag.empty.model=DicomFileModel为空
dicom.tag.empty.attributes=DICOM属性为空
dicom.tag.empty.id=标签ID不能为空
dicom.model.empty.id=ID不能为空
dicom.image.empty.id=图像ID不能为空
dicom.image.create.failed=创建DicomImage失败: {0}

# Service related messages
dicom.service.init.failed=DICOM服务初始化失败
dicom.service.init.complete=DICOM服务初始化完成，扫描目录: {0}
dicom.service.refresh.complete=DICOM数据已刷新
dicom.service.refresh.failed=刷新DICOM数据失败
dicom.directory.not.exist=DICOM扫描目录不存在: {0}
dicom.directory.create.failed=创建DICOM扫描目录失败: {0}
dicom.no.files=目录中没有找到DICOM文件: {0}
dicom.no.files.found=未找到DICOM文件
dicom.process.file.failed=处理DICOM文件失败: {0}, 错误: {1}
dicom.process.file.success=成功处理DICOM文件: {0}
dicom.process.unknown.error=处理DICOM文件时发生未知错误: {0} - {1}
dicom.process.summary=DICOM处理完成: 成功={0}, 失败={1}
dicom.aspect.processing.error=处理DICOM数据时发生错误: {0} - {1}

# 分析模块消息
analysis.error=分析失败: {0}
analysis.timeout=分析超时
analysis.unsupported=不支持的分析类型: {0}

# 分析状态消息
analysis.in.progress=分析已经在进行中，请等待当前分析完成
analysis.started=开始分析...
analysis.completed=分析成功完成，结果报告已生成
analysis.failed=分析失败，未能生成有效报告
analysis.process.result.error=处理结果时发生错误: {0}

# 协议相关消息
analysis.protocol.info=分析协议: {0} - {1}
analysis.series.prefix=Series

# 错误消息
analysis.unknown.error=未知错误
analysis.execution.failed=执行失败: {0}

# 文件相关消息
analysis.output.file.warning=警告: 输出文件未生成



# 错误消息提取关键词
analysis.io.error.prefix=IO错误:
analysis.create.process.error=CreateProcess error=
analysis.ellipsis=...
