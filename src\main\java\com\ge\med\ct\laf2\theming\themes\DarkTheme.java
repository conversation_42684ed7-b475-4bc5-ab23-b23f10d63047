package com.ge.med.ct.laf2.theming.themes;

import com.ge.med.ct.laf2.theming.Theme;
import java.awt.Color;
import java.awt.Dimension;
import java.awt.Font;

/**
 * 深色主题实现
 */
public class DarkTheme implements Theme {
    private static final CTTheme ctTheme = new CTTheme(); // 用于继承部分属性

    @Override
    public String getName() {
        return "dark-theme";
    }

    @Override
    public String getDisplayName() {
        return "深色主题";
    }

    // 应用程序常量 - 与CT主题相同
    @Override
    public String getAppName() {
        return ctTheme.getAppName();
    }

    @Override
    public String getAppVersion() {
        return ctTheme.getAppVersion();
    }

    // 颜色常量 - 基础
    @Override
    public Color getPrimaryColor() {
        return new Color(33, 150, 243); // 蓝色主色调
    }

    @Override
    public Color getSecondaryColor() {
        return new Color(25, 118, 210); // 深蓝色按钮背景
    }

    @Override
    public Color getBackgroundColor() {
        return new Color(33, 33, 33); // 深灰色背景
    }

    @Override
    public Color getHeaderBackgroundColor() {
        return new Color(18, 18, 18); // 黑色头部背景
    }

    @Override
    public Color getPanelBackgroundColor() {
        return new Color(48, 48, 48); // 深灰色面板背景
    }

    @Override
    public Color getTextColor() {
        return new Color(255, 255, 255); // 白色文本
    }

    @Override
    public Color getTextBrightColor() {
        return new Color(255, 255, 255); // 白色文本
    }

    @Override
    public Color getTextLightColor() {
        return new Color(144, 202, 249); // 浅蓝色文本
    }

    @Override
    public Color getBorderColor() {
        return new Color(66, 66, 66); // 灰色边框
    }

    // 颜色常量 - 状态
    @Override
    public Color getSuccessColor() {
        return new Color(76, 175, 80); // 绿色成功提示
    }

    @Override
    public Color getWarningColor() {
        return new Color(255, 152, 0); // 橙色警告提示
    }

    @Override
    public Color getErrorColor() {
        return new Color(244, 67, 54); // 红色错误提示
    }

    // 颜色常量 - 表格
    @Override
    public Color getTableHeaderColor() {
        return new Color(66, 66, 66); // 深灰色表头
    }

    @Override
    public Color getTableSelectedRowColor() {
        return new Color(55, 71, 79); // 深蓝灰色选中行
    }

    @Override
    public Color getTableAlternateRowColor() {
        return new Color(38, 38, 38); // 深灰色交替行
    }

    // 颜色常量 - 界面元素
    @Override
    public Color getToolbarBackgroundColor() {
        return new Color(33, 33, 33); // 深灰色工具栏
    }

    @Override
    public Color getMenubarColor() {
        return new Color(18, 18, 18); // 黑色菜单栏
    }

    @Override
    public Color getButtonBackgroundColor() {
        return new Color(66, 66, 66); // 灰色按钮
    }

    @Override
    public Color getButtonBorderColor() {
        return new Color(97, 97, 97); // 浅灰色按钮边框
    }

    @Override
    public Color getFieldBackgroundColor() {
        return new Color(48, 48, 48); // 深灰色输入框
    }

    // 颜色常量 - 列表
    @Override
    public Color getListHeaderColor() {
        return new Color(66, 66, 66); // 深灰色列表头
    }

    @Override
    public Color getListSelectedRowColor() {
        return new Color(55, 71, 79); // 深蓝灰色选中行
    }

    @Override
    public Color getListAlternateRowColor() {
        return new Color(38, 38, 38); // 深灰色交替行
    }

    @Override
    public Color getListBackgroundColor() {
        return new Color(33, 33, 33); // 深灰色列表背景
    }

    // 字体常量 - 与CT主题相同
    @Override
    public Font getHeaderFont() {
        return ctTheme.getHeaderFont();
    }

    @Override
    public Font getTitleFont() {
        return ctTheme.getTitleFont();
    }

    @Override
    public Font getNormalFont() {
        return ctTheme.getNormalFont();
    }

    @Override
    public Font getSmallFont() {
        return ctTheme.getSmallFont();
    }

    @Override
    public Font getDefaultFont() {
        return ctTheme.getDefaultFont();
    }

    @Override
    public Font getLargeFont() {
        return ctTheme.getLargeFont();
    }

    @Override
    public Font getReportFont() {
        return ctTheme.getReportFont();
    }

    @Override
    public Font getTableHeaderFont() {
        return ctTheme.getTableHeaderFont();
    }

    @Override
    public Font getTableContentFont() {
        return ctTheme.getTableContentFont();
    }

    @Override
    public Font getImageIndexFont() {
        return ctTheme.getImageIndexFont();
    }

    @Override
    public Font getListItemFont() {
        return ctTheme.getListItemFont();
    }

    // 尺寸常量 - 与CT主题相同
    @Override
    public int getDefaultMargin() {
        return ctTheme.getDefaultMargin();
    }

    @Override
    public int getDefaultPadding() {
        return ctTheme.getDefaultPadding();
    }

    @Override
    public int getDefaultBorderRadius() {
        return ctTheme.getDefaultBorderRadius();
    }

    @Override
    public int getDefaultButtonHeight() {
        return ctTheme.getDefaultButtonHeight();
    }

    @Override
    public int getDefaultComponentHeight() {
        return ctTheme.getDefaultComponentHeight();
    }

    @Override
    public int getDefaultIconSize() {
        return ctTheme.getDefaultIconSize();
    }

    @Override
    public Dimension getDefaultButtonSize() {
        return ctTheme.getDefaultButtonSize();
    }

    @Override
    public Dimension getSmallButtonSize() {
        return ctTheme.getSmallButtonSize();
    }

    @Override
    public Dimension getLargeButtonSize() {
        return ctTheme.getLargeButtonSize();
    }

    // 图标尺寸 - 与CT主题相同
    @Override
    public int getIconSmall() {
        return ctTheme.getIconSmall();
    }

    @Override
    public int getIconMedium() {
        return ctTheme.getIconMedium();
    }

    @Override
    public int getIconLarge() {
        return ctTheme.getIconLarge();
    }

    // 窗口尺寸 - 与CT主题相同
    @Override
    public Dimension getDefaultWindowSize() {
        return ctTheme.getDefaultWindowSize();
    }

    @Override
    public Dimension getDialogSize() {
        return ctTheme.getDialogSize();
    }

    // 动画常量 - 与CT主题相同
    @Override
    public int getAnimationDuration() {
        return ctTheme.getAnimationDuration();
    }
}
