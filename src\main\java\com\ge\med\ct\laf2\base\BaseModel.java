package com.ge.med.ct.laf2.base;

import java.beans.PropertyChangeListener;
import java.beans.PropertyChangeSupport;

/**
 * 基础模型类，提供属性变更通知功能
 */
public abstract class BaseModel {
    
    private final PropertyChangeSupport propertyChangeSupport;
    
    public BaseModel() {
        this.propertyChangeSupport = new PropertyChangeSupport(this);
    }
    
    /**
     * 添加属性变更监听器
     */
    public void addPropertyChangeListener(PropertyChangeListener listener) {
        if (listener != null) {
            propertyChangeSupport.addPropertyChangeListener(listener);
        }
    }
    
    /**
     * 移除属性变更监听器
     */
    public void removePropertyChangeListener(PropertyChangeListener listener) {
        if (listener != null) {
            propertyChangeSupport.removePropertyChangeListener(listener);
        }
    }
    
    /**
     * 触发属性变更事件
     */
    protected void firePropertyChange(String propertyName, Object oldValue, Object newValue) {
        propertyChangeSupport.firePropertyChange(propertyName, oldValue, newValue);
    }
} 