# CT Quality Assurance Tool 配置文件

# 应用程序设置
app.name=CT Quality Assurance Tool
app.version=1.0.0
app.logging.level=INFO

# UI设置
ui.theme=light
ui.locale=zh_CN
ui.language=zh_CN

# 表格设置
table.config.file=config/table_columns.properties

# 文件监控
file.monitor.enabled=true
file.monitor.interval=5000

# 线程池设置
thread.pool.size=4
dicom.max.threads=4

# DICOM设置
dicom.scan.directory.win=C:\\GEHC\\usr\\g\\sdc_image_pool\\images
dicom.scan.directory.linux=/usr/g/sdc_image_pool/images/p1
dicom.cache.enabled=true
dicom.cache.size=1000

# DICOM文件处理配置
dicom.validation.detailed_warnings=true
dicom.validation.group_warnings=true
dicom.validation.max_examples=10
dicom.validation.skip_invalid=true
dicom.scan.parallel=true

# 报告
report.output.directory.win=C:\\GEHC\\usr\\g\\bin\\qatdemo\\reports
report.output.directory.linux=/usr/g/bin/qatdemo/reports



