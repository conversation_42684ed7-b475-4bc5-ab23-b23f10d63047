package com.ge.med.ct.laf2.components;

import com.ge.med.ct.laf2.theming.ThemeConstants;

import javax.swing.*;
import javax.swing.border.EmptyBorder;
import java.awt.*;

/**
 * 自定义标签组件，提供现代化的外观和交互效果
 */
public class CTLabel extends JLabel {
    private static final long serialVersionUID = 1L;

    /**
     * 标签类型
     */
    public enum LabelType {
        DEFAULT, TITLE, HEADER, INFO, WARNING, ERROR, SUCCESS
    }

    private LabelType type = LabelType.DEFAULT;
    private boolean underline = false;

    /**
     * 创建默认标签
     */
    public CTLabel() {
        configureLabel();
    }

    /**
     * 创建指定文本的标签
     *
     * @param text 标签文本
     */
    public CTLabel(String text) {
        super(text);
        configureLabel();
    }

    /**
     * 创建指定图标的标签
     *
     * @param icon 标签图标
     */
    public CTLabel(Icon icon) {
        super(icon);
        configureLabel();
    }

    /**
     * 创建指定文本和图标的标签
     *
     * @param text 标签文本
     * @param icon 标签图标
     */
    public CTLabel(String text, Icon icon) {
        super(text, icon, JLabel.LEADING);
        configureLabel();
    }

    /**
     * 创建指定文本和对齐方式的标签
     *
     * @param text 标签文本
     * @param horizontalAlignment 水平对齐方式
     */
    public CTLabel(String text, int horizontalAlignment) {
        super(text, horizontalAlignment);
        configureLabel();
    }

    /**
     * 创建带类型的标签
     *
     * @param text 标签文本
     * @param type 标签类型
     */
    public CTLabel(String text, LabelType type) {
        super(text);
        configureLabel();
        setLabelType(type);
    }

    /**
     * 配置标签的基本属性
     */
    private void configureLabel() {
        setFont(ThemeConstants.Fonts.getNormal());
        setForeground(ThemeConstants.Colors.getText());
        setBorder(new EmptyBorder(2, 0, 2, 0));
        setOpaque(false);
    }

    /**
     * 设置标签类型
     *
     * @param type 标签类型
     */
    public void setLabelType(LabelType type) {
        this.type = type;

        switch (type) {
            case DEFAULT:
                setFont(ThemeConstants.Fonts.getNormal());
                setForeground(ThemeConstants.Colors.getText());
                break;
            case TITLE:
                setFont(ThemeConstants.Fonts.getTitle());
                setForeground(ThemeConstants.Colors.getText());
                break;
            case HEADER:
                setFont(ThemeConstants.Fonts.getHeader());
                setForeground(ThemeConstants.Colors.getText());
                break;
            case INFO:
                setFont(ThemeConstants.Fonts.getNormal());
                setForeground(ThemeConstants.Colors.getPrimary());
                break;
            case WARNING:
                setFont(ThemeConstants.Fonts.getNormal());
                setForeground(ThemeConstants.Colors.getWarning());
                break;
            case ERROR:
                setFont(ThemeConstants.Fonts.getNormal());
                setForeground(ThemeConstants.Colors.getError());
                break;
            case SUCCESS:
                setFont(ThemeConstants.Fonts.getNormal());
                setForeground(ThemeConstants.Colors.getSuccess());
                break;
        }

        repaint();
    }

    /**
     * 获取标签类型
     *
     * @return 标签类型
     */
    public LabelType getLabelType() {
        return type;
    }

    /**
     * 设置是否显示下划线
     *
     * @param underline 是否显示下划线
     */
    public void setUnderline(boolean underline) {
        this.underline = underline;
        repaint();
    }

    /**
     * 是否显示下划线
     *
     * @return 是否显示下划线
     */
    public boolean isUnderline() {
        return underline;
    }

    @Override
    protected void paintComponent(Graphics g) {
        Graphics2D g2 = (Graphics2D) g.create();
        g2.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
        g2.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_ON);

        // 调用父类绘制文本和图标
        super.paintComponent(g2);

        // 绘制下划线
        if (underline && getText() != null && !getText().isEmpty()) {
            g2.setColor(getForeground());
            int textWidth = g2.getFontMetrics().stringWidth(getText());
            int y = getHeight() - 3;

            if (getIcon() != null) {
                int iconTextGap = getIconTextGap();
                int iconWidth = getIcon().getIconWidth();
                g2.drawLine(iconWidth + iconTextGap, y, iconWidth + iconTextGap + textWidth, y);
            } else {
                g2.drawLine(0, y, textWidth, y);
            }
        }

        g2.dispose();
    }

    /**
     * 创建标题标签
     *
     * @param text 标签文本
     * @return 标题标签
     */
    public static CTLabel createTitleLabel(String text) {
        return new CTLabel(text, LabelType.TITLE);
    }

    /**
     * 创建标头标签
     *
     * @param text 标签文本
     * @return 标头标签
     */
    public static CTLabel createHeaderLabel(String text) {
        return new CTLabel(text, LabelType.HEADER);
    }

    /**
     * 创建信息标签
     *
     * @param text 标签文本
     * @return 信息标签
     */
    public static CTLabel createInfoLabel(String text) {
        return new CTLabel(text, LabelType.INFO);
    }

    /**
     * 创建警告标签
     *
     * @param text 标签文本
     * @return 警告标签
     */
    public static CTLabel createWarningLabel(String text) {
        return new CTLabel(text, LabelType.WARNING);
    }

    /**
     * 创建错误标签
     *
     * @param text 标签文本
     * @return 错误标签
     */
    public static CTLabel createErrorLabel(String text) {
        return new CTLabel(text, LabelType.ERROR);
    }

    /**
     * 创建成功标签
     *
     * @param text 标签文本
     * @return 成功标签
     */
    public static CTLabel createSuccessLabel(String text) {
        return new CTLabel(text, LabelType.SUCCESS);
    }
}