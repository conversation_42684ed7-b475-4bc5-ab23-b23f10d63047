# 表格列配置文件

# 检查表格列配置 - 逗号分隔，定义显示顺序
table.exam.columns=PatientID,StudyID,StationName,PatientName,StudyDate,Modality,StudyDescription

# 患者ID
table.exam.column.PatientID.display-name=患者ID
table.exam.column.PatientID.width=120
table.exam.column.PatientID.visible=true
table.exam.column.PatientID.order=1

# 检查ID
table.exam.column.StudyID.display-name=检查ID
table.exam.column.StudyID.width=120
table.exam.column.StudyID.visible=true
table.exam.column.StudyID.order=2

# 站点名称
table.exam.column.StationName.display-name=站点名称
table.exam.column.StationName.width=150
table.exam.column.StationName.visible=true
table.exam.column.StationName.order=3

# 患者姓名
table.exam.column.PatientName.display-name=姓名
table.exam.column.PatientName.width=120
table.exam.column.PatientName.visible=true
table.exam.column.PatientName.order=4

# 检查日期
table.exam.column.StudyDate.display-name=日期
table.exam.column.StudyDate.width=120
table.exam.column.StudyDate.visible=true
table.exam.column.StudyDate.order=5

# 模态类型
table.exam.column.Modality.display-name=模态
table.exam.column.Modality.width=80
table.exam.column.Modality.visible=true
table.exam.column.Modality.order=6

# 检查描述
table.exam.column.StudyDescription.display-name=描述
table.exam.column.StudyDescription.width=200
table.exam.column.StudyDescription.visible=true
table.exam.column.StudyDescription.order=7

# ==================== 序列表格列配置 ====================
# 检查表格列配置 - 逗号分隔，定义显示顺序
table.series.columns=SeriesNumber,Modality,ImageCount,SeriesDescription,Manufacturer

# 序列号
table.series.column.SeriesNumber.display-name=序列号
table.series.column.SeriesNumber.width=80
table.series.column.SeriesNumber.visible=true
table.series.column.SeriesNumber.order=1

# 类型/模态
table.series.column.Modality.display-name=类型
table.series.column.Modality.width=80
table.series.column.Modality.visible=true
table.series.column.Modality.order=2

# 图像数量
table.series.column.ImageCount.display-name=图像数
table.series.column.ImageCount.width=80
table.series.column.ImageCount.visible=true
table.series.column.ImageCount.order=3
table.series.column.ImageCount.special=true

# 序列描述
table.series.column.SeriesDescription.display-name=描述
table.series.column.SeriesDescription.width=200
table.series.column.SeriesDescription.visible=true
table.series.column.SeriesDescription.order=4

# 制造商
table.series.column.Manufacturer.display-name=制造商
table.series.column.Manufacturer.width=150
table.series.column.Manufacturer.visible=true
table.series.column.Manufacturer.order=5

# ==================== 图像表格列配置 ====================
# 检查表格列配置 - 逗号分隔，定义显示顺序
table.image.columns=InstanceNumber,ImagePosition,SliceThickness,GantryTilt,FieldOfView,ReconstructionDiameter,ConvolutionKernel,KVP,Rows,ImageComments

# 图像号
table.image.column.InstanceNumber.display-name=图像号
table.image.column.InstanceNumber.width=80
table.image.column.InstanceNumber.visible=true
table.image.column.InstanceNumber.order=1

# 图像中心位置
table.image.column.ImagePosition.display-name=位置
table.image.column.ImagePosition.width=100
table.image.column.ImagePosition.visible=true
table.image.column.ImagePosition.order=2
table.image.column.ImagePosition.special=true

# 层厚
table.image.column.SliceThickness.display-name=层厚(mm)
table.image.column.SliceThickness.width=100
table.image.column.SliceThickness.visible=true
table.image.column.SliceThickness.order=3

# 机架倾斜
table.image.column.GantryTilt.display-name=倾斜(°)
table.image.column.GantryTilt.width=80
table.image.column.GantryTilt.visible=true
table.image.column.GantryTilt.order=4

# 扫描视野
table.image.column.FieldOfView.display-name=SFOV(cm)
table.image.column.FieldOfView.width=120
table.image.column.FieldOfView.visible=true
table.image.column.FieldOfView.order=5

# 重建视野
table.image.column.ReconstructionDiameter.display-name=DFOV(cm)
table.image.column.ReconstructionDiameter.width=120
table.image.column.ReconstructionDiameter.visible=true
table.image.column.ReconstructionDiameter.order=6

# 算法
table.image.column.ConvolutionKernel.display-name=算法
table.image.column.ConvolutionKernel.width=100
table.image.column.ConvolutionKernel.visible=true
table.image.column.ConvolutionKernel.order=7

# KV
table.image.column.KVP.display-name=KV
table.image.column.KVP.width=80
table.image.column.KVP.visible=true
table.image.column.KVP.order=8

# 矩阵
table.image.column.Rows.display-name=矩阵
table.image.column.Rows.width=100
table.image.column.Rows.visible=true
table.image.column.Rows.order=9

# 相位
table.image.column.ImageComments.display-name=相位(%)
table.image.column.ImageComments.width=100
table.image.column.ImageComments.visible=true
table.image.column.ImageComments.order=10