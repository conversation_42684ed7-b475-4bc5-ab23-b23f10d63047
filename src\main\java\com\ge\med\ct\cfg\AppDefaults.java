package com.ge.med.ct.cfg;

/**
 * 应用程序默认值
 * 集中管理应用程序的默认配置值
 */
public final class AppDefaults {
    // 应用程序默认值
    public static final String DEFAULT_APP_NAME = "CT Quality Assurance Tool";
    public static final String DEFAULT_APP_VERSION = "1.0.0";
    public static final String DEFAULT_UI_THEME = "light";
    public static final String DEFAULT_UI_LOCALE = "zh_CN";

    // 文件路径默认值
    public static final String DEFAULT_CONFIG_DIR = "config";
    public static final String DEFAULT_CONFIG_PATH = DEFAULT_CONFIG_DIR + "/application.properties";
    public static final String DEFAULT_TABLE_CONFIG_PATH = DEFAULT_CONFIG_DIR + "/table_columns.properties";
    public static final String DEFAULT_EXPORT_DIR = "export";
    public static final String DEFAULT_TEMP_DIR = "temp";
    public static final String DEFAULT_LOG_DIR = "logs";
    public static final String DEFAULT_LOG_LEVEL = "INFO";
    public static final String DEFAULT_REPORT_DIR = "./reports";

    // DICOM默认值
    public static final String DEFAULT_DICOM_ROOT_DIR = System.getProperty("java.io.tmpdir") + java.io.File.separator + "dicom";
    public static final boolean DEFAULT_DICOM_CACHE_ENABLED = true;
    public static final int DEFAULT_DICOM_CACHE_SIZE = 1000;
    public static final String DEFAULT_DICOM_CHARSET = "GB18030";
    
    // DICOM验证默认值
    public static final boolean DEFAULT_DETAILED_WARNINGS = true;
    public static final boolean DEFAULT_GROUP_WARNINGS = true;
    public static final int DEFAULT_MAX_EXAMPLES = 10;
    public static final boolean DEFAULT_SKIP_INVALID = true;
    public static final boolean DEFAULT_SCAN_PARALLEL = true;
    public static final int DEFAULT_MAX_THREADS = 4;

    // 表格默认值
    public static final int DEFAULT_COLUMN_WIDTH = 100;
    public static final boolean DEFAULT_COLUMN_VISIBLE = true;
    public static final int DEFAULT_COLUMN_ORDER = 0;

    // 私有构造函数，防止实例化
    private AppDefaults() {
        throw new AssertionError("工具类不应被实例化");
    }
}
