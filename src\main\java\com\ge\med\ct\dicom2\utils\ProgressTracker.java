package com.ge.med.ct.dicom2.utils;

import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Consumer;

/**
 * 加载进度跟踪器
 * 统一管理进度计算和回调通知
 */
public class ProgressTracker {
    private final int total;
    private final AtomicInteger processed = new AtomicInteger(0);
    private final AtomicInteger failed = new AtomicInteger(0);

    public ProgressTracker(int total) {
        this.total = total;
    }

    /**
     * 增加处理成功计数
     */
    public void incrementProcessed() {
        processed.incrementAndGet();
    }

    /**
     * 增加处理失败计数
     */
    public void incrementFailed() {
        failed.incrementAndGet();
    }

    /**
     * 更新进度并通知回调
     */
    public void updateProgress(Consumer<LoadingProgress> progressCallback) {
        if (progressCallback != null) {
            progressCallback.accept(new LoadingProgress(processed.get(), total));
        }
    }

    /**
     * 初始化进度
     */
    public void initializeProgress(Consumer<LoadingProgress> progressCallback) {
        if (progressCallback != null) {
            progressCallback.accept(new LoadingProgress(0, total));
        }
    }

    /**
     * 完成进度
     */
    public void finalizeProgress(Consumer<LoadingProgress> progressCallback) {
        if (progressCallback != null) {
            progressCallback.accept(new LoadingProgress(processed.get(), total));
        }
    }

    public int getProcessed() {
        return processed.get();
    }

    public int getFailed() {
        return failed.get();
    }

    public int getTotal() {
        return total;
    }
}