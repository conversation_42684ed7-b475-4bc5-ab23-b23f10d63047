# CT 质量保证工具详细设计

## 1. 引言

### 1.1 目的

本文档提供 CT 质量保证工具的详细设计，包括各模块的详细结构、类设计、接口定义和交互关系。该文档旨在为开发团队提供实现指导，确保系统各部分的一致性和完整性。

### 1.2 范围

本详细设计文档涵盖 CT 质量保证工具的所有主要模块，包括：

- DICOM 数据服务
- 表格数据模块
- 分析服务
- 报告服务
- 用户界面
- 异常处理机制
- 配置管理

### 1.3 参考文档

- [设计概要](design-overview.md)
- [系统架构设计](architecture/system-architecture.md)
- [表格数据模块设计](module-design/table-data-module.md)

## 2. 系统结构

### 2.1 包结构

系统的包结构如下：

```
com.ge.med.ct
├── cfg              # 配置管理
│   ├── dicom        # DICOM配置服务
│   ├── report       # 报告配置服务
│   └── table        # 表格配置管理
├── dicom2           # DICOM数据服务（版本2）
│   ├── core         # 核心组件
│   ├── model        # DICOM数据模型
│   ├── reader       # DICOM文件读取器
│   ├── service      # DICOM服务层
│   └── tag          # DICOM标签定义和解析
├── analysis         # 分析服务
│   ├── model        # 分析数据模型
│   └── service      # 分析服务实现
├── laf2             # 用户界面（Look and Feel v2）
│   ├── base         # 基础UI组件
│   ├── components   # 自定义UI组件
│   ├── presenter    # MVP表示者
│   ├── views        # 视图组件
│   ├── theming      # 主题管理
│   └── utils        # UI工具类
├── service          # 通用服务
│   └── converter    # 数据转换器
└── exception        # 异常处理
    ├── core         # 核心异常类
    ├── aspect       # AOP异常处理
    ├── message      # 异常消息系统
    └── util         # 异常工具类
```

### 2.2 模块依赖关系

模块间的主要依赖关系如下：

```
UI模块 --> 业务服务模块 --> DICOM数据服务模块 --> 配置管理模块
   |           |                |                   |
   |           |                |                   v
   |           |                |             异常处理模块
   |           |                v                   ^
   |           |          工具类模块                |
   |           v                                    |
   +---------> 异常处理模块 <-----------------------+
```

## 3. DICOM 数据服务

### 3.1 概述

**包路径**: `com.ge.med.ct.dicom2`（版本 2 实现）
**技术基础**: 基于 DCM4CHE 库实现 DICOM 标准支持

DICOM 数据服务负责 DICOM 文件的加载、解析和管理，是系统的核心数据服务。该服务支持标准 DICOM 标签以及 GE CT 设备的专用标签。

### 3.2 主要组件

#### 3.2.1 DICOM 数据模型

DICOM 数据模型定义了系统中 DICOM 数据的表示方式，主要包括以下类：

- `DicomExam`: 表示一个 DICOM 检查
- `DicomSeries`: 表示一个 DICOM 序列
- `DicomImage`: 表示一个 DICOM 图像
- `DicomTag`: 表示一个 DICOM 标签

类图和详细设计参见：[DICOM 数据模型设计](module-design/dicom-data-model.md)

#### 3.2.2 DICOM 文件加载器

DICOM 文件加载器负责从文件系统加载 DICOM 文件，主要包括以下类：

- `DicomFileService`: DICOM 文件服务，提供文件管理功能
- `DicomDataService`: DICOM 数据服务，提供数据访问接口
- `DicomDataProvider`: DICOM 数据提供者，核心数据访问组件
- `DicomMetadataReader`: DICOM 元数据读取器

#### 3.2.3 DICOM 标签解析器

DICOM 标签解析器负责解析 DICOM 文件中的标签，主要包括以下类：

- `DicomTagParser`: DICOM 标签解析器，提供标签解析功能
- `DicomTagServiceSimplified`: DICOM 标签服务简化版，提供标签管理接口
- `DicomTagConstants`: DICOM 标签常量定义，包含 GE CT 专用标签
- `DicomTag`: DICOM 标签数据模型

### 3.3 关键流程

#### 3.3.1 DICOM 文件加载流程

```
1. 用户选择DICOM文件或目录
2. DicomFileService扫描目录，识别DICOM文件
3. DicomDataProvider使用DCM4CHE库加载DICOM文件
4. DicomMetadataReader读取文件元数据
5. DicomTagParser解析DICOM标签（包括GE CT专用标签）
6. 创建DicomExam、DicomSeries和DicomImage对象
7. DicomDataService将对象存入Caffeine缓存
8. 通知UI更新显示
```

#### 3.3.2 DICOM 数据查询流程

```
1. 用户在UI上选择查询条件
2. UI通过Presenter调用DicomDataService的查询方法
3. DicomDataService根据条件查询Caffeine缓存
4. 如缓存未命中，通过DicomDataProvider重新加载
5. 返回查询结果给Presenter
6. Presenter更新UI显示结果
```

#### 3.3.3 协议推断流程

```
1. 用户加载DICOM序列数据
2. ProtocolInferService创建推断上下文
3. 从DICOM标签中提取序列描述和技术参数
4. 基于序列描述进行协议字符串匹配
5. 基于技术参数进行协议类型推断
6. 确定协议名称和协议ID
7. 返回格式化的协议结果
8. ProtocolTypeComboBox更新UI显示
```

### 3.4 接口定义

#### 3.4.1 DicomDataService 接口

```java
/**
 * DICOM数据服务接口 - dicom2包实现
 * 基于DCM4CHE库提供DICOM数据访问功能
 */
public interface DicomDataService {
    /**
     * 加载DICOM文件
     * @param filePath DICOM文件路径
     * @return DICOM检查对象
     */
    DicomExam loadDicomFile(String filePath) throws QAToolException;

    /**
     * 扫描DICOM目录
     * @param directoryPath DICOM目录路径
     * @return DICOM检查列表
     */
    List<DicomExam> scanDirectory(String directoryPath) throws QAToolException;

    /**
     * 获取所有检查
     * @return 检查列表
     */
    List<DicomExam> getAllExams();

    /**
     * 获取检查的所有序列
     * @param examId 检查ID
     * @return 序列列表
     */
    List<DicomSeries> getSeriesByExamId(String examId);

    /**
     * 获取序列的所有图像
     * @param seriesId 序列ID
     * @return 图像列表
     */
    List<DicomImage> getImagesBySeriesId(String seriesId);

    /**
     * 清除缓存
     */
    void clearCache();

    /**
     * 获取缓存统计信息
     * @return 缓存统计
     */
    CacheStats getCacheStats();
}
```

#### 3.4.2 ProtocolInferService 接口

```java
/**
 * 协议推断服务 - analysis.service包实现
 * 提供从DICOM序列推断IA协议参数的功能
 */
public class ProtocolInferService {
    /**
     * 从序列中推断协议参数
     * @param series DICOM序列
     * @param exam 相关检查
     * @param images 序列图像列表
     * @return 格式化的协议字符串 (格式：协议字符串|协议名称|协议ID)
     */
    public String inferFromSeries(DicomSeries series, DicomExam exam,
                                 List<DicomImage> images);

    /**
     * 创建推断上下文
     * @param series DICOM序列
     * @param exam 相关检查
     * @param images 序列图像列表
     * @return 推断上下文
     */
    private InferenceContext createContext(DicomSeries series, DicomExam exam,
                                          List<DicomImage> images);

    /**
     * 推断协议字符串
     * @param context 推断上下文
     * @return 协议字符串
     */
    private String inferProtocolString(InferenceContext context);

    /**
     * 推断协议名称
     * @param context 推断上下文
     * @return 协议名称
     */
    private String inferProtocolName(InferenceContext context);
}
```

#### 3.4.3 ProtocolTypeMode 接口

```java
/**
 * 协议类型模式 - laf2.model包实现
 * 负责协议类型定义和管理
 */
public class ProtocolTypeMode {
    /**
     * 获取所有协议类型
     * @return 协议类型列表
     */
    public static List<ProtocolType> getAllProtocolTypes();

    /**
     * 根据协议字符串查找协议类型
     * @param protocolString 协议字符串
     * @return 协议类型，未找到返回null
     */
    public static ProtocolType findByProtocolString(String protocolString);
}
```

## 4. 表格数据模块

表格数据模块负责将 DICOM 数据转换为表格可显示的格式，是系统的重要数据处理模块。

详细设计参见：[表格数据模块设计](module-design/table-data-module.md)

主要组件包括：

- **表格数据引擎**：负责数据转换和处理，详见[表格数据引擎设计](module-design/table-data-engine.md)
- **表格配置**：负责表格列配置管理，详见[表格配置设计](module-design/table-config.md)
- **相位列实现**：CT 图像相位列的特殊实现，详见[相位列实现设计](module-design/phase-column.md)

## 5. 分析服务

### 5.1 概述

分析服务提供各种图像分析和质量评估功能，是系统的核心业务逻辑。

### 5.2 主要组件

#### 5.2.1 分析工具

分析工具提供各种图像分析功能，主要包括以下类：

- `AnalysisTool`: 分析工具接口
- `DensityAnalysisTool`: 密度分析工具
- `DistanceMeasurementTool`: 距离测量工具
- `NoiseAnalysisTool`: 噪声分析工具
- `ResolutionAnalysisTool`: 分辨率分析工具
- `UniformityAnalysisTool`: 均匀性分析工具

#### 5.2.2 质量评估

质量评估负责根据预设标准评估图像质量，主要包括以下类：

- `QualityEvaluator`: 质量评估器接口
- `CTQualityEvaluator`: CT 质量评估器实现
- `QualityStandard`: 质量标准定义
- `QualityReport`: 质量报告

### 5.3 关键流程

#### 5.3.1 图像分析流程

```
1. 用户选择分析工具
2. 用户在图像上选择感兴趣区域
3. 分析工具执行分析算法
4. 分析结果存入分析结果对象
5. UI显示分析结果
```

#### 5.3.2 质量评估流程

```
1. 用户请求质量评估
2. 系统收集所有分析结果
3. 质量评估器根据质量标准评估结果
4. 生成质量评估报告
5. UI显示评估结果
```

### 5.4 接口定义

#### 5.4.1 AnalysisService 接口

```java
public interface AnalysisService {
    /**
     * 获取所有分析工具
     * @return 分析工具列表
     */
    List<AnalysisTool> getAllTools();

    /**
     * 执行分析
     * @param toolId 工具ID
     * @param image 图像
     * @param roi 感兴趣区域
     * @return 分析结果
     */
    AnalysisResult analyze(String toolId, DicomImage image, ROI roi) throws QAToolException;

    /**
     * 评估质量
     * @param seriesId 序列ID
     * @return 质量评估报告
     */
    QualityReport evaluateQuality(String seriesId) throws QAToolException;
}
```

## 6. 报告服务

### 6.1 概述

报告服务负责生成质量评估报告，是系统的输出组件。

### 6.2 主要组件

#### 6.2.1 报告模板

报告模板定义了报告的结构和格式，主要包括以下类：

- `ReportTemplate`: 报告模板接口
- `HTMLReportTemplate`: HTML 报告模板
- `PDFReportTemplate`: PDF 报告模板
- `TemplateManager`: 模板管理器

#### 6.2.2 报告生成器

报告生成器负责根据模板生成报告，主要包括以下类：

- `ReportGenerator`: 报告生成器接口
- `HTMLReportGenerator`: HTML 报告生成器
- `PDFReportGenerator`: PDF 报告生成器

#### 6.2.3 报告导出器

报告导出器负责将报告导出为文件，主要包括以下类：

- `ReportExporter`: 报告导出器接口
- `FileReportExporter`: 文件报告导出器
- `PrintReportExporter`: 打印报告导出器

### 6.3 关键流程

#### 6.3.1 报告生成流程

```
1. 用户请求生成报告
2. 系统选择合适的报告模板
3. 报告生成器收集分析结果和质量评估结果
4. 报告生成器根据模板生成报告
5. 报告显示在UI上
```

#### 6.3.2 报告导出流程

```
1. 用户请求导出报告
2. 用户选择导出格式和位置
3. 报告导出器将报告导出为文件
4. 系统提示导出成功
```

### 6.4 接口定义

#### 6.4.1 ReportService 接口

```java
public interface ReportService {
    /**
     * 生成报告
     * @param seriesId 序列ID
     * @param templateId 模板ID
     * @return 报告对象
     */
    Report generateReport(String seriesId, String templateId) throws QAToolException;

    /**
     * 导出报告
     * @param report 报告对象
     * @param format 导出格式
     * @param outputFile 输出文件
     */
    void exportReport(Report report, String format, File outputFile) throws QAToolException;

    /**
     * 获取所有报告模板
     * @return 模板列表
     */
    List<ReportTemplate> getAllTemplates();
}
```

## 7. 用户界面

### 7.1 概述

用户界面采用 Swing 框架实现，采用 MVP 架构模式，分离视图和业务逻辑。

### 7.2 主要组件

#### 7.2.1 主窗口

主窗口是系统的主界面，包含菜单栏、工具栏和状态栏，主要类包括：

- `MainFrame`: 主窗口类
- `MainMenuBar`: 主菜单栏
- `MainToolBar`: 主工具栏
- `StatusBar`: 状态栏

#### 7.2.2 图像显示面板

图像显示面板负责显示 DICOM 图像，支持缩放、平移和窗宽窗位调整，主要类包括：

- `ImagePanel`: 图像面板
- `ImageViewController`: 图像视图控制器
- `WindowLevelTool`: 窗宽窗位工具

#### 7.2.3 数据浏览面板

数据浏览面板显示 DICOM 数据的树形结构，主要类包括：

- `DataBrowserPanel`: 数据浏览面板
- `DicomTreeModel`: DICOM 树模型
- `DicomTreeRenderer`: DICOM 树渲染器

#### 7.2.4 分析工具面板

分析工具面板提供各种分析工具的控制界面，主要类包括：

- `AnalysisToolPanel`: 分析工具面板
- `ToolButtonGroup`: 工具按钮组
- `AnalysisResultPanel`: 分析结果面板

#### 7.2.5 报告面板

报告面板显示质量评估报告，主要类包括：

- `ReportPanel`: 报告面板
- `ReportViewController`: 报告视图控制器
- `ReportExportDialog`: 报告导出对话框

### 7.3 MVP 实现

系统采用 MVP 架构模式实现 UI，主要包括：

- **Model**: 数据模型，如 DicomExam、DicomSeries 等
- **View**: 视图接口和实现，如 MainFrame、ImagePanel 等
- **Presenter**: 表示者，负责连接 Model 和 View，处理用户操作

#### 7.3.1 主要 Presenter

- `MainPresenter`: 主窗口表示者
- `ImagePresenter`: 图像显示表示者
- `DataBrowserPresenter`: 数据浏览表示者
- `AnalysisPresenter`: 分析工具表示者
- `ReportPresenter`: 报告表示者

### 7.4 用户交互流程

#### 7.4.1 加载 DICOM 数据

```
1. 用户点击"打开文件"菜单项
2. MainPresenter显示文件选择对话框
3. 用户选择DICOM文件或目录
4. MainPresenter调用DicomService加载数据
5. DataBrowserPresenter更新数据浏览树
```

#### 7.4.2 查看图像

```
1. 用户在数据浏览树中选择图像
2. DataBrowserPresenter通知ImagePresenter
3. ImagePresenter从DicomService获取图像数据
4. ImagePresenter更新ImagePanel显示图像
```

#### 7.4.3 执行分析

```
1. 用户选择分析工具
2. AnalysisPresenter激活选中的工具
3. 用户在图像上选择感兴趣区域
4. AnalysisPresenter调用AnalysisService执行分析
5. AnalysisPresenter更新UI显示分析结果
```

## 8. 异常处理机制

### 8.1 概述

系统采用统一的异常处理机制，使用 AOP 方式处理异常，提高代码的健壮性和可维护性。

### 8.2 主要组件

#### 8.2.1 异常类层次

- `QAToolException`: 系统基础异常类
- `DicomException`: DICOM 相关异常
- `AnalysisException`: 分析相关异常
- `ReportException`: 报告相关异常
- `ConfigException`: 配置相关异常

#### 8.2.2 异常处理切面

- `ExceptionAspect`: 异常处理切面
- `HandleException`: 异常处理注解
- `ExceptionHandler`: 异常处理器接口
- `DefaultExceptionHandler`: 默认异常处理器

#### 8.2.3 异常消息

- `Message`: 消息接口
- `AbstractMessage`: 消息抽象基类
- `CommonMessages`: 通用错误消息
- `DicomMessages`: DICOM 错误消息
- `AnalysisMessages`: 分析错误消息
- `ReportMessages`: 报告错误消息

#### 8.2.4 异常恢复机制

- `RetryStrategy`: 重试策略类，提供重试功能
- `RetryStrategy.RunnableWithException`: 可能抛出异常的 Runnable 接口

### 8.3 异常处理流程

```
1. 方法抛出异常
2. ExceptionAspect捕获异常
3. ExceptionAspect根据HandleException注解获取处理信息
4. ExceptionAspect调用对应的ExceptionHandler处理异常
5. ExceptionHandler记录日志并显示错误消息
6. 如果需要，ExceptionHandler将异常转换为更具体的异常重新抛出
```

### 8.4 异常处理示例

#### 8.4.1 使用异常处理注解

```java
@HandleException(errorCode = ErrorCode.DICOM_LOAD_ERROR)
public DicomExam loadDicomFile(File file) throws QAToolException {
    try {
        // 加载DICOM文件的代码
        return dicomExam;
    } catch (IOException e) {
        throw new DicomException(DicomMessages.LOAD_ERROR.format(file.getName()), e);
    }
}
```

#### 8.4.2 使用重试机制

```java
// 使用重试策略处理可能暂时失败的操作
public DicomExam loadDicomFileWithRetry(File file) throws Exception {
    return RetryStrategy.retry(
        () -> dicomLoader.loadFile(file),
        "loadDicomFile",
        3,
        1000,
        e -> e instanceof IOException
    );
}

// 使用无返回值的重试操作
public void saveDicomFileWithRetry(DicomExam exam, File file) throws Exception {
    RetryStrategy.retryVoid(
        () -> dicomSaver.saveFile(exam, file),
        "saveDicomFile"
    );
}
```

## 9. 配置管理

### 9.1 概述

配置管理负责系统参数和分析标准的配置，支持从配置文件加载配置。

### 9.2 主要组件

#### 9.2.1 配置读取器

- `ConfigReader`: 配置读取器接口
- `PropertyConfigReader`: 属性文件配置读取器
- `XMLConfigReader`: XML 配置读取器

#### 9.2.2 配置模型

- `AppConfig`: 应用配置
- `DicomConfig`: DICOM 配置
- `AnalysisConfig`: 分析配置
- `ReportConfig`: 报告配置
- `UIConfig`: UI 配置
- `TableConfig`: 表格配置

### 9.3 配置文件

系统使用以下配置文件：

- `application.properties`: 应用程序配置
- `dicom.properties`: DICOM 配置
- `analysis.properties`: 分析配置
- `report.properties`: 报告配置
- `ui.properties`: UI 配置
- `table_columns.properties`: 表格列配置

### 9.4 配置加载流程

```
1. 系统启动
2. ConfigReader加载配置文件
3. ConfigReader解析配置项
4. ConfigReader创建配置对象
5. 各模块获取对应的配置对象
```

## 10. 部署设计

### 10.1 系统要求

- 操作系统：Windows 7/10/11, Linux
- JRE：Java 1.8 或更高版本
- 内存：最小 4GB，推荐 8GB
- 磁盘空间：最小 1GB，取决于 DICOM 数据量

### 10.2 部署结构

```
CT质量保证工具/
├── bin/                # 可执行文件
│   ├── startup.bat     # Windows启动脚本
│   └── startup.sh      # Linux启动脚本
├── lib/                # 依赖库
├── config/             # 配置文件
├── logs/               # 日志文件
└── docs/               # 文档
```

### 10.3 安装步骤

1. 解压安装包
2. 配置 config 目录下的配置文件
3. 运行 bin 目录下的启动脚本
4. 系统启动并显示主界面

## 11. 参考文档

- [设计概要](design-overview.md)
- [表格数据模块设计](module-design/table-data-module.md)
- [表格数据引擎设计](module-design/table-data-engine.md)
- [表格配置设计](module-design/table-config.md)
- [相位列实现设计](module-design/phase-column.md)
- [系统架构设计](architecture/system-architecture.md)
