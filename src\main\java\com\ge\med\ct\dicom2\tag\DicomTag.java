package com.ge.med.ct.dicom2.tag;

import com.ge.med.ct.dicom2.service.DicomTagService;
import com.ge.med.ct.exception.aspect.HandleException;
import com.ge.med.ct.exception.code.ErrorCode;
import com.ge.med.ct.exception.core.DicomException;
import com.ge.med.ct.exception.factory.ExceptionFactory;
import com.ge.med.ct.exception.message.DicomMessages;
import org.dcm4che3.data.VR;

/**
 * DICOM Tag class
 * Represents a tag and its value in a DICOM file
 * Designed as an immutable class to avoid accidental state modification
 * Refactored: Use DicomTagValueConverter for value type conversion, remove
 * static factory methods to DicomTagFactory
 */
public final class DicomTag {

    private final String tagId; // Tag hexadecimal ID
    private final String value; // Tag value (using final to ensure immutability)
    private final VR vr; // Value Representation type
    private final String name; // Tag name
    private final String description; // Tag description
    private final boolean isPrivate; // Whether it is a private tag

    /**
     * Constructor
     *
     * @param tagId       Tag ID, hexadecimal format, e.g. "(0010,0010)"
     * @param name        Tag name
     * @param value       Tag value
     * @param vr          Value Representation type
     * @param description Description (optional)
     * @param isPrivate   Whether it is a private tag
     * @throws DicomException If parameters are invalid
     */
    public DicomTag(String tagId, String name, String value, VR vr, String description, boolean isPrivate)
            throws DicomException {
        if (tagId == null || tagId.trim().isEmpty()) {
            throw ExceptionFactory.createDicomException(ErrorCode.TAG_PROCESSING, DicomMessages.TAG_INVALID, "tagId",
                    "null");
        }

        this.tagId = DicomTagService.normalizeTagId(tagId);
        this.name = (name != null && !name.isEmpty()) ? name : "Tag-" + this.tagId;
        this.value = (value != null) ? value : "";
        this.vr = vr != null ? vr : VR.UN;
        this.description = (description != null) ? description : "DICOM Tag " + this.name;
        this.isPrivate = isPrivate || DicomTagService.isPrivateTag(tagId);
    }

    public DicomTag(String tagId, String value, VR vr) throws DicomException {
        this(tagId, DicomTagService.getTagName(tagId), value, vr, null,
                DicomTagService.isPrivateTag(tagId));
    }

    public DicomTag(String tagId, String value) throws DicomException {
        this(tagId, DicomTagService.getTagName(tagId), value, DicomTagService.getTagVR(tagId), null,
                DicomTagService.isPrivateTag(tagId));
    }

    public DicomTag(String tagId) throws DicomException {
        this(tagId, DicomTagService.getTagName(tagId), "", DicomTagService.getTagVR(tagId), null,
                DicomTagService.isPrivateTag(tagId));
    }

    @HandleException(errorCode = ErrorCode.TAG_PROCESSING)
    public DicomTag withValue(String newValue) throws DicomException {
        return new DicomTag(this.tagId, this.name, newValue, this.vr, this.description, this.isPrivate);
    }

    public int getGroup() {
        return DicomTagValueConverter.getGroupFromTagId(this.tagId);
    }

    public int getElement() {
        return DicomTagValueConverter.getElementFromTagId(this.tagId);
    }

    public String getValueAsString() {
        return this.value;
    }

    @HandleException(errorCode = ErrorCode.TAG_PROCESSING)
    public Integer getValueAsInteger() {
        return DicomTagValueConverter.getIntegerValue(this.value);
    }

    public Float getValueAsFloat() {
        return DicomTagValueConverter.getFloatValue(this.value);
    }

    public Double getValueAsDouble() {
        return DicomTagValueConverter.getDoubleValue(this.value);
    }

    public byte[] getValueAsBytes() {
        return DicomTagValueConverter.getBytesValue(this.value, this.vr);
    }

    public Object getRawValue() {
        return DicomTagValueConverter.getRawValue(this.value, this.vr);
    }

    public String getTagId() {
        return tagId;
    }

    public String getName() {
        return name;
    }

    public VR getVr() {
        return vr;
    }

    public String getDescription() {
        return description;
    }

    public boolean isPrivate() {
        return isPrivate;
    }

    public String getCategory() {
        return DicomTagService.getTagCategory(this.tagId).name();
    }

    @Override
    public String toString() {
        return String.format("%s[%s]=\"%s\"", this.name, this.tagId, this.value);
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null || getClass() != obj.getClass())
            return false;

        DicomTag other = (DicomTag) obj;
        return this.tagId.equals(other.tagId) &&
                ((this.value == null && other.value == null) ||
                        (this.value != null && this.value.equals(other.value)));
    }

    @Override
    public int hashCode() {
        // Calculate hash code using tag ID and value
        final int prime = 31;
        int result = 1;
        result = prime * result + ((tagId == null) ? 0 : tagId.hashCode());
        result = prime * result + ((value == null) ? 0 : value.hashCode());
        return result;
    }
}