# 异常处理优化

## 1. 消息系统

### 1.1 Message 接口

`Message` 接口定义了消息的基本操作，是异常处理模块的核心组件之一。

```java
/**
 * 消息接口
 * 定义消息的基本操作
 */
public interface Message {
    /**
     * 获取消息键
     *
     * @return 消息键
     */
    String getKey();

    /**
     * 获取默认消息
     *
     * @return 默认消息
     */
    String getDefaultMessage();

    /**
     * 格式化消息
     *
     * @param args 消息参数
     * @return 格式化后的消息
     */
    Message format(Object... args);

    /**
     * 转换为字符串
     *
     * @return 消息字符串
     */
    String toStr();
}
```

### 1.2 AbstractMessage 类

`AbstractMessage` 类提供了 `Message` 接口的基本实现，简化了消息枚举的实现。

```java
/**
 * 消息抽象基类
 * 简化消息枚举的实现
 */
public abstract class AbstractMessage implements Message {
    private final String key;
    private final String defaultMessage;
    private String formattedMessage;

    protected AbstractMessage(String key, String defaultMessage) {
        this.key = key;
        this.defaultMessage = defaultMessage;
        this.formattedMessage = defaultMessage;
    }

    @Override
    public String getKey() {
        return key;
    }

    @Override
    public String getDefaultMessage() {
        return defaultMessage;
    }

    @Override
    public Message format(Object... args) {
        Message formattedMessage = MessageBuilder.of(this, args).build();
        return formattedMessage;
    }

    @Override
    public String toStr() {
        return formattedMessage != null ? formattedMessage : defaultMessage;
    }

    /**
     * 设置格式化后的消息
     * 由MessageBuilder调用
     *
     * @param formattedMessage 格式化后的消息
     */
    void setFormattedMessage(String formattedMessage) {
        this.formattedMessage = formattedMessage;
    }
}
```

### 1.3 消息枚举

消息枚举提供了预定义的消息，使用 `AbstractMessage` 作为委托实现 `Message` 接口。

```java
/**
 * 常见错误消息枚举
 * 提供系统中常见的错误消息定义
 */
public enum CommonMessages implements Message {
    // 通用错误
    UNEXPECTED_ERROR("common.unexpected", "发生未预期的错误: {0}"),
    PROCESSING_ERROR("common.processing", "处理错误: {0}"),
    SERVICE_UNAVAILABLE("common.service.unavailable", "服务不可用: {0}"),

    // 参数验证错误
    INVALID_PARAMETER("common.param.invalid", "无效的参数 {0}: {1}"),
    MISSING_PARAMETER("common.param.missing", "缺少必要参数: {0}"),
    INVALID_FORMAT("common.format.invalid", "无效的格式: {0}");

    private final AbstractMessage delegate;

    CommonMessages(String key, String defaultMessage) {
        this.delegate = new AbstractMessage(key, defaultMessage) {};
    }

    @Override
    public String getKey() {
        return delegate.getKey();
    }

    @Override
    public String getDefaultMessage() {
        return delegate.getDefaultMessage();
    }

    @Override
    public Message format(Object... args) {
        return delegate.format(args);
    }

    @Override
    public String toStr() {
        return delegate.toStr();
    }
}
```

### 1.4 MessageBuilder 类

`MessageBuilder` 类用于构建和格式化消息，支持国际化。

```java
/**
 * 消息构建器
 * 用于构建和格式化消息
 */
public class MessageBuilder {
    private static final String DEFAULT_BUNDLE_NAME = "messages";
    private static ResourceBundle bundle;

    static {
        try {
            bundle = ResourceBundle.getBundle(DEFAULT_BUNDLE_NAME, Locale.getDefault());
        } catch (MissingResourceException e) {
            // 如果找不到资源包，使用空的资源包
            bundle = null;
        }
    }

    private final Message message;
    private final Object[] args;

    /**
     * 创建消息构建器
     *
     * @param message 消息
     * @param args 消息参数
     */
    private MessageBuilder(Message message, Object... args) {
        this.message = message;
        this.args = args;
    }

    /**
     * 创建消息构建器
     *
     * @param message 消息
     * @param args 消息参数
     * @return 消息构建器
     */
    public static MessageBuilder of(Message message, Object... args) {
        return new MessageBuilder(message, args);
    }

    /**
     * 构建消息
     *
     * @return 构建的消息
     */
    public Message build() {
        if (message == null) {
            return new AbstractMessage("unknown", "Unknown message") {};
        }

        String template = getMessageTemplate(message.getKey(), message.getDefaultMessage());
        String formattedText = formatMessageText(template, args);

        // 如果消息是AbstractMessage的实例，设置格式化后的消息
        if (message instanceof AbstractMessage) {
            ((AbstractMessage) message).setFormattedMessage(formattedText);
        }

        return message;
    }

    /**
     * 获取消息模板
     *
     * @param key 消息键
     * @param defaultMessage 默认消息
     * @return 消息模板
     */
    private String getMessageTemplate(String key, String defaultMessage) {
        if (bundle == null) {
            return defaultMessage;
        }

        try {
            return bundle.getString(key);
        } catch (MissingResourceException e) {
            return defaultMessage;
        }
    }

    /**
     * 格式化消息文本
     *
     * @param template 消息模板
     * @param args 消息参数
     * @return 格式化后的消息文本
     */
    private String formatMessageText(String template, Object... args) {
        if (args == null || args.length == 0) {
            return template;
        }

        try {
            return MessageFormat.format(template, args);
        } catch (IllegalArgumentException e) {
            return template;
        }
    }
}
```

## 2. 注解式异常处理

### 2.1 HandleException 注解

`HandleException` 注解用于标记需要进行异常处理的方法或类。

```java
/**
 * 异常处理注解
 * 用于标记需要进行异常处理的方法或类
 */
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
public @interface HandleException {
    // 错误码
    ErrorCode errorCode() default ErrorCode.UNEXPECTED;

    // 是否记录异常
    boolean logException() default true;

    // 是否发布异常事件
    boolean publishEvent() default true;

    // 重试相关属性
    int maxRetries() default 0;
    long retryDelay() default 0;
    Class<? extends Throwable>[] retryFor() default {};
    Class<? extends Throwable>[] noRetryFor() default {};
}
```

### 2.2 ExceptionHandlingAspect 类

`ExceptionHandlingAspect` 类实现异常处理的 AOP 切面，支持方法级和类级注解。

```java
/**
 * 异常处理切面
 * 实现异常处理的AOP切面
 */
@Aspect
@Component
public class ExceptionHandlingAspect {
    private static final Logger LOGGER = Logger.getLogger(ExceptionHandlingAspect.class.getName());

    /**
     * 处理方法级注解
     */
    @Around("@annotation(com.ge.med.ct.exception.aspect.HandleException)")
    public Object handleException(ProceedingJoinPoint joinPoint) throws Throwable {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        HandleException annotation = method.getAnnotation(HandleException.class);

        // 获取重试配置
        int maxRetries = annotation != null ? annotation.maxRetries() : 0;
        long retryDelay = annotation != null ? annotation.retryDelay() : 0;

        // 如果不需要重试，直接执行
        if (maxRetries <= 0) {
            return executeWithExceptionHandling(joinPoint, annotation);
        }

        // 需要重试的情况
        return executeWithRetry(joinPoint, annotation, maxRetries, retryDelay);
    }

    /**
     * 处理类级注解
     */
    @Around("@within(com.ge.med.ct.exception.aspect.HandleException)")
    public Object handleClassException(ProceedingJoinPoint joinPoint) throws Throwable {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();

        // 获取方法上的注解
        HandleException methodAnnotation = method.getAnnotation(HandleException.class);

        // 如果方法上有注解，则使用方法上的注解
        if (methodAnnotation != null) {
            return handleException(joinPoint);
        }

        // 获取类上的注解
        HandleException classAnnotation = method.getDeclaringClass().getAnnotation(HandleException.class);

        // 使用类上的注解处理异常
        return executeWithExceptionHandling(joinPoint, classAnnotation);
    }

    /**
     * 使用重试机制执行
     */
    private Object executeWithRetry(ProceedingJoinPoint joinPoint, HandleException annotation,
                                   int maxRetries, long retryDelay) throws Throwable {
        int attempts = 0;
        Throwable lastException = null;
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();

        while (attempts <= maxRetries) {
            try {
                return executeWithExceptionHandling(joinPoint, annotation);
            } catch (Throwable ex) {
                lastException = ex;
                attempts++;

                // 如果已经达到最大重试次数，抛出异常
                if (attempts > maxRetries) {
                    throw ex;
                }

                // 检查是否应该重试
                if (!shouldRetry(ex, annotation)) {
                    throw ex;
                }

                // 记录重试信息
                LOGGER.log(Level.INFO, "Retrying method {0} ({1}/{2}) after exception: {3}",
                        new Object[]{method.getName(), attempts, maxRetries, ex.getMessage()});

                // 等待一段时间后重试
                if (retryDelay > 0) {
                    Thread.sleep(retryDelay);
                }
            }
        }

        // 不应该到达这里，但为了编译通过
        throw lastException;
    }

    /**
     * 检查是否应该重试
     */
    private boolean shouldRetry(Throwable ex, HandleException annotation) {
        Class<? extends Throwable>[] retryFor = annotation.retryFor();
        Class<? extends Throwable>[] noRetryFor = annotation.noRetryFor();

        // 如果异常类型在noRetryFor中，不重试
        for (Class<? extends Throwable> noRetryClass : noRetryFor) {
            if (noRetryClass.isInstance(ex)) {
                return false;
            }
        }

        // 如果retryFor为空，重试所有异常
        if (retryFor.length == 0) {
            return true;
        }

        // 如果异常类型在retryFor中，重试
        for (Class<? extends Throwable> retryClass : retryFor) {
            if (retryClass.isInstance(ex)) {
                return true;
            }
        }

        return false;
    }

    /**
     * 执行并处理异常
     */
    private Object executeWithExceptionHandling(ProceedingJoinPoint joinPoint, HandleException annotation) throws Throwable {
        try {
            return joinPoint.proceed();
        } catch (Throwable ex) {
            // 处理异常
            handleExceptionInternal(ex, annotation, joinPoint.getSignature().getName(), joinPoint.getArgs());
            throw ex;
        }
    }

    /**
     * 内部异常处理
     */
    private void handleExceptionInternal(Throwable ex, HandleException annotation, String methodName, Object[] args) {
        // 如果已经是QAToolException，则直接使用
        if (ex instanceof QAToolException) {
            QAToolException qaEx = (QAToolException) ex;

            // 记录异常
            if (annotation.logException()) {
                logException(qaEx, methodName, args);
            }

            // 发布异常事件
            if (annotation.publishEvent()) {
                publishExceptionEvent(qaEx);
            }

            return;
        }

        // 如果不是QAToolException，则包装为QAToolException
        ErrorCode errorCode = annotation.errorCode();
        Message messageKey = getDefaultMessageForErrorCode(errorCode);
        QAToolException qaEx = ExceptionFactory.createException(errorCode, messageKey, ex, methodName);

        // 记录异常
        if (annotation.logException()) {
            logException(qaEx, methodName, args);
        }

        // 发布异常事件
        if (annotation.publishEvent()) {
            publishExceptionEvent(qaEx);
        }
    }
}
```

## 3. 异常恢复机制

### 3.1 RetryStrategy 类

`RetryStrategy` 类提供重试功能，用于处理可能是暂时性的错误。

```java
/**
 * 重试策略
 * 提供重试机制，用于处理可能暂时失败的操作
 */
@HandleException(errorCode = ErrorCode.OPERATION)
public final class RetryStrategy {
    private static final Logger LOG = LogManager.getInstance().getLogger(RetryStrategy.class);

    private RetryStrategy() {
        throw new AssertionError("工具类不应被实例化");
    }

    /**
     * 使用默认参数执行重试操作
     *
     * @param <T>           返回值类型
     * @param operation     要执行的操作
     * @param operationName 操作名称（用于日志）
     * @return 操作结果
     * @throws Exception 如果所有重试都失败
     */
    public static <T> T retry(Callable<T> operation, String operationName) throws Exception {
        return retry(operation, operationName, 3, 1000, e -> true);
    }

    /**
     * 执行重试操作
     *
     * @param <T>           返回值类型
     * @param operation     要执行的操作
     * @param operationName 操作名称（用于日志）
     * @param maxAttempts   最大尝试次数
     * @param delayMs       重试间隔（毫秒）
     * @param retryOn       决定哪些异常应该重试的谓词
     * @return 操作结果
     * @throws Exception 如果所有重试都失败
     */
    public static <T> T retry(
            Callable<T> operation,
            String operationName,
            int maxAttempts,
            long delayMs,
            Predicate<Exception> retryOn) throws Exception {

        int attempts = 0;
        Exception lastException = null;

        while (attempts < maxAttempts) {
            try {
                attempts++;
                return operation.call();
            } catch (InterruptedException e) {
                // 对于中断异常，不重试，直接抛出
                Thread.currentThread().interrupt();
                throw e;
            } catch (Exception e) {
                lastException = e;

                if (!retryOn.test(e)) {
                    LOG.log(Level.WARNING, "操作 {0} 失败，异常不符合重试条件，直接抛出", operationName);
                    throw e;
                }

                if (attempts >= maxAttempts) {
                    LOG.log(Level.WARNING, "操作 {0} 在 {1} 次尝试后失败", new Object[] { operationName, maxAttempts });
                    throw e;
                }

                LOG.log(Level.INFO, "操作 {0} 失败，将在 {1}ms 后进行第 {2}/{3} 次重试: {4}",
                        new Object[] { operationName, delayMs, attempts + 1, maxAttempts, e.getMessage() });

                try {
                    Thread.sleep(delayMs);
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    throw ExceptionFactory.createException(ErrorCode.OPERATION,
                            CommonMessages.THREAD_INTERRUPTED, "重试等待被中断", ie);
                }
            }
        }

        // 这里通常不会到达，因为最后一次失败会在循环中抛出异常
        throw ExceptionFactory.createException(ErrorCode.OPERATION,
                CommonMessages.PROCESSING_ERROR, "所有重试尝试都失败: " + operationName, lastException);
    }

    /**
     * 执行不返回值的重试操作
     *
     * @param operation     要执行的操作
     * @param operationName 操作名称（用于日志）
     * @throws Exception 如果所有重试都失败
     */
    public static void retryVoid(RunnableWithException operation, String operationName) throws Exception {
        retry(() -> {
            operation.run();
            return null;
        }, operationName);
    }

    /**
     * 可能抛出异常的Runnable接口
     */
    @FunctionalInterface
    public interface RunnableWithException {
        void run() throws Exception;
    }
}
```

## 4. 异常类层次结构

### 4.1 QAToolException 类

`QAToolException` 是所有自定义异常的基类，提供了错误码管理、消息处理、上下文信息等功能。

```java
/**
 * 质量保证工具基础异常类
 * 所有自定义异常的基类
 */
public class QAToolException extends RuntimeException {
    private final ErrorCode errorCode;
    private final Message message;
    private final Map<String, Object> context;
    private final long timestamp;

    protected QAToolException(Builder<?> builder) {
        super(builder.cause);
        this.errorCode = builder.errorCode;
        this.message = builder.message;
        this.context = Collections.unmodifiableMap(new HashMap<>(builder.context));
        this.timestamp = System.currentTimeMillis();
    }

    /**
     * 获取错误码
     */
    public ErrorCode getErrorCode() {
        return errorCode;
    }

    /**
     * 获取消息
     */
    public Message getMessage() {
        return message;
    }

    /**
     * 获取上下文信息
     */
    public Map<String, Object> getContext() {
        return context;
    }

    /**
     * 获取时间戳
     */
    public long getTimestamp() {
        return timestamp;
    }

    /**
     * 创建异常构建器
     */
    public static Builder<?> builder() {
        return new Builder<>();
    }

    /**
     * 异常构建器
     */
    public static class Builder<T extends Builder<T>> {
        private ErrorCode errorCode = ErrorCode.UNEXPECTED;
        private Message message;
        private Throwable cause;
        private final Map<String, Object> context = new HashMap<>();

        /**
         * 设置错误码
         */
        public T errorCode(ErrorCode errorCode) {
            this.errorCode = errorCode;
            return self();
        }

        /**
         * 设置消息
         */
        public T message(Message message) {
            this.message = message;
            return self();
        }

        /**
         * 设置消息参数
         */
        public T messageArgs(Object... args) {
            if (this.message != null) {
                this.message = this.message.format(args);
            }
            return self();
        }

        /**
         * 设置原因
         */
        public T cause(Throwable cause) {
            this.cause = cause;
            return self();
        }

        /**
         * 添加上下文信息
         */
        public T context(String key, Object value) {
            this.context.put(key, value);
            return self();
        }

        /**
         * 构建异常
         */
        public QAToolException build() {
            return new QAToolException(this);
        }

        @SuppressWarnings("unchecked")
        protected T self() {
            return (T) this;
        }
    }
}
```

### 4.2 DicomException 类

`DicomException` 是 DICOM 相关异常的基类，继承自 `QAToolException`。

```java
/**
 * DICOM异常类
 * DICOM相关异常的基类
 */
public class DicomException extends QAToolException {
    private final String dicomFile;

    protected DicomException(Builder builder) {
        super(builder);
        this.dicomFile = builder.dicomFile;
    }

    /**
     * 获取DICOM文件路径
     */
    public String getDicomFile() {
        return dicomFile;
    }

    /**
     * 创建DICOM异常构建器
     */
    public static Builder builder() {
        return new Builder();
    }

    /**
     * DICOM异常构建器
     */
    public static class Builder extends QAToolException.Builder<Builder> {
        private String dicomFile;

        /**
         * 设置DICOM文件路径
         */
        public Builder dicomFile(String dicomFile) {
            this.dicomFile = dicomFile;
            return this;
        }

        /**
         * 构建DICOM异常
         */
        @Override
        public DicomException build() {
            return new DicomException(this);
        }
    }
}
```

## 5. 最佳实践

### 5.1 类级别与方法级别注解

#### 类级别注解适用场景

- 类中大多数方法需要相同的异常处理策略
- 希望简化代码，避免在每个方法上重复添加相同的注解

```java
@HandleException(errorCode = ErrorCode.PROCESSING)
public class DicomFileManager implements AutoCloseable {
    // 所有方法都会使用PROCESSING作为默认错误码
}
```

#### 方法级别注解适用场景

- 方法需要与类不同的异常处理策略
- 方法需要特定的错误码或重试策略

```java
@HandleException(errorCode = ErrorCode.PROCESSING)
public class DicomFileManager implements AutoCloseable {

    // 使用类级别的默认错误码
    public DicomFileModel readFile(String filePath) throws DicomException {
        // ...
    }

    // 覆盖类级别的错误码，使用特定的错误码
    @HandleException(errorCode = ErrorCode.DICOM_VALIDATION)
    public boolean validateDicomFile(String filePath) {
        // ...
    }

    // 使用特定的重试策略
    @HandleException(
        errorCode = ErrorCode.READ,
        maxRetries = 3,
        retryDelay = 1000
    )
    public DicomFileModel readFileWithRetry(String filePath) throws DicomException {
        // ...
    }
}
```

### 5.2 异常创建与抛出

#### 使用构建器模式

对于复杂的异常，使用构建器模式创建异常对象。

```java
throw DicomException.builder()
    .errorCode(ErrorCode.INVALID_FILE)
    .message(DicomMessages.FILE_INVALID)
    .messageArgs(filePath)
    .context("fileName", file.getName())
    .context("fileSize", file.length())
    .build();
```

#### 使用工厂方法

对于常见的异常，使用工厂方法创建异常对象。

```java
throw ExceptionFactory.createDicomException(
    ErrorCode.INVALID_FILE,
    DicomMessages.FILE_INVALID,
    filePath
);
```

### 5.3 异常恢复与重试

#### 使用注解配置重试

对于可能是暂时性的错误，如网络超时，使用注解配置重试策略。

```java
@HandleException(
    errorCode = ErrorCode.NETWORK_ERROR,
    maxRetries = 3,
    retryDelay = 1000,
    retryFor = {IOException.class, TimeoutException.class}
)
public Response sendRequest(Request request) {
    // 发送请求的代码
}
```

#### 使用 RetryStrategy 类

直接使用 RetryStrategy 类进行重试操作。

```java
// 使用有返回值的重试
DicomFile file = RetryStrategy.retry(
    () -> dicomReader.readFile(filePath),
    "readDicomFile",
    3,
    1000,
    e -> e instanceof IOException
);

// 使用无返回值的重试
RetryStrategy.retryVoid(
    () -> dicomWriter.writeFile(file, outputPath),
    "writeDicomFile"
);
```
