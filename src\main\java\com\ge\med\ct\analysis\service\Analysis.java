package com.ge.med.ct.analysis.service;

import com.ge.med.ct.exception.core.QAToolException;
import com.ge.med.ct.analysis.model.AnalysisParams;
import com.ge.med.ct.analysis.model.AnalysisState;
import com.ge.med.ct.analysis.model.AnalysisResult;
import com.ge.med.ct.service.CommandInvoker;

import java.io.File;
import java.util.Date;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executors;
import java.util.function.Consumer;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * 分析类
 * 提供统一的分析流程抽象，管理分析生命周期
 */
public class Analysis {
    private static final Logger LOGGER = Logger.getLogger(Analysis.class.getName());
    private final AnalysisContext context;
    
    /**
     * 创建分析实例
     * @param params 分析参数
     */
    public Analysis(AnalysisParams params) {
        Objects.requireNonNull(params, "分析参数不能为空");
        this.context = new AnalysisContext(
            params,
            AnalysisState.WAITING,
            Executors.newSingleThreadExecutor()
        );
    }
    
    /**
     * 获取分析参数
     * @return 分析参数对象
     */
    public AnalysisParams getParams() {
        return context.getParams();
    }
    
    /**
     * 获取当前分析状态
     * @return 分析状态枚举值
     */
    public AnalysisState getState() {
        return context.getState();
    }
    
    /**
     * 获取分析结果（如果已完成）
     * @return 分析结果对象，如果分析未完成则返回null
     */
    public AnalysisResult getResult() {
        return context.getResult();
    }
    
    /**
     * 添加前置处理器
     * @param hook 处理器函数
     * @return 分析对象本身，用于链式调用
     */
    public Analysis addPreProcessHook(Consumer<AnalysisContext> hook) {
        context.addPreProcessHook(hook);
        return this;
    }
    
    /**
     * 添加后置处理器
     * @param hook 处理器函数
     * @return 分析对象本身，用于链式调用
     */
    public Analysis addPostProcessHook(Consumer<AnalysisContext> hook) {
        context.addPostProcessHook(hook);
        return this;
    }
    
    /**
     * 异步执行分析
     * @param callback 状态回调接口，用于接收进度和状态更新
     * @return 包含分析结果的CompletableFuture对象
     */
    public CompletableFuture<AnalysisResult> executeAsync(AnalysisStatusCallback callback) {
        // 参数验证
        validateParams(callback);
        
        // 初始化上下文
        context.setStartTime(new Date());
        context.setState(AnalysisState.IN_PROGRESS);
        
        if (callback != null) {
            callback.onStateChange(AnalysisState.IN_PROGRESS);
            callback.onProgress(0, "开始分析...");
        }
        
        return CompletableFuture.supplyAsync(() -> {
            try {
                // 执行分析流程
                preProcess(callback);
                executeCommand(callback);
                postProcess(callback);
                
                // 创建结果并完成
                AnalysisResult result = createResult();
                completeAnalysis(result, callback);
                return result;
            } catch (Exception e) {
                return handleError(e, callback);
            }
        }, context.getExecutor());
    }
    
    /**
     * 同步执行分析
     * @param callback 状态回调接口
     * @return 分析结果
     * @throws Exception 如果分析过程中发生异常
     */
    public AnalysisResult execute(AnalysisStatusCallback callback) throws Exception {
        return executeAsync(callback).get();
    }
    
    /**
     * 验证分析参数
     */
    private void validateParams(AnalysisStatusCallback callback) {
        AnalysisParams params = context.getParams();
        
        if (context.getState() == AnalysisState.IN_PROGRESS) {
            throw new IllegalStateException("分析已经在进行中");
        }
        
        if (!params.isValid()) {
            String errorMessage = params.getValidationError();
            if (callback != null) {
                callback.onError(errorMessage, null);
            }
            throw new IllegalArgumentException(errorMessage);
        }
    }
    
    /**
     * 执行前置处理
     */
    private void preProcess(AnalysisStatusCallback callback) {
        if (callback != null) {
            callback.onProgress(10, "准备分析环境...");
        }
        
        // 执行前置处理钩子
        context.executePreProcessHooks();
        
        // 解析协议类型进行日志记录
        if (callback != null) {
            String protocolType = context.getParams().getProtocolType();
            String protocolName = context.getParams().getProtocolName();
            callback.onLog(AnalysisStatusCallback.LogLevel.INFO, 
                "使用协议: " + protocolType + (protocolName != null ? ", 名称: " + protocolName : ""));
        }
    }
    
    /**
     * 执行命令
     */
    private void executeCommand(AnalysisStatusCallback callback) throws QAToolException {
        AnalysisParams params = context.getParams();
        String command = params.toCommandString();
        
        if (callback != null) {
            callback.onLog(AnalysisStatusCallback.LogLevel.INFO, "执行命令: " + command);
            callback.onProgress(20, "命令执行中...");
        }
        
        // 执行命令
        String cmdOutput = CommandInvoker.execute(CommandInvoker.IAUI_SCRIPT, command);
        context.setCommandOutput(cmdOutput);
        
        // 处理输出
        if (callback != null && cmdOutput != null) {
            callback.onProgress(70, "处理输出结果...");
            processCommandOutput(cmdOutput, callback);
        }
    }
    
    /**
     * 处理命令输出，过滤并记录关键信息
     */
    private void processCommandOutput(String output, AnalysisStatusCallback callback) {
        if (output == null || output.isEmpty() || callback == null) {
            return;
        }
        
        int logCount = 0;
        for (String line : output.split("\\r?\\n")) {
            if (shouldDisplayLine(line)) {
                callback.onStatus(line);
                logCount++;
                
                // 限制日志行数，避免过多输出
                if (logCount >= 50) {
                    callback.onStatus("...(更多日志已省略)");
                    break;
                }
            }
        }
        
        if (logCount == 0) {
            callback.onStatus("命令执行完成，无有用输出");
        }
    }
    
    /**
     * 执行后置处理
     */
    private void postProcess(AnalysisStatusCallback callback) {
        if (callback != null) {
            callback.onProgress(80, "完成分析...");
        }
        
        // 检查输出文件
        AnalysisParams params = context.getParams();
        File outputFile = new File(params.getOutputPath());
        boolean outputFileExists = outputFile.exists();
        context.setOutputFileExists(outputFileExists);
        
        if (callback != null) {
            if (outputFileExists) {
                callback.onLog(AnalysisStatusCallback.LogLevel.INFO, 
                    "成功生成输出文件: " + params.getOutputPath());
            } else {
                callback.onLog(AnalysisStatusCallback.LogLevel.WARNING, 
                    "警告: 输出文件未生成: " + params.getOutputPath());
            }
        }
        
        // 确定分析状态
        AnalysisState resultState = determineAnalysisState(
            context.getCommandOutput(), 
            context.isOutputFileExists()
        );
        context.setState(resultState);
        
        // 执行后置处理钩子
        context.executePostProcessHooks();
    }
    
    /**
     * 完成分析，设置结果和通知回调
     */
    private void completeAnalysis(AnalysisResult result, AnalysisStatusCallback callback) {
        context.setEndTime(new Date());
        context.setResult(result);
        
        if (callback != null) {
            callback.onProgress(100, "分析完成");
            callback.onStateChange(result.getStatus());
            callback.onComplete(result);
        }
    }
    
    /**
     * 创建分析结果
     */
    private AnalysisResult createResult() {
        return new AnalysisResult(
            context.getParams(),
            context.getState(),
            getResultMessage(context.getState()),
            null // 图像路径为空
        );
    }
    
    /**
     * 处理错误
     */
    private AnalysisResult handleError(Exception e, AnalysisStatusCallback callback) {
        LOGGER.log(Level.SEVERE, "分析执行失败", e);
        context.setState(AnalysisState.FAIL);
        context.setEndTime(new Date());
        
        // 创建错误结果
        AnalysisResult result = new AnalysisResult(
            context.getParams(),
            AnalysisState.FAIL,
            "执行失败: " + e.getMessage(),
            null
        );
        
        context.setResult(result);
        
        if (callback != null) {
            callback.onError(e.getMessage(), e);
            callback.onComplete(result);
        }
        
        return result;
    }
    
    /**
     * 获取结果消息
     */
    private String getResultMessage(AnalysisState state) {
        switch (state) {
            case PASS:
                return "分析成功完成，结果报告已生成";
            case FAIL:
                return "分析失败，未能生成有效报告";
            case IN_PROGRESS:
                return "分析正在进行中";
            case WAITING:
                return "分析等待执行";
            default:
                return "未知状态";
        }
    }
    
    /**
     * 判断是否应该显示日志行
     */
    private boolean shouldDisplayLine(String line) {
        if (line == null || line.trim().isEmpty()) {
            return false;
        }
        
        // 忽略不重要的日志
        return !line.contains("DEBUG") && 
               !line.contains("Cleaning up") && 
               !line.contains("ProtocolMgr") &&
               !line.trim().startsWith("[IA]");
    }
    
    /**
     * 根据输出结果和文件存在性确定分析状态
     */
    private AnalysisState determineAnalysisState(String output, boolean fileExists) {
        if (!fileExists) {
            return AnalysisState.FAIL;
        }
        
        if (output != null && (output.contains("ERROR") || output.contains("错误"))) {
            return AnalysisState.FAIL;
        }
        
        return AnalysisState.PASS;
    }
} 