package com.ge.med.ct.analysis.service;

import com.ge.med.ct.dicom2.model.DicomExam;
import com.ge.med.ct.dicom2.model.DicomImage;
import com.ge.med.ct.dicom2.model.DicomSeries;
import com.ge.med.ct.dicom2.tag.DicomTagConstants;

import java.util.List;
import java.util.Map;
import java.util.HashMap;

/**
 * 协议服务
 * 提供从DICOM序列推断IA协议参数的功能
 */
public class ProtocolInferService {
    // 定义配置文件中常见的协议类型
    private static final String PROTOCOL_MTF = "MTF10_50";
    private static final String PROTOCOL_MTF4_50 = "MTF4_50";
    private static final String PROTOCOL_LCD = "LCD";
    private static final String PROTOCOL_MEANS = "MEANS";
    private static final String PROTOCOL_SERIES_MEANS = "SERIES_MEANS";
    private static final String PROTOCOL_BEAM_WIDTH = "BEAM_WIDTH";
    private static final String PROTOCOL_SSP = "SSP";
    private static final String PROTOCOL_CTDI = "CTDIw";
    private static final String PROTOCOL_QA1 = "QA1";
    private static final String PROTOCOL_QA2 = "QA2";
    private static final String PROTOCOL_QA3 = "QA3";
    private static final String PROTOCOL_STREAK = "STREAK";
    private static final String PROTOCOL_RING = "RING";
    private static final String PROTOCOL_CLUMP = "CLUMP";
    private static final String PROTOCOL_CENTER_SMUDGE = "CENTER_SMUDGE";
    private static final String PROTOCOL_LARGE_CENTER_SMUDGE = "LARGE_CENTER_SMUDGE";
    private static final String PROTOCOL_CENTER_ARTIFACT = "CENTER_ARTIFACT";
    private static final String PROTOCOL_CENTER_SPOT = "CENTER_SPOT";
    private static final String PROTOCOL_BAND = "BAND";
    private static final String PROTOCOL_GE_PERF1 = "GE_PERF1";
    private static final String PROTOCOL_GE_PERF2 = "GE_PERF2";
    private static final String PROTOCOL_VISIBLE_LINES = "VISIBLE_LINES";
    private static final String PROTOCOL_VISIBLE_HOLES = "VISIBLE_HOLES";
    private static final String PROTOCOL_P35_NOISE = "P35_NOISE";
    private static final String PROTOCOL_MANUAL_ROI = "MANUAL_ROI";
    private static final String PROTOCOL_ROW_COMB_SERIES_MEANS = "ROW_COMB_SERIES_MEANS";
    private static final String PROTOCOL_CENTRE_ARTIFACT = "CENTRE_ARTIFACT"; // 英式拼写变体
    
    // 定义常见的协议套件名称
    private static final String SUITE_IMGSER = "ImgSer20QA";
    private static final String SUITE_GRDRHOT = "GrDrHot20QA";
    
    /**
     * 从序列中推断协议参数
     * 返回格式化的协议字符串 (格式：协议字符串|协议名称|协议ID)
     * 
     * @param series DICOM序列
     * @param exam 相关检查
     * @param images 序列图像列表
     * @return 格式化的协议字符串
     */
    public String inferFromSeries(DicomSeries series, DicomExam exam, List<DicomImage> images) {
        if (series == null) {
            return "Unknown Protocol|Unknown|0";
        }
        
        String seriesNumber = getTagValueAsString(series, DicomTagConstants.Series.SERIES_NUMBER);
        // 2. 创建推断上下文
        InferenceContext context = createContext(series, exam, images);
        
        // 3. 推断三个参数
        String protocolString = inferProtocolString(context);
        String protocolName = inferProtocolName(context);
        String protocolId = seriesNumber != null && !seriesNumber.isEmpty() ? seriesNumber : "0";
        
        // 4. 返回格式化结果
        return protocolString + "|" + protocolName + "|" + protocolId;
    }
    
    /**
     * 创建推断上下文
     */
    private InferenceContext createContext(DicomSeries series, DicomExam exam, List<DicomImage> images) {
        InferenceContext context = new InferenceContext();
        
        // 设置序列描述
        String seriesDescription = getTagValueAsString(series, DicomTagConstants.Series.SERIES_DESCRIPTION);
        context.setSeriesDescription(seriesDescription);
        
        // 收集关键DICOM标签
        Map<String, Object> tags = new HashMap<>();
        if (series != null) {
            addTagValue(tags, series, DicomTagConstants.Series.SERIES_NUMBER);
            addTagValue(tags, series, DicomTagConstants.Series.MODALITY);
            addTagValue(tags, series, DicomTagConstants.CT.KVP);
            addTagValue(tags, series, DicomTagConstants.Series.BODY_PART_EXAMINED);
        }
        
        if (images != null && !images.isEmpty()) {
            addTagValue(tags, images.get(0), DicomTagConstants.Image.SLICE_THICKNESS);
            addTagValue(tags, images.get(0), DicomTagConstants.CT.RECONSTRUCTION_DIAMETER);
        }
        
        context.setDicomTags(tags);
        
        // 获取机器型号信息用于配置文件变体
        if (exam != null) {
            String modelName = getTagValueAsString(exam, DicomTagConstants.Equipment.MANUFACTURER_MODEL_NAME);
            if (modelName != null) {
                // 简化的配置变体推断 - 实际系统可能更复杂
                String configVariant = inferConfigVariant(modelName);
                context.setConfigVariant(configVariant);
            }
        }
        
        return context;
    }
    
    /**
     * 推断配置文件变体
     */
    private String inferConfigVariant(String modelName) {
        if (modelName == null) return null;
        
        String model = modelName.toLowerCase();
        
        // 简化的映射，实际系统中可能更复杂
        if (model.contains("revolution") && model.contains("evo")) return "revo_evo";
        if (model.contains("vct") && model.contains("hd")) return "vcthd64";
        if (model.contains("vct") && model.contains("64")) return "vct64";
        if (model.contains("vct") && model.contains("32")) return "vct32";
        if (model.contains("lightspeed") && model.contains("16")) return "hl16";
        
        // 默认配置
        return "vct64";
    }
    
    /**
     * 推断协议字符串
     */
    private String inferProtocolString(InferenceContext context) {
        String description = context.getUpperSeriesDescription();
        if (description == null || description.isEmpty()) {
            return PROTOCOL_MTF;
        }
        
        // 确保大写比较，不区分大小写
        String upperDesc = description.toUpperCase();
        
        // 优先通过序列描述进行精确匹配
        // LCD 相关
        if (upperDesc.contains("LCD")) return PROTOCOL_LCD;
        
        // MTF 相关
        if (upperDesc.contains("MTF")) {
            if (upperDesc.contains("MTF4") || upperDesc.contains("MTF 4")) return PROTOCOL_MTF4_50;
            return PROTOCOL_MTF;
        }
        
        // QA 相关
        if (upperDesc.contains("QA3")) return PROTOCOL_QA3;
        if (upperDesc.contains("QA2")) return PROTOCOL_QA2;
        if (upperDesc.contains("QA1")) return PROTOCOL_QA1;
        
        // MEANS 相关
        if (upperDesc.contains("MEANS") || upperDesc.contains("NOISE") || 
            upperDesc.contains("UNIF")) {
            if (upperDesc.contains("SERIES")) return PROTOCOL_SERIES_MEANS;
            return PROTOCOL_MEANS;
        }
        
        // CENTER/CENTRE 相关
        if (upperDesc.contains("CENTER") || upperDesc.contains("CENTRE")) {
            if (upperDesc.contains("SMUDGE")) {
                if (upperDesc.contains("LARGE")) return PROTOCOL_LARGE_CENTER_SMUDGE;
                return PROTOCOL_CENTER_SMUDGE;
            }
            if (upperDesc.contains("ARTIFACT")) {
                // 处理英式和美式拼写
                if (upperDesc.contains("CENTRE")) return PROTOCOL_CENTRE_ARTIFACT;
                return PROTOCOL_CENTER_ARTIFACT;
            }
            if (upperDesc.contains("SPOT")) return PROTOCOL_CENTER_SPOT;
            // 默认CENTER
            return PROTOCOL_CENTER_SMUDGE;
        }
        
        // GE 性能测试相关
        if (upperDesc.contains("GE") && upperDesc.contains("PERF")) {
            if (upperDesc.contains("1") || upperDesc.contains("PERF1")) return PROTOCOL_GE_PERF1;
            if (upperDesc.contains("2") || upperDesc.contains("PERF2")) return PROTOCOL_GE_PERF2;
            // 默认PERF1
            return PROTOCOL_GE_PERF1;
        }
        
        // 其他明确类型
        if (upperDesc.contains("STREAK")) return PROTOCOL_STREAK;
        if (upperDesc.contains("RING")) return PROTOCOL_RING;
        if (upperDesc.contains("CLUMP")) return PROTOCOL_CLUMP;
        if (upperDesc.contains("BAND")) return PROTOCOL_BAND;
        if (upperDesc.contains("BEAM") || upperDesc.contains("WIDTH")) return PROTOCOL_BEAM_WIDTH;
        if (upperDesc.contains("SSP") || upperDesc.contains("PROFILE")) return PROTOCOL_SSP;
        if (upperDesc.contains("DOSE") || upperDesc.contains("CTDI")) return PROTOCOL_CTDI;
        if (upperDesc.contains("VISIBLE")) {
            if (upperDesc.contains("LINE")) return PROTOCOL_VISIBLE_LINES;
            if (upperDesc.contains("HOLE")) return PROTOCOL_VISIBLE_HOLES;
        }
        if (upperDesc.contains("P35") && upperDesc.contains("NOISE")) return PROTOCOL_P35_NOISE;
        if (upperDesc.contains("MANUAL") && upperDesc.contains("ROI")) return PROTOCOL_MANUAL_ROI;
        if (upperDesc.contains("ROW") && upperDesc.contains("COMB") && 
            upperDesc.contains("SERIES") && upperDesc.contains("MEANS")) return PROTOCOL_ROW_COMB_SERIES_MEANS;
        
        // 次优先从技术参数推断
        Object kvObj = context.getDicomTag(DicomTagConstants.CT.KVP);
        Object thicknessObj = context.getDicomTag(DicomTagConstants.Image.SLICE_THICKNESS);
        
        if (kvObj != null) {
            try {
                float kv = Float.parseFloat(kvObj.toString());
                if (kv <= 80) return PROTOCOL_LCD;
                if (kv >= 140) return PROTOCOL_MEANS;
            } catch (NumberFormatException ignored) {}
        }
        
        if (thicknessObj != null) {
            try {
                float thickness = Float.parseFloat(thicknessObj.toString());
                if (thickness <= 0.625f) return PROTOCOL_MTF;
                if (thickness >= 10.0f) return PROTOCOL_BEAM_WIDTH;
                if (thickness <= 1.25f) return PROTOCOL_QA3;
                if (thickness >= 5.0f) return PROTOCOL_MEANS;
            } catch (NumberFormatException ignored) {}
        }
        
        // 默认返回最常用的协议
        return PROTOCOL_MTF;
    }
    
    /**
     * 推断协议名称（测试套件）
     */
    private String inferProtocolName(InferenceContext context) {
        Object bodyPart = context.getDicomTag(DicomTagConstants.Series.BODY_PART_EXAMINED);
        Object seriesNumber = context.getDicomTag(DicomTagConstants.Series.SERIES_NUMBER);
        
        // 基于身体部位推断
        if (bodyPart != null) {
            String part = bodyPart.toString().toUpperCase();
            if (part.contains("HEAD") || part.contains("BRAIN")) {
                return SUITE_IMGSER;
            }
            if (part.contains("BODY") || part.contains("ABDOMEN") || part.contains("CHEST")) {
                return SUITE_GRDRHOT;
            }
        }
        
        // 基于序列号推断
        if (seriesNumber != null) {
            try {
                int num = Integer.parseInt(seriesNumber.toString());
                if (num % 2 == 0) {
                    return SUITE_IMGSER;
                } else {
                    return SUITE_GRDRHOT;
                }
            } catch (NumberFormatException ignored) {}
        }
        
        // 默认返回
        return SUITE_IMGSER;
    }
    
    /**
     * 安全获取标签值的字符串表示
     */
    private String getTagValueAsString(Object dicomObject, String tagName) {
        if (dicomObject == null) return "";
        
        Object value = null;
        if (dicomObject instanceof DicomSeries) {
            value = ((DicomSeries) dicomObject).getTagValue(tagName);
        } else if (dicomObject instanceof DicomExam) {
            value = ((DicomExam) dicomObject).getTagValue(tagName);
        } else if (dicomObject instanceof DicomImage) {
            value = ((DicomImage) dicomObject).getTagValue(tagName);
        }
        
        return value != null ? value.toString() : "";
    }
    
    /**
     * 添加标签值到映射
     */
    private void addTagValue(Map<String, Object> tags, Object dicomObject, String tagName) {
        if (dicomObject == null || tagName == null) return;
        
        Object value = null;
        if (dicomObject instanceof DicomSeries) {
            value = ((DicomSeries) dicomObject).getTagValue(tagName);
        } else if (dicomObject instanceof DicomExam) {
            value = ((DicomExam) dicomObject).getTagValue(tagName);
        } else if (dicomObject instanceof DicomImage) {
            value = ((DicomImage) dicomObject).getTagValue(tagName);
        }
        
        if (value != null) {
            tags.put(tagName, value);
        }
    }
} 