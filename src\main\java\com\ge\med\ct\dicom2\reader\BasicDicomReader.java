package com.ge.med.ct.dicom2.reader;

import com.ge.med.ct.dicom2.model.DicomFileModel;
import com.ge.med.ct.dicom2.service.DicomFileValidator;
import com.ge.med.ct.exception.aspect.HandleException;
import com.ge.med.ct.exception.code.ErrorCode;
import com.ge.med.ct.exception.core.DicomException;
import com.ge.med.ct.exception.factory.ExceptionFactory;
import com.ge.med.ct.exception.message.DicomMessages;
import org.dcm4che3.data.Attributes;
import org.dcm4che3.io.DicomInputStream;

import java.io.File;
import java.util.logging.Logger;

// 基础DICOM文件读取器
public class BasicDicomReader {
    protected static final Logger LOG = Logger.getLogger(BasicDicomReader.class.getName());

    @HandleException(errorCode = ErrorCode.READ)
    public Attributes readBasicAttributes(String filePath) throws DicomException {
        if (!DicomFileValidator.isDicomFile(filePath)) {
            throw ExceptionFactory.createDicomException(ErrorCode.DICOM_VALIDATION, DicomMessages.FILE_INVALID,
                    filePath);
        }

        try {
            File file = new File(filePath);
            try (DicomInputStream dis = new DicomInputStream(file)) {
                return dis.readDataset();
            }
        } catch (DicomException e) {
            // 直接重新抛出DicomException
            throw e;
        } catch (Exception e) {
            throw ExceptionFactory.createDicomException(ErrorCode.READ, DicomMessages.FILE_READ_ERROR, e,
                    filePath);
        }
    }

    @HandleException(errorCode = ErrorCode.PROCESSING)
    protected DicomFileModel createBasicModel(String filePath) throws DicomException {
        File file = new File(filePath);
        try {
            DicomFileModel model = new DicomFileModel(file.getName());
            model.setFilePath(filePath);
            model.setFileName(file.getName());
            model.setFileSize(file.length());
            return model;
        } catch (DicomException e) {
            // 直接重新抛出DicomException
            throw e;
        } catch (Exception e) {
            throw ExceptionFactory.createDicomException(ErrorCode.PROCESSING,
                    DicomMessages.PROCESSING_ERROR, e, file.getName(), e.getMessage());
        }
    }
}