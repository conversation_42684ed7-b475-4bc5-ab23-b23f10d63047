package com.ge.med.ct.laf2.components;

import com.ge.med.ct.laf2.theming.ThemeConstants;

import javax.swing.*;
import javax.swing.plaf.basic.BasicScrollBarUI;
import java.awt.*;

/**
 * 自定义滚动条UI，与CT工作站风格一致
 */
public class CTScrollBarUI extends BasicScrollBarUI {

    private static final int THUMB_SIZE = 10;
    private static final int TRACK_ALPHA = 0; // 完全透明的轨道

    // 隐藏按钮
    @Override
    protected JButton createDecreaseButton(int orientation) {
        return createZeroButton();
    }

    @Override
    protected JButton createIncreaseButton(int orientation) {
        return createZeroButton();
    }

    // 创建隐藏按钮
    private JButton createZeroButton() {
        JButton button = new JButton();
        button.setPreferredSize(new Dimension(0, 0));
        button.setMinimumSize(new Dimension(0, 0));
        button.setMaximumSize(new Dimension(0, 0));
        return button;
    }

    // 绘制轨道
    @Override
    protected void paintTrack(Graphics g, JComponent c, Rectangle trackBounds) {
        Graphics2D g2 = (Graphics2D) g.create();
        g2.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);

        // 设置透明度
        g2.setComposite(AlphaComposite.getInstance(AlphaComposite.SRC_OVER, TRACK_ALPHA / 100.0f));

        // 使用面板背景色
        g2.setColor(ThemeConstants.Colors.getPanelBackground());
        g2.fillRect(trackBounds.x, trackBounds.y, trackBounds.width, trackBounds.height);

        g2.dispose();
    }

    // 绘制滑块
    @Override
    protected void paintThumb(Graphics g, JComponent c, Rectangle thumbBounds) {
        if (thumbBounds.isEmpty() || !scrollbar.isEnabled()) {
            return;
        }

        Graphics2D g2 = (Graphics2D) g.create();
        g2.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);

        // 设置滑块颜色为深蓝灰色
        g2.setColor(ThemeConstants.Colors.getSecondary());

        // 计算滑块大小
        int x = thumbBounds.x + (scrollbar.getOrientation() == JScrollBar.HORIZONTAL ? 0 : (thumbBounds.width - THUMB_SIZE) / 2);
        int y = thumbBounds.y + (scrollbar.getOrientation() == JScrollBar.HORIZONTAL ? (thumbBounds.height - THUMB_SIZE) / 2 : 0);
        int width = scrollbar.getOrientation() == JScrollBar.HORIZONTAL ? thumbBounds.width : THUMB_SIZE;
        int height = scrollbar.getOrientation() == JScrollBar.HORIZONTAL ? THUMB_SIZE : thumbBounds.height;

        // 绘制圆角矩形
        g2.fillRoundRect(x, y, width, height, 5, 5);

        g2.dispose();
    }

    // 设置滑块的尺寸
    @Override
    protected Dimension getMinimumThumbSize() {
        return new Dimension(THUMB_SIZE, THUMB_SIZE * 2);
    }
}