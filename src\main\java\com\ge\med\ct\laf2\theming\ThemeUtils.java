package com.ge.med.ct.laf2.theming;

import java.awt.Color;
import java.awt.Font;

/**
 * 主题工具类
 * 提供颜色和字体工厂方法
 */
public class ThemeUtils {
    
    /**
     * 创建带透明度的颜色
     * @param baseColor 基础颜色
     * @param alpha 透明度 (0-255)
     * @return 派生颜色
     */
    public static Color withAlpha(Color baseColor, int alpha) {
        return new Color(baseColor.getRed(), baseColor.getGreen(), baseColor.getBlue(), alpha);
    }
    
    /**
     * 创建更亮的颜色
     * @param baseColor 基础颜色
     * @param factor 亮度因子 (0.0-1.0)
     * @return 亮色版本
     */
    public static Color brighter(Color baseColor, float factor) {
        return new Color(
            Math.min(255, (int)(baseColor.getRed() + (255 - baseColor.getRed()) * factor)),
            Math.min(255, (int)(baseColor.getGreen() + (255 - baseColor.getGreen()) * factor)),
            Math.min(255, (int)(baseColor.getBlue() + (255 - baseColor.getBlue()) * factor))
        );
    }
    
    /**
     * 创建更暗的颜色
     * @param baseColor 基础颜色
     * @param factor 暗度因子 (0.0-1.0)
     * @return 暗色版本
     */
    public static Color darker(Color baseColor, float factor) {
        return new Color(
            (int)(baseColor.getRed() * (1 - factor)),
            (int)(baseColor.getGreen() * (1 - factor)),
            (int)(baseColor.getBlue() * (1 - factor))
        );
    }
    
    /**
     * 创建派生字体
     * @param baseFont 基础字体
     * @param style 字体样式
     * @param sizeDelta 大小变化量
     * @return 派生字体
     */
    public static Font deriveFont(Font baseFont, int style, int sizeDelta) {
        return baseFont.deriveFont(style, baseFont.getSize() + sizeDelta);
    }
    
    /**
     * 创建派生字体
     * @param baseFont 基础字体
     * @param style 字体样式
     * @return 派生字体
     */
    public static Font deriveFont(Font baseFont, int style) {
        return baseFont.deriveFont(style);
    }
    
    /**
     * 创建派生字体
     * @param baseFont 基础字体
     * @param size 字体大小
     * @return 派生字体
     */
    public static Font deriveFont(Font baseFont, float size) {
        return baseFont.deriveFont(size);
    }
    
    private ThemeUtils() {
        // 防止实例化
    }
}
