package com.ge.med.ct.dicom2.model;

import com.ge.med.ct.dicom2.service.DicomTagService;
import com.ge.med.ct.dicom2.tag.DicomTag;
import com.ge.med.ct.exception.aspect.HandleException;
import com.ge.med.ct.exception.code.ErrorCode;
import com.ge.med.ct.exception.core.DicomException;
import com.ge.med.ct.exception.factory.ExceptionFactory;
import com.ge.med.ct.exception.message.DicomMessages;

import java.util.*;
import java.util.logging.Logger;

// DICOM检查类
public class DicomExam {
    private static final Logger LOG = Logger.getLogger(DicomExam.class.getName());

    private final String id;
    private final Map<String, DicomTag> tags;
    private final List<DicomSeries> series;
    private String patientID;
    private String patientName;
    private String studyDate;
    private String studyTime;

    public DicomExam(String id) throws DicomException {
        if (id == null || id.trim().isEmpty()) {
            throw ExceptionFactory.createDicomException(ErrorCode.DICOM_VALIDATION, DicomMessages.ID_EMPTY);
        }
        this.id = id;
        this.tags = new HashMap<>();
        this.series = new ArrayList<>();
    }

    public String getId() {
        return id;
    }

    public DicomTag getTag(String tagId) {
        return tags.get(tagId);
    }

    public void addTag(String tagId, DicomTag tag) {
        if (tagId != null && tag != null) {
            tags.put(tagId, tag);
            LOG.fine("添加标签 " + tagId + " 到检查 " + id);
        }
    }

    public String getTagValue(String tagId) {
        DicomTag tag = getTag(tagId);
        return tag != null ? tag.getValueAsString() : null;
    }

    public String getStudyInstanceUID() {
        return id;
    }

    public String getPatientID() {
        return patientID;
    }

    @HandleException(errorCode = ErrorCode.DICOM_VALIDATION)
    public void setPatientID(String patientID) throws DicomException {
        if (patientID == null) {
            throw ExceptionFactory.createDicomException(ErrorCode.DICOM_VALIDATION,
                    DicomMessages.EMPTY_PATIENT_ID);
        }
        this.patientID = patientID;
    }

    public String getPatientName() {
        return patientName;
    }

    @HandleException(errorCode = ErrorCode.DICOM_VALIDATION)
    public void setPatientName(String patientName) throws DicomException {
        if (patientName == null) {
            throw ExceptionFactory.createDicomException(ErrorCode.DICOM_VALIDATION,
                    DicomMessages.EMPTY_PATIENT_NAME);
        }
        this.patientName = patientName;
    }

    public String getStudyDate() {
        return studyDate;
    }

    public void setStudyDate(String studyDate) {
        if (studyDate == null) {
            throw new IllegalArgumentException("检查日期不能为空");
        }
        this.studyDate = studyDate;
    }

    public String getStudyTime() {
        return studyTime;
    }

    public void setStudyTime(String studyTime) {
        if (studyTime == null) {
            throw new IllegalArgumentException("检查时间不能为空");
        }
        this.studyTime = studyTime;
    }

    @HandleException(errorCode = ErrorCode.TAG_PROCESSING)
    private void setTagValue(String tagId, String value) throws DicomException {
        DicomTag tag = tags.get(tagId);
        if (tag == null) {
            tag = new DicomTag(tagId, DicomTagService.getTagName(tagId), value,
                    DicomTagService.getTagVR(tagId), "动态标签 " + tagId, false);
            tags.put(tagId, tag);
        } else {
            tag = tag.withValue(value);
            tags.put(tagId, tag);
        }
        LOG.fine("设置标签 " + tagId + " 值为 " + value + " 对于检查 " + id);
    }

    public List<DicomSeries> getSeries() {
        return Collections.unmodifiableList(series);
    }

    @HandleException(errorCode = ErrorCode.DICOM_SERIES_PROCESSING)
    public void setSeries(List<DicomSeries> series) throws DicomException {
        if (series == null) {
            throw ExceptionFactory.createDicomException(ErrorCode.DICOM_VALIDATION,
                    DicomMessages.VALIDATION_ERROR, "序列列表不能为空");
        }

        // 移除所有现有序列
        for (DicomSeries s : new ArrayList<>(this.series)) {
            removeSeries(s);
        }

        // 添加所有新序列
        for (DicomSeries s : series) {
            addSeries(s);
        }
    }

    @HandleException(errorCode = ErrorCode.DICOM_SERIES_PROCESSING)
    public void addSeries(DicomSeries series) throws DicomException {
        if (series != null && !this.series.contains(series)) {
            this.series.add(series);
            if (series.getExam() != this) {
                series.setExam(this);
            }
            LOG.fine("添加序列 " + series.getId() + " 到检查 " + id);
        }
    }

    @HandleException(errorCode = ErrorCode.DICOM_SERIES_PROCESSING)
    public void removeSeries(DicomSeries series) throws DicomException {
        if (series != null && this.series.contains(series)) {
            this.series.remove(series);
            if (series.getExam() == this) {
                series.setExam(null);
            }
            LOG.fine("移除序列 " + series.getId() + " 从检查 " + id);
        }
    }

    @Override
    public String toString() {
        return String.format(
                "%s[id=%s, studyInstanceUID=%s, patientID=%s, patientName=%s, studyDate=%s, studyTime=%s, seriesCount=%d]",
                getClass().getSimpleName(), id, getStudyInstanceUID(), getPatientID(), getPatientName(),
                getStudyDate(), getStudyTime(), series.size());
    }

    @Override
    public boolean equals(Object o) {
        if (this == o)
            return true;
        if (o == null || getClass() != o.getClass())
            return false;
        DicomExam that = (DicomExam) o;
        return id.equals(that.id);
    }

    @Override
    public int hashCode() {
        return id.hashCode();
    }
}