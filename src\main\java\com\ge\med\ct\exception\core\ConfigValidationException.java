package com.ge.med.ct.exception.core;

import com.ge.med.ct.exception.code.ErrorCode;
import com.ge.med.ct.exception.message.Message;

/**
 * 配置验证异常类
 * 用于处理配置验证过程中的错误
 */
public class ConfigValidationException extends QAToolException {

    private ConfigValidationException(Builder builder) {
        super(builder);
    }

    /**
     * 创建异常构建器
     *
     * @return 异常构建器
     */
    public static Builder builder() {
        return new Builder();
    }

    /**
     * 配置验证异常构建器类
     */
    public static class Builder extends QAToolException.Builder {

        @Override
        public Builder message(Message message) {
            super.message(message);
            return this;
        }

        @Override
        public Builder errorCode(ErrorCode errorCode) {
            super.errorCode(errorCode);
            return this;
        }

        @Override
        public Builder cause(Throwable cause) {
            super.cause(cause);
            return this;
        }

        @Override
        public Builder context(String key, Object value) {
            super.context(key, value);
            return this;
        }

        @Override
        public ConfigValidationException build() {
            return new ConfigValidationException(this);
        }
    }
}