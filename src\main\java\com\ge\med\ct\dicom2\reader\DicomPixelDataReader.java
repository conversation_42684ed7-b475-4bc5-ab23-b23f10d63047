package com.ge.med.ct.dicom2.reader;

import com.ge.med.ct.dicom2.model.DicomFileModel;
import com.ge.med.ct.dicom2.model.DicomImage;
import com.ge.med.ct.exception.aspect.HandleException;
import com.ge.med.ct.exception.code.ErrorCode;
import com.ge.med.ct.exception.core.DicomException;
import com.ge.med.ct.exception.factory.ExceptionFactory;
import com.ge.med.ct.exception.message.DicomMessages;
import org.dcm4che3.data.Attributes;
import org.dcm4che3.data.Tag;

/**
 * DICOM像素数据读取器
 * 负责DICOM文件的像素数据读取和验证
 */
public class DicomPixelDataReader extends BasicDicomReader {
    /**
     * 读取DICOM文件的像素数据
     *
     * @param filePath 文件路径
     * @return DicomFileModel实例
     * @throws DicomException 如果读取失败
     */
    @HandleException(errorCode = ErrorCode.DICOM_IMAGE_PROCESSING)
    public DicomFileModel readPixelData(String filePath) throws DicomException {
        // 读取基本属性
        Attributes attributes = readBasicAttributes(filePath);
        DicomFileModel model = createBasicModel(filePath);

        try {
            // 读取和验证像素数据
            validateAndSetPixelData(model, attributes);
            return model;
        } catch (DicomException e) {
            // 直接重新抛出DicomException
            throw e;
        } catch (Exception e) {
            throw ExceptionFactory.createDicomException(ErrorCode.DICOM_IMAGE_PROCESSING,
                    DicomMessages.IMAGE_PROCESSING_ERROR, e, e.getMessage());
        }
    }

    /**
     * 验证并设置像素数据
     *
     * @param model      DICOM文件模型
     * @param attributes DICOM属性
     * @throws DicomException 如果验证或设置失败
     */
    @HandleException(errorCode = ErrorCode.DICOM_IMAGE_PROCESSING)
    private void validateAndSetPixelData(DicomFileModel model, Attributes attributes) throws DicomException {
        try {
            DicomImage image = model.getImage();
            if (image == null) {
                throw ExceptionFactory.createDicomException(ErrorCode.DICOM_VALIDATION, DicomMessages.MODEL_NULL);
            }

            // 读取像素数据
            byte[] pixelData = attributes.getBytes(Tag.PixelData);
            if (pixelData == null || pixelData.length == 0) {
                throw ExceptionFactory.createDicomException(ErrorCode.DICOM_VALIDATION,
                        DicomMessages.PIXEL_DATA_EMPTY);
            }

            // 获取图像属性
            int rows = attributes.getInt(Tag.Rows, 0);
            int columns = attributes.getInt(Tag.Columns, 0);
            int bitsAllocated = attributes.getInt(Tag.BitsAllocated, 16);
            int samplesPerPixel = attributes.getInt(Tag.SamplesPerPixel, 1);

            // 验证图像尺寸
            if (rows <= 0 || columns <= 0) {
                throw ExceptionFactory.createDicomException(ErrorCode.DICOM_VALIDATION,
                        DicomMessages.INVALID_DIMENSION);
            }

            // 验证像素数据大小
            int expectedPixelDataSize = rows * columns * (bitsAllocated / 8) * samplesPerPixel;
            if (pixelData.length != expectedPixelDataSize) {
                throw ExceptionFactory.createDicomException(ErrorCode.DICOM_VALIDATION,
                        DicomMessages.INVALID_IMAGE_PARAMS, rows, columns, bitsAllocated, pixelData.length,
                        expectedPixelDataSize);
            }

            // 设置像素数据
            model.addTag(String.valueOf(Tag.PixelData), pixelData);

        } catch (DicomException e) {
            // 直接重新抛出DicomException
            throw e;
        } catch (Exception e) {
            throw ExceptionFactory.createDicomException(ErrorCode.DICOM_IMAGE_PROCESSING,
                    DicomMessages.IMAGE_PROCESSING_ERROR, e, e.getMessage());
        }
    }
}