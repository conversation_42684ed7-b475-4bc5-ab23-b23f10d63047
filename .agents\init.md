# 项目介绍
- 项目是质量保证工具(Quality Assurance Tool)，用于DICOM医学影像质量保证，主要组件包括DICOM数据服务、文件管理器、数据提供者、分析视图和异常处理机制。

# 沟通规则
1. 使用中文沟通
2. 设计小而美
3. 直接无套话
4. 不擅自更改设计
5. 对话坚持客观事实

# 技术要求
- 遵循 Java 1.8 兼容性要求
- 使用 exception 目录下的异常处理机制
- 采用 MVP 架构进行 UI 开发
- 异常创建优先使用 ExceptionFactory
- 异常处理优先使用 AOP 的 HandleException 方式
- UI 开发必须使用 Swing 框架

# 核心模块
- `dicom2`: DICOM数据处理核心功能
- `laf2`: 用户界面组件
- `service`: 通用服务
- `exception`: 异常处理机制
- `cfg`: 配置管理

# 设计原则
- 简洁设计/功能必要即可，切记大而全功能
- 大而无当是设计的敌人
- 达到编码/设计优美
- 实用为上
- 测试验证
- 注释要精简，一目了然的方法、字段等无需注释
- 文件操作时记录报错命令和最佳正确命令，避免重复错误

# .agents 目录结构及文件说明

## 目录结构
- `.agents/core/` - 核心配置和记忆文件目录
- `.agents/sessions/` - 会话记录存储目录
- `.agents/docs/` - 项目文档目录
- `.agents/init.md` - 会话初始化文件（当前文件）

## 核心文件说明
- `config.json` - 项目配置文件，包含项目名称、描述和偏好设置
- `core/memory.md` - 项目核心记忆，包含详细的项目概述、模块关系和设计原则
- `core/cc.md` - 项目编码指南，包含编码规范、Java兼容性和异常处理要点
- `core/cc/` - 代码优化规则目录，包含代码优化建议和实施计划

## 会话管理
- `sessions/README.md` - 会话管理指南
- `sessions/YYYY-MM-DD_主题.md` - 已保存的会话记录文件

## 文档目录
- `docs/README.md` - 文档目录说明
- `docs/design/` - 设计文档目录
- `docs/design/architecture/` - 架构设计文档
- `docs/design/module-design/` - 模块设计文档

# 需要加载的文件
在会话开始时，请加载以下文件以获取完整的项目上下文：

1. **核心记忆**：
   - `core/memory.md` - 提供项目的详细概述和模块关系

2. **编码指南**：
   - `core/cc.md` - 提供编码规范和技术要求

3. **相关会话记录**（按需加载）：
   - 如需了解特定主题的历史讨论，可查阅 `sessions/` 目录下的相关会话记录

# 使用方法
1. 在新会话开始时，发送此初始化文件内容
2. 明确说明当前任务或问题
3. 参考相关文档或会话记录提供上下文
4. 遵循沟通规则进行交流
