package com.ge.med.ct.cfg.dicom;

import com.ge.med.ct.cfg.AppDefaults;
import com.ge.med.ct.cfg.ConfigManager;
import com.ge.med.ct.exception.core.QAToolException;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.io.File;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

/**
 * DicomConfigService单元测试
 */
public class DicomConfigServiceTest {
    @Mock
    private ConfigManager configManager;

    private DicomConfigService dicomConfigService;

    @Before
    public void setUp() {
        MockitoAnnotations.openMocks(this);

        // 配置模拟行为
        when(configManager.getString(eq("dicom.scan.directory"), anyString()))
                .thenReturn(System.getProperty("java.io.tmpdir") + File.separator + "test_dicom");
        when(configManager.getBoolean(eq("dicom.cache.enabled"), anyBoolean())).thenReturn(true);
        when(configManager.getInt(eq("dicom.cache.size"), anyInt())).thenReturn(2000);
        when(configManager.getString(eq("dicom.charset"), anyString())).thenReturn("UTF-8");

        dicomConfigService = new DicomConfigService(configManager);
    }

    @Test
    public void testGetRootDirectory() {
        String expected = System.getProperty("java.io.tmpdir") + File.separator + "test_dicom";
        assertEquals(expected, dicomConfigService.getRootDirectory());
        verify(configManager).getString("dicom.scan.directory", AppDefaults.DEFAULT_DICOM_ROOT_DIR);
    }

    @Test
    public void testIsCacheEnabled() {
        assertTrue(dicomConfigService.isCacheEnabled());
        verify(configManager).getBoolean("dicom.cache.enabled", AppDefaults.DEFAULT_DICOM_CACHE_ENABLED);
    }

    @Test
    public void testGetCacheSize() {
        assertEquals(2000, dicomConfigService.getCacheSize());
        verify(configManager).getInt("dicom.cache.size", AppDefaults.DEFAULT_DICOM_CACHE_SIZE);
    }

    @Test
    public void testGetCharset() {
        assertEquals("UTF-8", dicomConfigService.getCharset());
        verify(configManager).getString("dicom.charset", AppDefaults.DEFAULT_DICOM_CHARSET);
    }

    @Test
    public void testValidateSuccess() {
        // 创建测试目录
        File testDir = new File(dicomConfigService.getRootDirectory());
        testDir.mkdirs();

        try {
            // 验证不应抛出异常
            dicomConfigService.validate();
        } catch (QAToolException e) {
            fail("验证不应抛出异常: " + e.getMessage());
        } finally {
            // 清理测试目录
            testDir.delete();
        }
    }

    @Test(expected = QAToolException.class)
    public void testValidateInvalidRootDir() throws QAToolException {
        // 配置无效的根目录（文件而非目录）
        File testFile = new File(System.getProperty("java.io.tmpdir"), "test_file.txt");
        try {
            testFile.createNewFile();

            when(configManager.getString(eq("dicom.scan.directory"), anyString()))
                    .thenReturn(testFile.getAbsolutePath());

            // 应抛出异常
            dicomConfigService.validate();
        } catch (Exception e) {
            if (e instanceof QAToolException) {
                throw (QAToolException) e;
            }
            fail("应抛出QAToolException，但抛出了: " + e.getClass().getName());
        } finally {
            testFile.delete();
        }
    }

    @Test(expected = QAToolException.class)
    public void testValidateNegativeCacheSize() throws QAToolException {
        when(configManager.getInt(eq("dicom.cache.size"), anyInt())).thenReturn(-100);

        // 应抛出异常
        dicomConfigService.validate();
    }
}
