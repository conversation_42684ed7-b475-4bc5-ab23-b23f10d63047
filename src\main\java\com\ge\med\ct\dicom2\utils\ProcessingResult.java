package com.ge.med.ct.dicom2.utils;

import java.util.List;

import com.ge.med.ct.dicom2.model.DicomFileModel;

/**
 * 处理结果
 */
public class ProcessingResult {
    private final List<DicomFileModel> successfulModels;
    private final List<String> failedFiles;

    public ProcessingResult(List<DicomFileModel> successfulModels, List<String> failedFiles) {
        this.successfulModels = successfulModels;
        this.failedFiles = failedFiles;
    }

    public List<DicomFileModel> getSuccessfulModels() {
        return successfulModels;
    }

    public List<String> getFailedFiles() {
        return failedFiles;
    }

    public int getSuccessCount() {
        return successfulModels.size();
    }

    public int getFailedCount() {
        return failedFiles.size();
    }

    public int getFailureCount() {
        return getFailedCount();
    }

    public boolean hasFailures() {
        return !failedFiles.isEmpty();
    }
}
