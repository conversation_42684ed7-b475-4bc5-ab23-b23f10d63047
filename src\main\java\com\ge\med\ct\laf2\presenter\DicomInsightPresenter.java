package com.ge.med.ct.laf2.presenter;

import com.ge.med.ct.dicom2.core.DicomDataProvider;
import com.ge.med.ct.laf2.base.MessageType;
import com.ge.med.ct.laf2.base.listeners.IDicomInsightViewListener;
import com.ge.med.ct.laf2.views.DicomInsightView;
import com.ge.med.ct.service.LogManager;

import java.util.logging.Logger;

/**
 * DICOM信息查看视图的Presenter
 * 处理DicomInsightView的业务逻辑
 */
public class DicomInsightPresenter implements IDicomInsightViewListener {
    private static final Logger logger = LogManager.getInstance().getLogger(DicomInsightPresenter.class);

    private final DicomInsightView view;
    public DicomInsightPresenter(DicomInsightView view) {
        this.view = view;
        this.view.setViewListener(this);
    }

    public void setDicomProvider(DicomDataProvider provider) {
        this.view.setDataProvider(provider);
    }

    public void loadDirectory(String directoryPath) {
        if (directoryPath == null || directoryPath.isEmpty()) {
            view.showMessage(MessageType.ERROR, "目录路径不能为空");
            return;
        }

        view.setDicomScanDir(directoryPath);
        view.loadDirectory();
    }

    @Override
    public void onDicomFileLoaded(String filePath, int tagCount) {
        logger.info("DICOM文件已加载: " + filePath + ", 标签数量: " + tagCount);
    }

    @Override
    public void onDirectoryLoaded(String directoryPath, int fileCount) {
        logger.info("目录已加载: " + directoryPath + ", 文件数量: " + fileCount);
        view.showMessage(MessageType.INFO, "已加载 " + fileCount + " 个文件");
    }

    @Override
    public void onMessage(MessageType type, String message) {
        // 可以在这里处理来自视图的消息
        logger.info("收到消息: " + message);
    }

    @Override
    public void onViewInitialized() {
        logger.info("DICOM信息查看视图已初始化");
    }

    @Override
    public void onViewDestroyed() {
        logger.info("DICOM信息查看视图已销毁");
    }
}
