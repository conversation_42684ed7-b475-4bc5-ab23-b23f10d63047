package com.ge.med.ct.analysis.model;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 分析参数模型类
 * 封装了分析所需的基本参数：输入路径、输出路径、协议和分析模式
 */
public class AnalysisParams {
    private final String inputPath;
    private final String outputPath;
    private final String protocol;
    private final boolean textMode;

    /**
     * 创建分析参数对象，默认使用文本模式
     * @param inputPath 输入路径，指定数据源位置
     * @param outputPath 输出路径，指定结果保存位置
     * @param protocol 分析协议，指定分析规则和参数
     */
    public AnalysisParams(String inputPath, String outputPath, String protocol) {
        this(inputPath, outputPath, protocol, true);
    }

    /**
     * 创建分析参数对象，可指定是否使用文本模式
     * @param inputPath 输入路径，指定数据源位置
     * @param outputPath 输出路径，指定结果保存位置
     * @param protocol 分析协议，指定分析规则和参数
     * @param textMode 是否使用文本模式运行分析
     */
    public AnalysisParams(String inputPath, String outputPath, String protocol, boolean textMode) {
        this.inputPath = inputPath;
        this.outputPath = outputPath;
        this.protocol = protocol;
        this.textMode = textMode;
    }

    /**
     * 获取输入路径
     * @return 数据输入路径
     */
    public String getInputPath() {
        return inputPath;
    }

    /**
     * 获取输出路径
     * @return 结果输出路径
     */
    public String getOutputPath() {
        return outputPath;
    }

    /**
     * 获取协议
     * @return 分析协议
     */
    public String getProtocol() {
        return protocol;
    }
    
    /**
     * 获取协议类型（协议的第一部分）
     * @return 协议类型，如果无法解析则返回整个协议字符串
     */
    public String getProtocolType() {
        String[] parts = parseProtocolParts();
        return parts.length > 0 ? parts[0] : protocol;
    }
    
    /**
     * 获取协议名称（协议的第二部分）
     * @return 协议名称，如果无法解析则返回null
     */
    public String getProtocolName() {
        String[] parts = parseProtocolParts();
        return parts.length > 1 ? parts[1] : null;
    }
    
    /**
     * 获取协议ID（协议的第三部分）
     * @return 协议ID，如果无法解析则返回null
     */
    public String getProtocolId() {
        String[] parts = parseProtocolParts();
        return parts.length > 2 ? parts[2] : null;
    }

    /**
     * 解析协议部分
     * @return 协议各部分组成的数组
     */
    private String[] parseProtocolParts() {
        if (protocol == null || !protocol.contains("|")) {
            return new String[]{protocol};
        }
        return protocol.split("\\|");
    }

    /**
     * 是否使用文本模式
     * @return 如果使用文本模式返回true，否则返回false
     */
    public boolean isTextMode() {
        return textMode;
    }

    /**
     * 验证参数是否有效
     * @return 如果所有必需参数都非空则返回true，否则返回false
     */
    public boolean isValid() {
        return getValidationErrors().isEmpty();
    }

    /**
     * 获取验证错误列表
     * @return 错误消息列表，如果没有错误则返回空列表
     */
    public List<String> getValidationErrors() {
        List<String> errors = new ArrayList<>();
        
        if (isEmpty(inputPath)) {
            errors.add("输入路径不能为空");
        }
        
        if (isEmpty(outputPath)) {
            errors.add("输出路径不能为空");
        }
        
        if (isEmpty(protocol)) {
            errors.add("协议不能为空");
        } else if (!isValidProtocolFormat(protocol)) {
            errors.add("协议格式无效，格式应为TYPE|NAME|ID");
        }
        
        return errors;
    }

    /**
     * 检查协议格式是否有效
     * @param protocol 协议字符串
     * @return 如果格式有效返回true
     */
    private boolean isValidProtocolFormat(String protocol) {
        // 简化协议格式验证
        return protocol.contains("|");
    }

    /**
     * 获取无效参数的错误信息
     * @return 描述无效参数的错误信息，如果所有参数都有效则返回null
     */
    public String getValidationError() {
        List<String> errors = getValidationErrors();
        return errors.isEmpty() ? null : String.join("; ", errors);
    }

    /**
     * 转换为命令行参数格式
     * @return 适用于命令行的参数字符串
     */
    public String toCommandString() {
        StringBuilder command = new StringBuilder();
        
        // 添加模式参数
        if (textMode) {
            command.append("-text ");
        }
        
        // 添加核心参数
        command.append("-input \"").append(inputPath).append("\" ")
               .append("-output \"").append(outputPath).append("\" ")
               .append("-protocol \"").append(protocol).append("\"");
               
        return command.toString();
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        AnalysisParams that = (AnalysisParams) o;
        return textMode == that.textMode &&
               Objects.equals(inputPath, that.inputPath) &&
               Objects.equals(outputPath, that.outputPath) &&
               Objects.equals(protocol, that.protocol);
    }

    @Override
    public int hashCode() {
        return Objects.hash(inputPath, outputPath, protocol, textMode);
    }

    @Override
    public String toString() {
        return "AnalysisParams{" +
                "inputPath='" + inputPath + '\'' +
                ", outputPath='" + outputPath + '\'' +
                ", protocol='" + protocol + '\'' +
                ", textMode=" + textMode +
                '}';
    }

    private boolean isEmpty(String str) {
        return str == null || str.trim().isEmpty();
    }
} 