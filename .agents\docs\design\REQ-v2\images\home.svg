<svg xmlns="http://www.w3.org/2000/svg" xmlns:p="http://www.evolus.vn/Namespace/Pencil" xmlns:pencil="http://www.evolus.vn/Namespace/Pencil" xmlns:html="http://www.w3.org/1999/xhtml" xmlns:xhtml="http://www.w3.org/1999/xhtml" xmlns:office="urn:oasis:names:tc:opendocument:xmlns:office:1.0" xmlns:style="urn:oasis:names:tc:opendocument:xmlns:style:1.0" xmlns:text="urn:oasis:names:tc:opendocument:xmlns:text:1.0" xmlns:table="urn:oasis:names:tc:opendocument:xmlns:table:1.0" xmlns:draw="urn:oasis:names:tc:opendocument:xmlns:drawing:1.0" xmlns:fo="urn:oasis:names:tc:opendocument:xmlns:xsl-fo-compatible:1.0" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:meta="urn:oasis:names:tc:opendocument:xmlns:meta:1.0" xmlns:number="urn:oasis:names:tc:opendocument:xmlns:datastyle:1.0" xmlns:svg="urn:oasis:names:tc:opendocument:xmlns:svg-compatible:1.0" xmlns:chart="urn:oasis:names:tc:opendocument:xmlns:chart:1.0" xmlns:dr3d="urn:oasis:names:tc:opendocument:xmlns:dr3d:1.0" xmlns:math="http://www.w3.org/1998/Math/MathML" xmlns:form="urn:oasis:names:tc:opendocument:xmlns:form:1.0" xmlns:script="urn:oasis:names:tc:opendocument:xmlns:script:1.0" xmlns:ooo="http://openoffice.org/2004/office" xmlns:ooow="http://openoffice.org/2004/writer" xmlns:oooc="http://openoffice.org/2004/calc" xmlns:dom="http://www.w3.org/2001/xml-events" xmlns:xforms="http://www.w3.org/2002/xforms" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:rpt="http://openoffice.org/2005/report" xmlns:of="urn:oasis:names:tc:opendocument:xmlns:of:1.2" xmlns:rdfa="http://docs.oasis-open.org/opendocument/meta/rdfa#" xmlns:field="urn:openoffice:names:experimental:ooo-ms-interop:xmlns:field:1.0" xmlns:regexp="http://exslt.org/regular-expressions" xmlns:em="http://exslt.org/math" xmlns:sodipodi="http://sodipodi.sourceforge.net/DTD/sodipodi-0.dtd" xmlns:inkscape="http://www.inkscape.org/namespaces/inkscape" width="900" height="639" id="exportedSVG" version="1.1" pencil:version="1.2.2" sodipodi:docname="qat"><g inkscape:label="Home" inkscape:groupmode="layer" id="layer_home"><g><rect x="0" y="0" width="900" height="639" fill="none"/><g p:type="Shape" p:def="Evolus.Prototype.GUI:frame" id="16dd3fe80c774e9daf3275e3e1036ae9" transform="matrix(1,0,0,1,0,0)"><p:metadata><p:property name="box">900,639</p:property><p:property name="disabled">false</p:property><p:property name="fillColor">#FAFAFAFF</p:property><p:property name="titleColor">#DEDEDEFF</p:property><p:property name="strokeColor">#666666FF</p:property><p:property name="strokeStyle">1|</p:property><p:property name="textContent">Quality Assurance Tool</p:property><p:property name="textFont">'Arial'|Bold|normal|15px|none|0</p:property><p:property name="textColor">#000000FF</p:property><p:property name="textAlign">0,1</p:property></p:metadata>
            <path style="fill: rgb(250, 250, 250); stroke: rgb(102, 102, 102); stroke-opacity: 1; stroke-width: 1; fill-opacity: 1;" p:name="body" id="156989098fa84db58192be5505270894" d="M 0 32 L 0 0 L 900 0 L 900 639 L 0 639 L 0 32 L 900 32"/>
            <path p:name="menu" id="2385f86ee1f9499d8459d66ade571623" d="M 0.5 0.5 L 899.5 0.5 L 899.5 31 L 0.5 31 z" style="fill: rgb(222, 222, 222); fill-opacity: 1;"/>
            <text p:name="text" id="12c514430ef64aa3bf70c5d4f75f8a3c" transform="translate(10,21)" style="font-family: Arial; font-size: 15px; font-weight: bold; font-style: normal; text-decoration: none; fill: rgb(0, 0, 0); fill-opacity: 1;"><tspan x="0" y="0">Quality Assurance Tool</tspan></text>
        </g><g p:type="Shape" p:def="Evolus.Prototype.GUI:frameControl" id="bd28b958c1c14a369af1fb45120fc838" transform="matrix(1,0,0,1,896.0000133514404,1.0000000298023224)"><p:metadata><p:property name="box">90,30</p:property><p:property name="withMinButton">true</p:property><p:property name="withRestoreButton">true</p:property><p:property name="withCloseButton">true</p:property><p:property name="disabled">false</p:property><p:property name="fillColor">#FAFAFAFF</p:property><p:property name="strokeColor">#666666FF</p:property><p:property name="strokeStyle">2|</p:property></p:metadata>
            <path style="fill: none; stroke: rgb(102, 102, 102); stroke-opacity: 1; stroke-width: 2;" p:name="button" id="64d598d582fb42339385213db6c49927" d="M 66 8 L 82 24 M 82 8 L 66 24 M 39 9 L 53 9 L 53 23 L 39 23 z M 11 23 L 25 23" transform="translate(2.5,-1)"/>
            <path p:name="box" id="97831e430839474f8214fc74c1b9be94" d="M 0 0 L 90 0 L 90 30 L 0 30 z" style="fill: rgb(250, 250, 250); fill-opacity: 0;"/>
        </g><g p:type="Shape" p:def="Evolus.BasicWebElements:pane-v2" id="152868585414434d9fc71d0e428dcb3f" transform="matrix(1,0,0,1,1,32.00000047683716)"><p:metadata><p:property name="radius">10,0</p:property><p:property name="textPadding">0,1.6666666666666667</p:property><p:property name="fixedRadius">false</p:property><p:property name="fillColor">#EEEEEEFF</p:property><p:property name="shadowStyle">0|0|3|1|#000000</p:property><p:property name="shadowColor">#00000000</p:property><p:property name="strokeColor">#CCCCCCFF</p:property><p:property name="strokeStyle">1|</p:property><p:property name="rightBorder">true</p:property><p:property name="bottomBorder">true</p:property><p:property name="textContent"/><p:property name="textFont">PencilUI|normal|normal|13px|none|0</p:property><p:property name="textColor">#000000FF</p:property><p:property name="textAlign">1,1</p:property><p:property name="box">898,50</p:property><p:property name="cornerStyle">none</p:property><p:property name="customStyle"/></p:metadata>
            <foreignObject x="-3" y="-3" width="904" height="56" p:name="htmlObject" id="61f601ed2468463e9dfb23b3a6c2498d" style="padding-top: 3px;">
                <div xmlns="http://www.w3.org/1999/xhtml" style="box-sizing: border-box; overflow: hidden; box-shadow: none; width: 898px; height: 50px; padding: 0px; background-color: rgb(238, 238, 238); x: 10px; border-radius: 0px; border: 1px solid rgb(204, 204, 204); margin-left: 3px;" p:name="div" id="dab7f0da253e4c17bac5b7b5662d2ed3">
                </div>
            </foreignObject>
            <foreignObject x="0" y="0" width="898" height="50" p:name="htmlObject2" id="e00596769cd942d292ff3792898eb272" style="font-family: PencilUI; font-size: 13px; font-weight: normal; font-style: normal; text-decoration: none; fill: rgb(0, 0, 0); fill-opacity: 1; color: rgb(0, 0, 0);">
                <div xmlns="http://www.w3.org/1999/xhtml" style="box-sizing: border-box; overflow: hidden; display: table-cell; width: 898px; height: 50px; padding: 0px; text-align: center; vertical-align: middle;" p:name="div2" id="207fae95520841dea344419726e425e8"><div></div></div>
            </foreignObject>
        </g><g p:type="Shape" p:def="Evolus.WindowsXP.Widgets:TabHeader" id="581a735ea86c4eb2bc93b4d1449ab903" transform="matrix(1,0,0,1,24.00000035762787,50.0000007301569)"><p:metadata><p:property name="box">80,30</p:property><p:property name="disabled">false</p:property><p:property name="tabTitle">Home</p:property><p:property name="textColor">#000000FF</p:property><p:property name="textFont">PencilUI|500|normal|13px|none|0</p:property><p:property name="textAlign">1,1</p:property></p:metadata>
            <g transform="translate(0.5, 2.5)">
                <path style="fill: rgb(245, 244, 234); stroke-width: 1px; fill-opacity: 1; stroke: rgb(145, 167, 180); stroke-opacity: 1;" p:name="rect" id="fdb35231be7647229ef7c6ee482d692e" d="M 0 29.5 L 0 5 C 0 0 0 0 5 0 L 73 0 C 78 0 78 0 78 5 L 78 29.5"/>
                <text x="33.415905" y="14.214675" style="text-align: left; text-anchor: start; dominant-baseline: central; fill: rgb(0, 0, 0); fill-opacity: 1; font-family: PencilUI; font-size: 13px; font-weight: 500; font-style: normal; text-decoration: none;" transform="translate(-11,1)" xml:space="preserve" p:name="text" id="1e89442621e244c6b2d2137c64a09089">Home</text>
            </g>
        </g><g p:type="Shape" p:def="Evolus.WindowsXP.Widgets:TabHeader" id="85b0b85e475b4b23b7bd04202d374da7" transform="matrix(1,0,0,1,117.00000174343586,50.0000007301569)"><p:metadata><p:property name="box">80,30</p:property><p:property name="disabled">false</p:property><p:property name="tabTitle">Report</p:property><p:property name="textColor">#000000FF</p:property><p:property name="textFont">PencilUI|500|normal|13px|none|0</p:property><p:property name="textAlign">1,1</p:property></p:metadata>
            <g transform="translate(0.5, 2.5)">
                <path style="fill: rgb(245, 244, 234); stroke-width: 1px; fill-opacity: 1; stroke: rgb(145, 167, 180); stroke-opacity: 1;" p:name="rect" id="8cf871c850f0403abca63bd821c65501" d="M 0 29.5 L 0 5 C 0 0 0 0 5 0 L 73 0 C 78 0 78 0 78 5 L 78 29.5"/>
                <text x="33.415905" y="14.214675" style="text-align: left; text-anchor: start; dominant-baseline: central; fill: rgb(0, 0, 0); fill-opacity: 1; font-family: PencilUI; font-size: 13px; font-weight: 500; font-style: normal; text-decoration: none;" transform="translate(-13,1)" xml:space="preserve" p:name="text" id="1083072a4b9b484fac082b3223ecb515">Report</text>
            </g>
        </g><g p:type="Shape" p:def="Evolus.WindowsXP.Widgets:TabHeader" id="f49fb79e65f749559d1e11db7ddd0a2d" transform="matrix(1,0,0,1,210.00000312924385,50.0000007301569)"><p:metadata><p:property name="box">100,30</p:property><p:property name="disabled">false</p:property><p:property name="tabTitle">Configuration</p:property><p:property name="textColor">#000000FF</p:property><p:property name="textFont">PencilUI|500|normal|13px|none|0</p:property><p:property name="textAlign">1,1</p:property></p:metadata>
            <g transform="translate(0.5, 2.5)">
                <path style="fill: rgb(245, 244, 234); stroke-width: 1px; fill-opacity: 1; stroke: rgb(145, 167, 180); stroke-opacity: 1;" p:name="rect" id="ce1ba676aea14ce991bb0fe465076d3e" d="M 0 29.5 L 0 5 C 0 0 0 0 5 0 L 93 0 C 98 0 98 0 98 5 L 98 29.5"/>
                <text x="33.415905" y="14.214675" style="text-align: left; text-anchor: start; dominant-baseline: central; fill: rgb(0, 0, 0); fill-opacity: 1; font-family: PencilUI; font-size: 13px; font-weight: 500; font-style: normal; text-decoration: none;" transform="translate(-23,1)" xml:space="preserve" p:name="text" id="d8f718038f634e3796f607be1227bcd8">Configuration</text>
            </g>
        </g><g p:type="Shape" p:def="Evolus.WindowsXP.Widgets:TabHeader" id="98d32bfc2af149f2beebbd7da2076cdb" transform="matrix(1,0,0,1,323.00000481307507,50.0000007301569)"><p:metadata><p:property name="box">80,30</p:property><p:property name="disabled">false</p:property><p:property name="tabTitle">History</p:property><p:property name="textColor">#000000FF</p:property><p:property name="textFont">PencilUI|500|normal|13px|none|0</p:property><p:property name="textAlign">1,1</p:property></p:metadata>
            <g transform="translate(0.5, 2.5)">
                <path style="fill: rgb(245, 244, 234); stroke-width: 1px; fill-opacity: 1; stroke: rgb(145, 167, 180); stroke-opacity: 1;" p:name="rect" id="6f496a713d9a43bbbf169b06329f109a" d="M 0 29.5 L 0 5 C 0 0 0 0 5 0 L 73 0 C 78 0 78 0 78 5 L 78 29.5"/>
                <text x="33.415905" y="14.214675" style="text-align: left; text-anchor: start; dominant-baseline: central; fill: rgb(0, 0, 0); fill-opacity: 1; font-family: PencilUI; font-size: 13px; font-weight: 500; font-style: normal; text-decoration: none;" transform="translate(-14,1)" xml:space="preserve" p:name="text" id="3ccb4dd33fb848eb9e1ee04f487989b3">History</text>
            </g>
        </g><g p:type="Shape" p:def="Evolus.BasicWebElements:pane-v2" id="6871bebd63864fedbf0ae992ce22ea7f" transform="matrix(1,0,0,1,1.0000000149011612,608.0000090450048)"><p:metadata><p:property name="radius">10,0</p:property><p:property name="textPadding">0,0.9999999999999999</p:property><p:property name="fixedRadius">false</p:property><p:property name="fillColor">#EEEEEEFF</p:property><p:property name="shadowStyle">0|0|3|1|#000000</p:property><p:property name="shadowColor">#00000000</p:property><p:property name="strokeColor">#CCCCCCFF</p:property><p:property name="strokeStyle">1|</p:property><p:property name="rightBorder">true</p:property><p:property name="bottomBorder">true</p:property><p:property name="textContent"/><p:property name="textFont">PencilUI|normal|normal|13px|none|0</p:property><p:property name="textColor">#000000FF</p:property><p:property name="textAlign">1,1</p:property><p:property name="box">898,30</p:property><p:property name="cornerStyle">none</p:property><p:property name="customStyle"/></p:metadata>
            <foreignObject x="-3" y="-3" width="904" height="36" p:name="htmlObject" id="23c09e5624034944bdb78591e2e1d2ee" style="padding-top: 3px;">
                <div xmlns="http://www.w3.org/1999/xhtml" style="box-sizing: border-box; overflow: hidden; box-shadow: none; width: 898px; height: 30px; padding: 0px; background-color: rgb(238, 238, 238); x: 10px; border-radius: 0px; border: 1px solid rgb(204, 204, 204); margin-left: 3px;" p:name="div" id="18f5dc3d3b164a1ea12ae143190b82ef">
                </div>
            </foreignObject>
            <foreignObject x="0" y="0" width="898" height="30" p:name="htmlObject2" id="333c00261060401687af47f6120931bd" style="font-family: PencilUI; font-size: 13px; font-weight: normal; font-style: normal; text-decoration: none; fill: rgb(0, 0, 0); fill-opacity: 1; color: rgb(0, 0, 0);">
                <div xmlns="http://www.w3.org/1999/xhtml" style="box-sizing: border-box; overflow: hidden; display: table-cell; width: 898px; height: 30px; padding: 0px; text-align: center; vertical-align: middle;" p:name="div2" id="5e148734bbc34be4a5c3df2e22d62812"><div></div></div>
            </foreignObject>
        </g><g p:type="Shape" p:def="Evolus.Prototype.GUI:frameControl" id="e5d72faaf0644a79a45f5a37854693c2" transform="matrix(1,0,0,1,790.0000000149012,0)"><p:metadata><p:property name="box">100,32</p:property><p:property name="withMinButton">true</p:property><p:property name="withRestoreButton">true</p:property><p:property name="withCloseButton">true</p:property><p:property name="disabled">false</p:property><p:property name="fillColor">#FAFAFAFF</p:property><p:property name="strokeColor">#666666FF</p:property><p:property name="strokeStyle">2|</p:property></p:metadata>
            <path style="fill: none; stroke: rgb(102, 102, 102); stroke-opacity: 1; stroke-width: 2;" p:name="button" id="aae6de62d9c147eab7ab63f99e4510ab" d="M 76 8 L 92 24 M 92 8 L 76 24 M 49 9 L 63 9 L 63 23 L 49 23 z M 21 23 L 35 23" transform="translate(2.5,0)"/>
            <path p:name="box" id="2ba4f2af80864552bba82a4c9278ba90" d="M 0 0 L 100 0 L 100 32 L 0 32 z" style="fill: rgb(250, 250, 250); fill-opacity: 0;"/>
        </g></g></g><g inkscape:label="Home Page" inkscape:groupmode="layer" id="layer_home_page"><g><rect x="0" y="0" width="900" height="639" fill="none"/><g p:type="Shape" p:def="Evolus.BasicWebElements:pane-v2" id="80dd931655ff433aa9b2fb88899ca651" transform="matrix(1,0,0,1,3,85)"><p:metadata><p:property name="radius">10,0</p:property><p:property name="textPadding">0,17.3</p:property><p:property name="fixedRadius">false</p:property><p:property name="fillColor">#EEEEEEFF</p:property><p:property name="shadowStyle">0|0|3|1|#000000</p:property><p:property name="shadowColor">#00000000</p:property><p:property name="strokeColor">#CCCCCCFF</p:property><p:property name="strokeStyle">1|</p:property><p:property name="rightBorder">true</p:property><p:property name="bottomBorder">true</p:property><p:property name="textContent"/><p:property name="textFont">"Liberation Sans",Arial,sans-serif|normal|normal|13px|none|0</p:property><p:property name="textColor">#000000FF</p:property><p:property name="textAlign">1,1</p:property><p:property name="box">250,519</p:property><p:property name="cornerStyle">none</p:property><p:property name="customStyle"/></p:metadata>
            <foreignObject x="-3" y="-3" width="256" height="525" p:name="htmlObject" id="7ddc3d9a32f04053952448b481dbc059" style="padding-top: 3px;">
                <div xmlns="http://www.w3.org/1999/xhtml" style="box-sizing: border-box; overflow: hidden; box-shadow: none; width: 250px; height: 519px; padding: 0px; background-color: rgb(238, 238, 238); x: 10px; border-radius: 0px; border: 1px solid rgb(204, 204, 204); margin-left: 3px;" p:name="div" id="db58680a515d4c779193abea3a4906a8">
                </div>
            </foreignObject>
            <foreignObject x="0" y="0" width="250" height="519" p:name="htmlObject2" id="5a6977a595234190ace0f17f3a91fe40" style="font-family: &quot;Liberation Sans&quot;, Arial, sans-serif; font-size: 13px; font-weight: normal; font-style: normal; text-decoration: none; fill: rgb(0, 0, 0); fill-opacity: 1; color: rgb(0, 0, 0);">
                <div xmlns="http://www.w3.org/1999/xhtml" style="box-sizing: border-box; overflow: hidden; display: table-cell; width: 250px; height: 519px; padding: 0px; text-align: center; vertical-align: middle;" p:name="div2" id="350fcc3345804c7cae56aaaf0dce054c"><div></div></div>
            </foreignObject>
        </g><g p:type="Shape" p:def="Evolus.WindowsXP.Widgets:Groupbox" id="7a9a9e28ceb640589b213d077b87ad48" transform="matrix(1,0,0,1,10.000000149011612,108.00000149011612)"><p:metadata><p:property name="box">236,147</p:property><p:property name="label">Test Options</p:property><p:property name="textColor">#000000FF</p:property><p:property name="textFont">PencilUI|500|normal|10px|none|0</p:property></p:metadata>
            <path d="M 72.24551010131836,0.5 L 231.5,0.5 C 234,0.5 235.5,2.2946114 235.5,4.5 L 235.5,142.5 C 235.5,145 234,146.5 231.5,146.5 L 4.5,146.5 C 2.284,146.5 0.5,145 0.5,142.5 L 0.5,4.5106114 C 0.5,2.2946114 2.284,0.51061056 4.5,0.51061056 L 5.5,0.51061056" style="color:#000000;fill:#ffffff;fill-opacity:0;fill-rule:nonzero;stroke:#aca899;stroke-width:1.00000012;stroke-linecap:butt;stroke-linejoin:miter;marker:none;marker-start:none;marker-mid:none;marker-end:none;stroke-miterlimit:4;stroke-dashoffset:0;stroke-opacity:1;visibility:visible;display:inline;overflow:visible;enable-background:accumulate" p:name="rect" id="1f66b638906c4989b6bc92a548a24683"/>
            <text y="9.0077095" x="17.999992" style="font-size: 10px; font-style: normal; font-weight: 500; text-align: left; text-anchor: start; dominant-baseline: central; fill: rgb(0, 0, 0); font-family: PencilUI; fill-opacity: 1; text-decoration: none;" xml:space="preserve" p:name="text" id="dcd8912649ac4be8a6978843f0f89c75" transform="translate(-8,-9)">Test Options</text>
        </g><g p:type="Shape" p:def="Evolus.WindowsXP.Widgets:Combo" id="ed5a5192f2ac4d858d011092877856b1" transform="matrix(1,0,0,1,38.000000566244125,142.0000021159649)"><p:metadata><p:property name="box">180,28</p:property><p:property name="buttonText">Acceptance</p:property><p:property name="textColor">#000000FF</p:property><p:property name="textFont">PencilUI|500|normal|11px|none|0</p:property><p:property name="textAlign">0,1</p:property><p:property name="disabled">false</p:property><p:property name="focused">false</p:property></p:metadata>

          <defs p:name="defs2177" id="547cb1deb77d49a0b1704a313a38a9cc">
            <linearGradient x1="0%" y1="0%" x2="100%" y2="100%" p:name="linearGradient4129" id="f02327d5c03d472096da3c7e85b43134">
              <stop style="stop-color:#e7eeff;stop-opacity:1" offset="0" p:name="stop1" id="a1b40b11353941d3bb5141298819e6de"/>
              <stop style="stop-color:#aec8f7;stop-opacity:1" offset="1" p:name="stop2" id="47454320ebf544b785d6ca8ceee1c4b4"/>
            </linearGradient>
          </defs>
            <rect width="179" height="27" x="0.5" y="0.5" style="opacity: 1; color: rgb(0, 0, 0); fill: rgb(255, 255, 255); fill-opacity: 1; fill-rule: nonzero; stroke: none;" p:name="bgRect1" id="e433612341bb4606990312e8eb3df37a"/>
            <rect width="20" height="28" x="160" y="0" style="opacity:1;color:#000000;fill:#ffffff;fill-opacity:1;fill-rule:nonzero;stroke: none;" p:name="bgRect2" id="322abf96136f423794d73efd61c4bf42"/>
            <rect width="179" height="27" x="0.5" y="0.5" style="opacity:1;color:#000000;fill:none;fill-opacity:1;fill-rule:nonzero;stroke:#7f9db9;stroke-width:1;stroke-linecap:square;stroke-linejoin:miter;marker:none;marker-start:none;marker-mid:none;marker-end:none;stroke-miterlimit:4;stroke-dasharray:none;stroke-dashoffset:0;stroke-opacity:1;visibility:visible;display:inline;overflow:visible;enable-background:accumulate" p:name="rect" id="a4c340bdd2514bebae839391309f0c59"/>
            <rect width="16" height="23" rx="1" ry="1" x="161.5" y="2.5" style="opacity:1;color:#000000;fill:url(#f02327d5c03d472096da3c7e85b43134);fill-opacity:1;fill-rule:nonzero;stroke:#c3d3f8;stroke-width:0.96954674;stroke-linecap:square;stroke-linejoin:miter;marker:none;marker-start:none;marker-mid:none;marker-end:none;stroke-miterlimit:4;stroke-dasharray:none;stroke-dashoffset:0;stroke-opacity:1;visibility:visible;display:inline;overflow:visible;enable-background:accumulate" p:name="dropRect" id="fc0563b754c243e2828bac456446bc25"/>
            <path d="M 173.5,12 L 169.5,16 L 165.5,12" style="color:#000000;fill:none;fill-opacity:1;fill-rule:nonzero;stroke:#4d6185;stroke-width:1.99999988;stroke-linecap:butt;stroke-linejoin:miter;marker:none;marker-start:none;marker-mid:none;marker-end:none;stroke-miterlimit:4;stroke-dashoffset:0;stroke-opacity:1;visibility:visible;display:inline;overflow:visible;enable-background:accumulate" p:name="marker" id="5760570392714c30a25fba425ff82a4e"/>
            <rect width="156" height="23" x="2.5" y="2.5" style="opacity: 1; color: rgb(0, 0, 0); fill: rgb(49, 106, 197); fill-opacity: 1; fill-rule: nonzero; stroke: rgb(255, 255, 255); stroke-dasharray: 1, 1; visibility: hidden; display: none;" p:name="focusRect" id="3f03486e5d8b40178b0eb41cdc2222c0"/>
            <text x="4.561419" y="14.175718" style="text-align: left; text-anchor: start; dominant-baseline: central; fill: rgb(0, 0, 0); fill-opacity: 1; font-family: PencilUI; font-size: 11px; font-weight: 500; font-style: normal; text-decoration: none;" xml:space="preserve" p:name="text" id="33f7dd8be9dc4d5fa3f9f4d93e5b3ad4" transform="translate(1,0)">Acceptance</text>
          </g><g p:type="Shape" p:def="Evolus.WindowsXP.Widgets:Combo" id="7c40dea988ba4395b991b3e4f4ac0767" transform="matrix(1,0,0,1,38.000000566244125,196.0000029057264)"><p:metadata><p:property name="box">180,28</p:property><p:property name="buttonText">GEHC</p:property><p:property name="textColor">#000000FF</p:property><p:property name="textFont">Tahoma|normal|normal|11px|none|0</p:property><p:property name="textAlign">0,1</p:property><p:property name="disabled">false</p:property><p:property name="focused">false</p:property></p:metadata>

          <defs p:name="defs2177" id="fd5cc93815754b5b9b8bc9d8f8505a8a">
            <linearGradient x1="0%" y1="0%" x2="100%" y2="100%" p:name="linearGradient4129" id="700d03992ceb44deb718dd88663d11d8">
              <stop style="stop-color:#e7eeff;stop-opacity:1" offset="0" p:name="stop1" id="5d654d6ca0eb40deb1b06aec0312533d"/>
              <stop style="stop-color:#aec8f7;stop-opacity:1" offset="1" p:name="stop2" id="761c26f936ae45d8a77e3a517487a78c"/>
            </linearGradient>
          </defs>
            <rect width="179" height="27" x="0.5" y="0.5" style="opacity: 1; color: rgb(0, 0, 0); fill: rgb(255, 255, 255); fill-opacity: 1; fill-rule: nonzero; stroke: none;" p:name="bgRect1" id="1fcd2e06e6994e688491c269f87ae983"/>
            <rect width="20" height="28" x="160" y="0" style="opacity:1;color:#000000;fill:#ffffff;fill-opacity:1;fill-rule:nonzero;stroke: none;" p:name="bgRect2" id="b5745384364f452e828ae96971898850"/>
            <rect width="179" height="27" x="0.5" y="0.5" style="opacity:1;color:#000000;fill:none;fill-opacity:1;fill-rule:nonzero;stroke:#7f9db9;stroke-width:1;stroke-linecap:square;stroke-linejoin:miter;marker:none;marker-start:none;marker-mid:none;marker-end:none;stroke-miterlimit:4;stroke-dasharray:none;stroke-dashoffset:0;stroke-opacity:1;visibility:visible;display:inline;overflow:visible;enable-background:accumulate" p:name="rect" id="22809d3781d04683819411739efb7586"/>
            <rect width="16" height="23" rx="1" ry="1" x="161.5" y="2.5" style="opacity:1;color:#000000;fill:url(#700d03992ceb44deb718dd88663d11d8);fill-opacity:1;fill-rule:nonzero;stroke:#c3d3f8;stroke-width:0.96954674;stroke-linecap:square;stroke-linejoin:miter;marker:none;marker-start:none;marker-mid:none;marker-end:none;stroke-miterlimit:4;stroke-dasharray:none;stroke-dashoffset:0;stroke-opacity:1;visibility:visible;display:inline;overflow:visible;enable-background:accumulate" p:name="dropRect" id="f51fbebf02494d87ae0ddcb8365438c3"/>
            <path d="M 173.5,12 L 169.5,16 L 165.5,12" style="color:#000000;fill:none;fill-opacity:1;fill-rule:nonzero;stroke:#4d6185;stroke-width:1.99999988;stroke-linecap:butt;stroke-linejoin:miter;marker:none;marker-start:none;marker-mid:none;marker-end:none;stroke-miterlimit:4;stroke-dashoffset:0;stroke-opacity:1;visibility:visible;display:inline;overflow:visible;enable-background:accumulate" p:name="marker" id="44b4582940464fd49605a0e640ab9c92"/>
            <rect width="156" height="23" x="2.5" y="2.5" style="opacity: 1; color: rgb(0, 0, 0); fill: rgb(49, 106, 197); fill-opacity: 1; fill-rule: nonzero; stroke: rgb(255, 255, 255); stroke-dasharray: 1, 1; visibility: hidden; display: none;" p:name="focusRect" id="e7b92525a1ef48ff8dab7ca181d81207"/>
            <text x="4.561419" y="14.175718" style="text-align: left; text-anchor: start; dominant-baseline: central; fill: rgb(0, 0, 0); fill-opacity: 1; font-family: Tahoma; font-size: 11px; font-weight: normal; font-style: normal; text-decoration: none;" xml:space="preserve" p:name="text" id="6984f5d7cd3b4c51bb7fc5af6cc3c38d" transform="translate(0,0)">GEHC</text>
          </g><g p:type="Shape" p:def="Evolus.WindowsXP.Widgets:Label" id="cdc502671eca4ffbb058ee98ebc37f0f" transform="matrix(1,0,0,1,39,125.99999998509884)"><p:metadata><p:property name="disabled">false</p:property><p:property name="label">Test Type</p:property><p:property name="textColor">#000000FF</p:property><p:property name="textFont">PencilUI|500|normal|13px|none|0</p:property></p:metadata>
              <text y="9.0077095" x="17.999992" style="font-size: 13px; font-style: normal; font-weight: 500; text-align: left; text-anchor: start; dominant-baseline: central; fill: rgb(0, 0, 0); font-family: PencilUI; fill-opacity: 1; text-decoration: none;" xml:space="preserve" p:name="text" id="ff094dd6a9e340c795a2c9e30e8a19da" transform="translate(-18,-3)">Test Type</text>
             <path style="fill: none; stroke-width: 1px; visibility: hidden;" p:name="accelIndicator" id="abe93725a3eb4dfa9ad0224f3df191c5"/>
        </g><g p:type="Shape" p:def="Evolus.WindowsXP.Widgets:Label" id="cdfd2e9d61014eb8bc05bf4169f54818" transform="matrix(1,0,0,1,39,179.9999999552965)"><p:metadata><p:property name="disabled">false</p:property><p:property name="label">Test Standards</p:property><p:property name="textColor">#000000FF</p:property><p:property name="textFont">PencilUI|500|normal|13px|none|0</p:property></p:metadata>
              <text y="9.0077095" x="17.999992" style="font-size: 13px; font-style: normal; font-weight: 500; text-align: left; text-anchor: start; dominant-baseline: central; fill: rgb(0, 0, 0); font-family: PencilUI; fill-opacity: 1; text-decoration: none;" xml:space="preserve" p:name="text" id="a666bf8f002444ea8780dc3f46603037" transform="translate(-18,-3)">Test Standards</text>
             <path style="fill: none; stroke-width: 1px; visibility: hidden;" p:name="accelIndicator" id="4840d6de4a3c46f4b8a35a937d7d40b4"/>
        </g><g p:type="Shape" p:def="Evolus.WindowsXP.Widgets:Groupbox" id="4c4bb0c6881f49159d296bf0c8c093d8" transform="matrix(1,0,0,1,11.000000163912773,265.00000393390656)"><p:metadata><p:property name="box">235,285</p:property><p:property name="label">Analysis Protocols</p:property><p:property name="textColor">#000000FF</p:property><p:property name="textFont">PencilUI|500|normal|10px|none|0</p:property></p:metadata>
            <path d="M 99.3589859008789,0.5 L 230.5,0.5 C 233,0.5 234.5,2.2946114 234.5,4.5 L 234.5,280.5 C 234.5,283 233,284.5 230.5,284.5 L 4.5,284.5 C 2.284,284.5 0.5,283 0.5,280.5 L 0.5,4.5106114 C 0.5,2.2946114 2.284,0.51061056 4.5,0.51061056 L 5.5,0.51061056" style="color:#000000;fill:#ffffff;fill-opacity:0;fill-rule:nonzero;stroke:#aca899;stroke-width:1.00000012;stroke-linecap:butt;stroke-linejoin:miter;marker:none;marker-start:none;marker-mid:none;marker-end:none;stroke-miterlimit:4;stroke-dashoffset:0;stroke-opacity:1;visibility:visible;display:inline;overflow:visible;enable-background:accumulate" p:name="rect" id="5954ff7a74e44a9bb9dc8e5aee6e0cb1"/>
            <text y="9.0077095" x="17.999992" style="font-size: 10px; font-style: normal; font-weight: 500; text-align: left; text-anchor: start; dominant-baseline: central; fill: rgb(0, 0, 0); font-family: PencilUI; fill-opacity: 1; text-decoration: none;" xml:space="preserve" p:name="text" id="678828713da94c5bb27d53769a3cfb1c" transform="translate(-7,-9)">Analysis Protocols</text>
        </g><g p:type="Shape" p:def="Evolus.WindowsXP.Widgets:CheckBox" id="489163a57c394e5da855c7db0535b7a6" transform="matrix(1,0,0,1,38.00000061094761,302.0000045001507)"><p:metadata><p:property name="disabled">false</p:property><p:property name="checked">false</p:property><p:property name="label">LCD</p:property><p:property name="textColor">#000000FF</p:property><p:property name="textFont">PencilUI|500|normal|13px|none|0</p:property></p:metadata>
                <defs p:name="defs2659" id="3bb8ae4407d94ea2b9ce9dfed7921254">
                    <linearGradient x1="0%" y1="0%" x2="100%" y2="100%" p:name="linearGradient31435" id="67fa28933f1e490687c000af306f2993">
                      <stop style="stop-color:#E2E2DD;stop-opacity:1" offset="0" p:name="stop1" id="15bc3acdf1284867bf85550b86bf959d"/>
                      <stop style="stop-color:#FFFFFF;stop-opacity:1" offset="1" p:name="stop2" id="112aaeb9308d4e4eb23421246ffc12e5"/>
                    </linearGradient>

              </defs>
                  <rect y="0.5" x="0.5" height="12" width="12" style="opacity: 1; color: rgb(0, 0, 0); fill: url(&quot;#67fa28933f1e490687c000af306f2993&quot;); fill-opacity: 1; fill-rule: nonzero; stroke: rgb(35, 86, 130); stroke-width: 1; stroke-linecap: round; stroke-linejoin: round; marker: none; stroke-miterlimit: 4; stroke-dasharray: none; stroke-dashoffset: 0; stroke-opacity: 1; visibility: visible; display: inline; overflow: visible;" p:name="rect" id="92156e1e65af4131b212cd0d06d29336"/>
                  <text y="9.0077095" x="17.999992" style="font-size: 13px; font-style: normal; font-weight: 500; text-align: left; text-anchor: start; dominant-baseline: central; fill: rgb(0, 0, 0); font-family: PencilUI; fill-opacity: 1; text-decoration: none;" xml:space="preserve" p:name="text" id="9cde2d11f0d44436a4257760acfd8c99" transform="translate(0,-3)">LCD</text>
                  <path style="fill: none; fill-rule: evenodd; stroke: rgb(56, 185, 53); stroke-width: 2; stroke-linecap: butt; stroke-linejoin: miter; stroke-miterlimit: 4; stroke-dasharray: none; stroke-opacity: 1; visibility: hidden; display: none;" d="M 2.9699794,5.3993084 L 5.5632114,8.7682884 L 10.376336,3.1939584" p:name="checkedPath" id="c770e7cbaf184dd5a333d1741f7bf67b"/>
        </g><g p:type="Shape" p:def="Evolus.WindowsXP.Widgets:CheckBox" id="fe2f977fa500435d86a9b9e7ea0b1350" transform="matrix(1,0,0,1,38.000000566244125,424.0000063329935)"><p:metadata><p:property name="disabled">false</p:property><p:property name="checked">false</p:property><p:property name="label">CT number</p:property><p:property name="textColor">#000000FF</p:property><p:property name="textFont">PencilUI|500|normal|13px|none|0</p:property></p:metadata>
                <defs p:name="defs2659" id="1920721a859a494aac03c44301133544">
                    <linearGradient x1="0%" y1="0%" x2="100%" y2="100%" p:name="linearGradient31435" id="5fc639f143324ca7bd2ba1cfb4e724b7">
                      <stop style="stop-color:#E2E2DD;stop-opacity:1" offset="0" p:name="stop1" id="36c74beae30b45978f8ce679cbfc7f0f"/>
                      <stop style="stop-color:#FFFFFF;stop-opacity:1" offset="1" p:name="stop2" id="44ba404ee8fd45c0937485f55ab14e4b"/>
                    </linearGradient>

              </defs>
                  <rect y="0.5" x="0.5" height="12" width="12" style="opacity: 1; color: rgb(0, 0, 0); fill: url(#5fc639f143324ca7bd2ba1cfb4e724b7); fill-opacity: 1; fill-rule: nonzero; stroke: rgb(35, 86, 130); stroke-width: 1; stroke-linecap: round; stroke-linejoin: round; marker: none; stroke-miterlimit: 4; stroke-dasharray: none; stroke-dashoffset: 0; stroke-opacity: 1; visibility: visible; display: inline; overflow: visible;" p:name="rect" id="595eaf0df9034c6db101d5dad0a001cf"/>
                  <text y="9.0077095" x="17.999992" style="font-size: 13px; font-style: normal; font-weight: 500; text-align: left; text-anchor: start; dominant-baseline: central; fill: rgb(0, 0, 0); font-family: PencilUI; fill-opacity: 1; text-decoration: none;" xml:space="preserve" p:name="text" id="9ffdbd9766ab434a8f7f9a1d7783451b" transform="translate(0,-3)">CT number</text>
                  <path style="fill: none; fill-rule: evenodd; stroke: rgb(56, 185, 53); stroke-width: 2; stroke-linecap: butt; stroke-linejoin: miter; stroke-miterlimit: 4; stroke-dasharray: none; stroke-opacity: 1; visibility: hidden; display: none;" d="M 2.9699794,5.3993084 L 5.5632114,8.7682884 L 10.376336,3.1939584" p:name="checkedPath" id="cc4fc534eb274ac68b08316da5890d54"/>
        </g><g p:type="Shape" p:def="Evolus.WindowsXP.Widgets:CheckBox" id="48e94ff786ff4b8d9e489d2f820878f8" transform="matrix(1,0,0,1,38.000000566244125,368.0000054985285)"><p:metadata><p:property name="disabled">false</p:property><p:property name="checked">false</p:property><p:property name="label">NOISE</p:property><p:property name="textColor">#000000FF</p:property><p:property name="textFont">PencilUI|500|normal|13px|none|0</p:property></p:metadata>
                <defs p:name="defs2659" id="4aa63b78b00349f99ef5ee4996aa6b07">
                    <linearGradient x1="0%" y1="0%" x2="100%" y2="100%" p:name="linearGradient31435" id="c6426e2c08844187876cf29c2dc653dd">
                      <stop style="stop-color:#E2E2DD;stop-opacity:1" offset="0" p:name="stop1" id="c634d38338d44fa48e407e9c7ec2ea9c"/>
                      <stop style="stop-color:#FFFFFF;stop-opacity:1" offset="1" p:name="stop2" id="f0913b7397024332a9ddd10482d08cec"/>
                    </linearGradient>

              </defs>
                  <rect y="0.5" x="0.5" height="12" width="12" style="opacity: 1; color: rgb(0, 0, 0); fill: url(#c6426e2c08844187876cf29c2dc653dd); fill-opacity: 1; fill-rule: nonzero; stroke: rgb(35, 86, 130); stroke-width: 1; stroke-linecap: round; stroke-linejoin: round; marker: none; stroke-miterlimit: 4; stroke-dasharray: none; stroke-dashoffset: 0; stroke-opacity: 1; visibility: visible; display: inline; overflow: visible;" p:name="rect" id="2e245d8e136547cd8a916c25108142a3"/>
                  <text y="9.0077095" x="17.999992" style="font-size: 13px; font-style: normal; font-weight: 500; text-align: left; text-anchor: start; dominant-baseline: central; fill: rgb(0, 0, 0); font-family: PencilUI; fill-opacity: 1; text-decoration: none;" xml:space="preserve" p:name="text" id="d2ee6f8d10134399840b93978ab47ca7" transform="translate(0,-3)">NOISE</text>
                  <path style="fill: none; fill-rule: evenodd; stroke: rgb(56, 185, 53); stroke-width: 2; stroke-linecap: butt; stroke-linejoin: miter; stroke-miterlimit: 4; stroke-dasharray: none; stroke-opacity: 1; visibility: hidden; display: none;" d="M 2.9699794,5.3993084 L 5.5632114,8.7682884 L 10.376336,3.1939584" p:name="checkedPath" id="19c17ad425a641778bb95decf8203c09"/>
        </g><g p:type="Shape" p:def="Evolus.WindowsXP.Widgets:CheckBox" id="1521a2287bed4f478f04dffbfb841a58" transform="matrix(1,0,0,1,38.000000566244125,396.000005915761)"><p:metadata><p:property name="disabled">false</p:property><p:property name="checked">false</p:property><p:property name="label">UNIFORMITY</p:property><p:property name="textColor">#000000FF</p:property><p:property name="textFont">PencilUI|500|normal|13px|none|0</p:property></p:metadata>
                <defs p:name="defs2659" id="8d86e5c5185344b99e8a7c0cd26110d1">
                    <linearGradient x1="0%" y1="0%" x2="100%" y2="100%" p:name="linearGradient31435" id="c56deea2383a4d028567e3c3f35421e5">
                      <stop style="stop-color:#E2E2DD;stop-opacity:1" offset="0" p:name="stop1" id="a2302024439a4bd48915bcda49f61397"/>
                      <stop style="stop-color:#FFFFFF;stop-opacity:1" offset="1" p:name="stop2" id="448bdaa573e74c1ea24663f313fff745"/>
                    </linearGradient>

              </defs>
                  <rect y="0.5" x="0.5" height="12" width="12" style="opacity: 1; color: rgb(0, 0, 0); fill: url(#c56deea2383a4d028567e3c3f35421e5); fill-opacity: 1; fill-rule: nonzero; stroke: rgb(35, 86, 130); stroke-width: 1; stroke-linecap: round; stroke-linejoin: round; marker: none; stroke-miterlimit: 4; stroke-dasharray: none; stroke-dashoffset: 0; stroke-opacity: 1; visibility: visible; display: inline; overflow: visible;" p:name="rect" id="13c2654e2a9948908f0b59e9071d5bdb"/>
                  <text y="9.0077095" x="17.999992" style="font-size: 13px; font-style: normal; font-weight: 500; text-align: left; text-anchor: start; dominant-baseline: central; fill: rgb(0, 0, 0); font-family: PencilUI; fill-opacity: 1; text-decoration: none;" xml:space="preserve" p:name="text" id="a6554f1c397944519d929edeebc4b01d" transform="translate(0,-3)">UNIFORMITY</text>
                  <path style="fill: none; fill-rule: evenodd; stroke: rgb(56, 185, 53); stroke-width: 2; stroke-linecap: butt; stroke-linejoin: miter; stroke-miterlimit: 4; stroke-dasharray: none; stroke-opacity: 1; visibility: hidden; display: none;" d="M 2.9699794,5.3993084 L 5.5632114,8.7682884 L 10.376336,3.1939584" p:name="checkedPath" id="ec030077eed84da4b5e4a39a160be8db"/>
        </g><g p:type="Shape" p:def="Evolus.WindowsXP.Widgets:Seprator" id="08fd19cc5d2d4efc89bc281d74dff3a8" transform="matrix(1,0,0,1,38.000000566244125,330.0000049173832)"><p:metadata><p:property name="box">170,10</p:property><p:property name="type">normal</p:property></p:metadata>
            <rect style="fill: #000000; fill-opacity: 0; stroke: none;" x="0" y="0" p:name="bgRect" id="718790ab0d2d4dbe8db65801240f2ee8" width="170" height="10"/>
            <path style="fill:none; stroke: #ACA899; stroke-width: 1px;" d="M 0 5 L 170 5" transform="translate(0.5,0.5)" p:name="line1" id="fef00672aa0841f48d06e4411fbca962"/>
            <path style="fill: none; stroke: rgb(255, 255, 255); stroke-width: 1px; visibility: visible;" d="M 0 6 L 170 6" transform="translate(0.5,0.5)" p:name="line2" id="3accabd1e9c042ab942ccb7754e9b5c9"/>
        </g><g p:type="Shape" p:def="Evolus.WindowsXP.Widgets:Label" id="27b11a87c4a14baeb08d0f71981706f1" transform="matrix(1,0,0,1,87.00000129640102,339.00000505149364)"><p:metadata><p:property name="disabled">false</p:property><p:property name="label">Series Means</p:property><p:property name="textColor">#999999FF</p:property><p:property name="textFont">Tahoma|bold|normal|12px|none|0</p:property></p:metadata>
              <text y="9.0077095" x="17.999992" style="font-size: 12px; font-style: normal; font-weight: bold; text-align: left; text-anchor: start; dominant-baseline: central; fill: rgb(153, 153, 153); font-family: Tahoma; fill-opacity: 1; text-decoration: none;" xml:space="preserve" p:name="text" id="023ae8ab4fa64154a0882d0f455a64d5" transform="translate(-18,-3)">Series Means</text>
             <path style="fill: none; stroke-width: 1px; visibility: hidden;" p:name="accelIndicator" id="c5ff488bdcff48b09659b6899da22074"/>
        </g><g p:type="Shape" p:def="Evolus.BasicWebElements:pane-v2" id="7c0e871445ab4508a85e663d7d274aeb" transform="matrix(1,0,0,1,255,85)"><p:metadata><p:property name="radius">10,0</p:property><p:property name="textPadding">0,17.3</p:property><p:property name="fixedRadius">false</p:property><p:property name="fillColor">#EEEEEEFF</p:property><p:property name="shadowStyle">0|0|3|1|#000000</p:property><p:property name="shadowColor">#00000000</p:property><p:property name="strokeColor">#CCCCCCFF</p:property><p:property name="strokeStyle">1|</p:property><p:property name="rightBorder">true</p:property><p:property name="bottomBorder">true</p:property><p:property name="textContent"/><p:property name="textFont">"Liberation Sans",Arial,sans-serif|normal|normal|13px|none|0</p:property><p:property name="textColor">#000000FF</p:property><p:property name="textAlign">1,1</p:property><p:property name="box">642,519</p:property><p:property name="cornerStyle">none</p:property><p:property name="customStyle"/></p:metadata>
            <foreignObject x="-3" y="-3" width="648" height="525" p:name="htmlObject" id="a8fc224a4e154bc1a1b79b53a1834112" style="padding-top: 3px;">
                <div xmlns="http://www.w3.org/1999/xhtml" style="box-sizing: border-box; overflow: hidden; box-shadow: none; width: 642px; height: 519px; padding: 0px; background-color: rgb(238, 238, 238); x: 10px; border-radius: 0px; border: 1px solid rgb(204, 204, 204); margin-left: 3px;" p:name="div" id="162dcdee8e2344b39165153c99b2fef4">
                </div>
            </foreignObject>
            <foreignObject x="0" y="0" width="642" height="519" p:name="htmlObject2" id="5bd1354eefe64e92bdcd5d8af40aa75e" style="font-family: &quot;Liberation Sans&quot;, Arial, sans-serif; font-size: 13px; font-weight: normal; font-style: normal; text-decoration: none; fill: rgb(0, 0, 0); fill-opacity: 1; color: rgb(0, 0, 0);">
                <div xmlns="http://www.w3.org/1999/xhtml" style="box-sizing: border-box; overflow: hidden; display: table-cell; width: 642px; height: 519px; padding: 0px; text-align: center; vertical-align: middle;" p:name="div2" id="2f67b47376c44cfa82ffffd5495e83ca"><div></div></div>
            </foreignObject>
        </g><g p:type="Shape" p:def="Evolus.WindowsXP.Widgets:Groupbox" id="0b1cb4b8b669443d9e251daf6787de9d" transform="matrix(1,0,0,1,264,108)"><p:metadata><p:property name="box">626,70</p:property><p:property name="label">Scan Query</p:property><p:property name="textColor">#000000FF</p:property><p:property name="textFont">PencilUI|500|normal|10px|none|0</p:property></p:metadata>
            <path d="M 66.76211166381836,0.5 L 621.5,0.5 C 624,0.5 625.5,2.2946114 625.5,4.5 L 625.5,65.5 C 625.5,68 624,69.5 621.5,69.5 L 4.5,69.5 C 2.284,69.5 0.5,68 0.5,65.5 L 0.5,4.5106114 C 0.5,2.2946114 2.284,0.51061056 4.5,0.51061056 L 5.5,0.51061056" style="color:#000000;fill:#ffffff;fill-opacity:0;fill-rule:nonzero;stroke:#aca899;stroke-width:1.00000012;stroke-linecap:butt;stroke-linejoin:miter;marker:none;marker-start:none;marker-mid:none;marker-end:none;stroke-miterlimit:4;stroke-dashoffset:0;stroke-opacity:1;visibility:visible;display:inline;overflow:visible;enable-background:accumulate" p:name="rect" id="8f44fd86a2fb40adad14c67e3acb3d9e"/>
            <text y="9.0077095" x="17.999992" style="font-size: 10px; font-style: normal; font-weight: 500; text-align: left; text-anchor: start; dominant-baseline: central; fill: rgb(0, 0, 0); font-family: PencilUI; fill-opacity: 1; text-decoration: none;" xml:space="preserve" p:name="text" id="17be84a968ed4ba39720f9e24ae9d3d2" transform="translate(-8,-9)">Scan Query</text>
        </g><g p:type="Shape" p:def="Evolus.WindowsXP.Widgets:TextBox" id="adf3c224e54841fb89decd4f3d2269fe" transform="matrix(1,0,0,1,282,141)"><p:metadata><p:property name="box">180,28</p:property><p:property name="textContent"/><p:property name="textColor">#000000FF</p:property><p:property name="textFont">Tahoma|normal|normal|11px|none|0</p:property><p:property name="textAlign">0,1</p:property><p:property name="status">normal</p:property><p:property name="disabled">false</p:property></p:metadata>

            <rect width="179" height="27" x="0.5" y="0.5" style="opacity: 1; color: rgb(0, 0, 0); fill: rgb(255, 255, 255); fill-opacity: 1; fill-rule: nonzero; stroke: rgb(127, 157, 185); stroke-width: 1; stroke-linecap: square; stroke-linejoin: miter; marker: none; stroke-miterlimit: 4; stroke-dasharray: none; stroke-dashoffset: 0; stroke-opacity: 1; visibility: visible; display: inline; overflow: visible;" p:name="rect" id="39e3970c1192454b936c9f44d4fa9aa5"/>
            <text x="4.561419" y="14.175718" style="text-align: left; text-anchor: start; dominant-baseline: central; fill: rgb(0, 0, 0); fill-opacity: 1; font-family: Tahoma; font-size: 11px; font-weight: normal; font-style: normal; text-decoration: none;" xml:space="preserve" p:name="text" id="46b7aed41c884480bb4cbd3eccd6d7e3" transform="translate(0,0)"> </text>
          </g><g p:type="Shape" p:def="Evolus.WindowsXP.Widgets:TextBox" id="83944797c7f34751b7173be9f3a9ca2c" transform="matrix(1,0,0,1,492.0000073313713,141.0000020712614)"><p:metadata><p:property name="box">180,28</p:property><p:property name="textContent"> QA3-LCD-00*</p:property><p:property name="textColor">#999999FF</p:property><p:property name="textFont">Tahoma|normal|normal|11px|none|0</p:property><p:property name="textAlign">0,1</p:property><p:property name="status">normal</p:property><p:property name="disabled">false</p:property></p:metadata>

            <rect width="179" height="27" x="0.5" y="0.5" style="opacity: 1; color: rgb(0, 0, 0); fill: rgb(255, 255, 255); fill-opacity: 1; fill-rule: nonzero; stroke: rgb(127, 157, 185); stroke-width: 1; stroke-linecap: square; stroke-linejoin: miter; marker: none; stroke-miterlimit: 4; stroke-dasharray: none; stroke-dashoffset: 0; stroke-opacity: 1; visibility: visible; display: inline; overflow: visible;" p:name="rect" id="d99a56073c274131af3e75a84d4e244d"/>
            <text x="4.561419" y="14.175718" style="text-align: left; text-anchor: start; dominant-baseline: central; fill: rgb(153, 153, 153); fill-opacity: 1; font-family: Tahoma; font-size: 11px; font-weight: normal; font-style: normal; text-decoration: none;" xml:space="preserve" p:name="text" id="a699a039d616467d92981c87e7a3a693" transform="translate(0,0)"> QA3-LCD-00*</text>
          </g><g p:type="Shape" p:def="Evolus.WindowsXP.Widgets:Label" id="631d241733984821ae8a2f2eab4e9975" transform="matrix(1,0,0,1,283.0000042170286,126.00000190734863)"><p:metadata><p:property name="disabled">false</p:property><p:property name="label">Scan Id</p:property><p:property name="textColor">#000000FF</p:property><p:property name="textFont">PencilUI|500|normal|13px|none|0</p:property></p:metadata>
              <text y="9.0077095" x="17.999992" style="font-size: 13px; font-style: normal; font-weight: 500; text-align: left; text-anchor: start; dominant-baseline: central; fill: rgb(0, 0, 0); font-family: PencilUI; fill-opacity: 1; text-decoration: none;" xml:space="preserve" p:name="text" id="f73bb3d801124b7c80b859a2ac3ebcf3" transform="translate(-18,-3)">Scan Id</text>
             <path style="fill: none; stroke-width: 1px; visibility: hidden;" p:name="accelIndicator" id="2ba58c224d9e4333be0955a6e1ae7e6d"/>
        </g><g p:type="Shape" p:def="Evolus.WindowsXP.Widgets:Label" id="5ec4407fc9d648648c4669201bddc59d" transform="matrix(1,0,0,1,493.00000734627247,126.00000187754631)"><p:metadata><p:property name="disabled">false</p:property><p:property name="label">Scan Record Name</p:property><p:property name="textColor">#000000FF</p:property><p:property name="textFont">PencilUI|500|normal|13px|none|0</p:property></p:metadata>
              <text y="9.0077095" x="17.999992" style="font-size: 13px; font-style: normal; font-weight: 500; text-align: left; text-anchor: start; dominant-baseline: central; fill: rgb(0, 0, 0); font-family: PencilUI; fill-opacity: 1; text-decoration: none;" xml:space="preserve" p:name="text" id="d8fe2e2d055347ffa616091d23a82651" transform="translate(-18,-3)">Scan Record Name</text>
             <path style="fill: none; stroke-width: 1px; visibility: hidden;" p:name="accelIndicator" id="96ba27c1feab4c9981347d0232d37218"/>
        </g><g p:type="Shape" p:def="Evolus.WindowsXP.Widgets:Button" id="bb07c18e89494074a8c761f105c3d987" transform="matrix(1,0,0,1,701.000010445714,141.00000210106373)"><p:metadata><p:property name="box">80,28</p:property><p:property name="buttonText">Query</p:property><p:property name="textColor">#000000FF</p:property><p:property name="textFont">Tahoma|normal|normal|11px|none|0</p:property><p:property name="textAlign">1,1</p:property><p:property name="disabled">false</p:property><p:property name="defaultButton">false</p:property></p:metadata>
            <defs p:name="defs2177" id="0d2f731a8f0f406db109bf3b1d5997fd">
                <linearGradient x1="0%" y1="0%" x2="0%" y2="100%" p:name="linearGradient4109" id="cbe570b59b3c48888e235432da5c5b9f">
                  <stop style="stop-color:#ffffff;stop-opacity:1" offset="0%" p:name="stop1" id="39e850fd0e444c8a8db2f52310df11ee"/>
                  <stop style="stop-color:#f0f0ea;stop-opacity:1" offset="72%" p:name="stop2" id="b97468d4b5564697b2c2d41872b1e0f2"/>
                  <stop style="stop-color:#d6d0c5;stop-opacity:1" offset="100%" p:name="stop3" id="e92d0d9605f54cff91e03a56739711a1"/>
                </linearGradient>
            </defs>
            <rect width="79" height="27" rx="3" ry="3" x="0.5" y="0.5" style="opacity:1;color:#000000;fill:url(#cbe570b59b3c48888e235432da5c5b9f);fill-opacity:1;fill-rule:nonzero;stroke:#003c74;stroke-width:1;stroke-linecap:square;stroke-linejoin:miter;marker:none;marker-start:none;marker-mid:none;marker-end:none;stroke-miterlimit:4;stroke-dasharray:none;stroke-dashoffset:0;stroke-opacity:1;visibility:visible;display:inline;overflow:visible;enable-background:accumulate" p:name="rect" id="af1791ca9f1d44b2bb081694659816d2"/>
            <rect width="76" height="24" rx="2" ry="2" x="2" y="2" style="opacity: 1; color: rgb(0, 0, 0); fill: none; fill-opacity: 1; fill-rule: nonzero; stroke: rgb(125, 164, 228); stroke-width: 2; stroke-linecap: square; stroke-linejoin: miter; marker: none; stroke-miterlimit: 4; stroke-dasharray: none; stroke-dashoffset: 0; stroke-opacity: 1; visibility: hidden; display: none; overflow: visible;" p:name="defaultMarker" id="66ff2d92c612403294f8cc5ac00fccf4"/>
            <text x="33.415905" y="14.214675" style="text-align: left; text-anchor: start; dominant-baseline: central; fill: rgb(0, 0, 0); fill-opacity: 1; font-family: Tahoma; font-size: 11px; font-weight: normal; font-style: normal; text-decoration: none;" xml:space="preserve" p:name="text" id="55e1a72c4df14e95a7c08835f50fafc0" transform="translate(-8,0)">Query</text>
            <path style="fill: none; stroke-width: 1px; visibility: hidden;" p:name="accelIndicator" id="a1774f7cde304e028635bf3a752e777c"/>
        </g><g p:type="Shape" p:def="Evolus.WindowsXP.Widgets:Groupbox" id="8dfb87cba6f64d04842e66f66f1271b6" transform="matrix(1,0,0,1,264,192.99999999999997)"><p:metadata><p:property name="box">626,356</p:property><p:property name="label">Scan Records</p:property><p:property name="textColor">#000000FF</p:property><p:property name="textFont">PencilUI|500|normal|10px|none|0</p:property></p:metadata>
            <path d="M 76.93301010131836,0.5 L 621.5,0.5 C 624,0.5 625.5,2.2946114 625.5,4.5 L 625.5,351.5 C 625.5,354 624,355.5 621.5,355.5 L 4.5,355.5 C 2.284,355.5 0.5,354 0.5,351.5 L 0.5,4.5106114 C 0.5,2.2946114 2.284,0.51061056 4.5,0.51061056 L 5.5,0.51061056" style="color:#000000;fill:#ffffff;fill-opacity:0;fill-rule:nonzero;stroke:#aca899;stroke-width:1.00000012;stroke-linecap:butt;stroke-linejoin:miter;marker:none;marker-start:none;marker-mid:none;marker-end:none;stroke-miterlimit:4;stroke-dashoffset:0;stroke-opacity:1;visibility:visible;display:inline;overflow:visible;enable-background:accumulate" p:name="rect" id="7a88e030690140cba1c2e3f3ea9d5695"/>
            <text y="9.0077095" x="17.999992" style="font-size: 10px; font-style: normal; font-weight: 500; text-align: left; text-anchor: start; dominant-baseline: central; fill: rgb(0, 0, 0); font-family: PencilUI; fill-opacity: 1; text-decoration: none;" xml:space="preserve" p:name="text" id="fabb12068a554752a275ee67d6723772" transform="translate(-8,-9)">Scan Records</text>
        </g><g p:type="Shape" p:def="Evolus.WindowsXP.Widgets:Button" id="352bf0bcc478461d973ac44ab925df63" transform="matrix(1,0,0,1,790.0000117719173,557.0000082999468)"><p:metadata><p:property name="box">80,28</p:property><p:property name="buttonText">Analysis</p:property><p:property name="textColor">#000000FF</p:property><p:property name="textFont">Tahoma|normal|normal|11px|none|0</p:property><p:property name="textAlign">1,1</p:property><p:property name="disabled">false</p:property><p:property name="defaultButton">false</p:property></p:metadata>
            <defs p:name="defs2177" id="20f643ee50f24795915e1087a94995bf">
                <linearGradient x1="0%" y1="0%" x2="0%" y2="100%" p:name="linearGradient4109" id="ce4dd61e73a34287bc9daf03dba6ce0a">
                  <stop style="stop-color:#ffffff;stop-opacity:1" offset="0%" p:name="stop1" id="3e5909a252db40a29c74122adc0be8de"/>
                  <stop style="stop-color:#f0f0ea;stop-opacity:1" offset="72%" p:name="stop2" id="d31b454f466b45b299277d356ee207d1"/>
                  <stop style="stop-color:#d6d0c5;stop-opacity:1" offset="100%" p:name="stop3" id="a1dcb5d286e042158652f9e2b2564793"/>
                </linearGradient>
            </defs>
            <rect width="79" height="27" rx="3" ry="3" x="0.5" y="0.5" style="opacity:1;color:#000000;fill:url(#ce4dd61e73a34287bc9daf03dba6ce0a);fill-opacity:1;fill-rule:nonzero;stroke:#003c74;stroke-width:1;stroke-linecap:square;stroke-linejoin:miter;marker:none;marker-start:none;marker-mid:none;marker-end:none;stroke-miterlimit:4;stroke-dasharray:none;stroke-dashoffset:0;stroke-opacity:1;visibility:visible;display:inline;overflow:visible;enable-background:accumulate" p:name="rect" id="c7a1f2c6bb5541f7ac571485e7e4ecd9"/>
            <rect width="76" height="24" rx="2" ry="2" x="2" y="2" style="opacity: 1; color: rgb(0, 0, 0); fill: none; fill-opacity: 1; fill-rule: nonzero; stroke: rgb(125, 164, 228); stroke-width: 2; stroke-linecap: square; stroke-linejoin: miter; marker: none; stroke-miterlimit: 4; stroke-dasharray: none; stroke-dashoffset: 0; stroke-opacity: 1; visibility: hidden; display: none; overflow: visible;" p:name="defaultMarker" id="3ba6a3bb4d794baca4fff6cd9efe1f38"/>
            <text x="33.415905" y="14.214675" style="text-align: left; text-anchor: start; dominant-baseline: central; fill: rgb(0, 0, 0); fill-opacity: 1; font-family: Tahoma; font-size: 11px; font-weight: normal; font-style: normal; text-decoration: none;" xml:space="preserve" p:name="text" id="b74596f2badf4879a26cd0f81121587d" transform="translate(-13,0)">Analysis</text>
            <path style="fill: none; stroke-width: 1px; visibility: hidden;" p:name="accelIndicator" id="b6f61d29729243d18939cc6cd3ed7e01"/>
        </g><g p:type="Shape" p:def="Evolus.BasicWebElements:table" id="f0bf8be75bc34adeaab2b92bc5179a77" transform="matrix(1,0,0,1,283.0000042170286,215.00000320374963)"><p:metadata><p:property name="box">350,143</p:property><p:property name="useHtmlContent">false</p:property><p:property name="fixedHeaderHeight">true</p:property><p:property name="padding">0,4</p:property><p:property name="headerHeight">0,30</p:property><p:property name="h0">30,0</p:property><p:property name="h1">120,0</p:property><p:property name="h2">220,0</p:property><p:property name="h3">260,0</p:property><p:property name="h4">270,0</p:property><p:property name="h5">280,0</p:property><p:property name="h6">290,0</p:property><p:property name="h7">290,0</p:property><p:property name="h8">290,0</p:property><p:property name="h9">290,0</p:property><p:property name="h10">290,0</p:property><p:property name="h11">290,0</p:property><p:property name="content">ID | Exam ID | Name
1 | 2 | QA3-LCD-001
2 | 3 | Bay36 maxima test
3 | 0 | QA3-ALL-001</p:property><p:property name="textFont">"Liberation Sans",Arial,sans-serif|normal|normal|13px|none|0</p:property><p:property name="textAlign">0,1</p:property><p:property name="customStyle"/><p:property name="textColor">"Liberation Sans",Arial,sans-serif|normal|normal|13px|none|0</p:property><p:property name="fillColor">#00000000</p:property><p:property name="headerTextColor">#000000FF</p:property><p:property name="headerBackground">#CCCCCCFF</p:property><p:property name="strokeColor">#00000055</p:property><p:property name="strokeStyle">1|</p:property></p:metadata>
            <foreignObject x="0" y="0" width="351" height="144" p:name="htmlObject" id="be301b69584f45bfa0a35785f6a2496f">
                <div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; width: 350px; height: 143px; color: rgb(0, 0, 0); font-family: &quot;Liberation Sans&quot;, Arial, sans-serif; font-size: 13px; font-weight: normal; font-style: normal; text-decoration: none;" p:name="textDiv" id="719d829a116549b6b17e5c5224ec0029"><table style="border-collapse: collapse; background-color: rgba(0, 0, 0, 0); width: 349px; height: 142px; border: solid 1px rgba(0, 0, 0, 0.33); border-width: 0px 1px 1px 0px"><thead><tr style="height: 31px; min-height: 0px"><th style="border: solid 1px rgba(0, 0, 0, 0.33); border-width: 1px 0px 0px 1px; padding: 0px; background-color: rgba(204, 204, 204, 1); color: rgba(0, 0, 0, 1); text-align: left; vertical-align: middle; min-width: 0px; min-height: 0px; height: 31px; overflow: hidden; -moz-box-sizing: border-box; box-sizing: border-box; width: 28px"><div style="overflow: hidden; min-width: 0px; -moz-box-sizing: border-box; box-sizing: border-box; padding: 4px; width: 28px">ID </div></th><th style="border: solid 1px rgba(0, 0, 0, 0.33); border-width: 1px 0px 0px 1px; padding: 0px; background-color: rgba(204, 204, 204, 1); color: rgba(0, 0, 0, 1); text-align: left; vertical-align: middle; min-width: 0px; min-height: 0px; height: 31px; overflow: hidden; -moz-box-sizing: border-box; box-sizing: border-box; width: 88px"><div style="overflow: hidden; min-width: 0px; -moz-box-sizing: border-box; box-sizing: border-box; padding: 4px; width: 88px"> Exam ID </div></th><th style="border: solid 1px rgba(0, 0, 0, 0.33); border-width: 1px 0px 0px 1px; padding: 0px; background-color: rgba(204, 204, 204, 1); color: rgba(0, 0, 0, 1); text-align: left; vertical-align: middle; min-width: 0px; min-height: 0px; height: 31px; overflow: hidden; -moz-box-sizing: border-box; box-sizing: border-box; width: 227px"><div style="overflow: hidden; min-width: 0px; -moz-box-sizing: border-box; box-sizing: border-box; padding: 4px; width: 227px"> Name</div></th></tr></thead><tbody><tr style="height: 20px; min-height: 0px"><td style="border: solid 1px rgba(0, 0, 0, 0.33); border-width: 1px 0px 0px 1px; padding: 0px; text-align: left; vertical-align: middle; min-width: 0px; min-height: 0px; height: undefinedpx; overflow: hidden; -moz-box-sizing: border-box; box-sizing: border-box; width: 28px"><div style="overflow: hidden; min-width: 0px; -moz-box-sizing: border-box; box-sizing: border-box; padding: 4px; width: 28px">1 </div></td><td style="border: solid 1px rgba(0, 0, 0, 0.33); border-width: 1px 0px 0px 1px; padding: 0px; text-align: left; vertical-align: middle; min-width: 0px; min-height: 0px; height: undefinedpx; overflow: hidden; -moz-box-sizing: border-box; box-sizing: border-box; width: 88px"><div style="overflow: hidden; min-width: 0px; -moz-box-sizing: border-box; box-sizing: border-box; padding: 4px; width: 88px"> 2 </div></td><td style="border: solid 1px rgba(0, 0, 0, 0.33); border-width: 1px 0px 0px 1px; padding: 0px; text-align: left; vertical-align: middle; min-width: 0px; min-height: 0px; height: undefinedpx; overflow: hidden; -moz-box-sizing: border-box; box-sizing: border-box; width: 227px"><div style="overflow: hidden; min-width: 0px; -moz-box-sizing: border-box; box-sizing: border-box; padding: 4px; width: 227px"> QA3-LCD-001</div></td></tr><tr style="height: 20px; min-height: 0px"><td style="border: solid 1px rgba(0, 0, 0, 0.33); border-width: 1px 0px 0px 1px; padding: 0px; text-align: left; vertical-align: middle; min-width: 0px; min-height: 0px; height: undefinedpx; overflow: hidden; -moz-box-sizing: border-box; box-sizing: border-box; width: 28px"><div style="overflow: hidden; min-width: 0px; -moz-box-sizing: border-box; box-sizing: border-box; padding: 4px; width: 28px">2 </div></td><td style="border: solid 1px rgba(0, 0, 0, 0.33); border-width: 1px 0px 0px 1px; padding: 0px; text-align: left; vertical-align: middle; min-width: 0px; min-height: 0px; height: undefinedpx; overflow: hidden; -moz-box-sizing: border-box; box-sizing: border-box; width: 88px"><div style="overflow: hidden; min-width: 0px; -moz-box-sizing: border-box; box-sizing: border-box; padding: 4px; width: 88px"> 3 </div></td><td style="border: solid 1px rgba(0, 0, 0, 0.33); border-width: 1px 0px 0px 1px; padding: 0px; text-align: left; vertical-align: middle; min-width: 0px; min-height: 0px; height: undefinedpx; overflow: hidden; -moz-box-sizing: border-box; box-sizing: border-box; width: 227px"><div style="overflow: hidden; min-width: 0px; -moz-box-sizing: border-box; box-sizing: border-box; padding: 4px; width: 227px"> Bay36 maxima test</div></td></tr><tr style="height: 20px; min-height: 0px"><td style="border: solid 1px rgba(0, 0, 0, 0.33); border-width: 1px 0px 0px 1px; padding: 0px; text-align: left; vertical-align: middle; min-width: 0px; min-height: 0px; height: undefinedpx; overflow: hidden; -moz-box-sizing: border-box; box-sizing: border-box; width: 28px"><div style="overflow: hidden; min-width: 0px; -moz-box-sizing: border-box; box-sizing: border-box; padding: 4px; width: 28px">3 </div></td><td style="border: solid 1px rgba(0, 0, 0, 0.33); border-width: 1px 0px 0px 1px; padding: 0px; text-align: left; vertical-align: middle; min-width: 0px; min-height: 0px; height: undefinedpx; overflow: hidden; -moz-box-sizing: border-box; box-sizing: border-box; width: 88px"><div style="overflow: hidden; min-width: 0px; -moz-box-sizing: border-box; box-sizing: border-box; padding: 4px; width: 88px"> 0 </div></td><td style="border: solid 1px rgba(0, 0, 0, 0.33); border-width: 1px 0px 0px 1px; padding: 0px; text-align: left; vertical-align: middle; min-width: 0px; min-height: 0px; height: undefinedpx; overflow: hidden; -moz-box-sizing: border-box; box-sizing: border-box; width: 227px"><div style="overflow: hidden; min-width: 0px; -moz-box-sizing: border-box; box-sizing: border-box; padding: 4px; width: 227px"> QA3-ALL-001</div></td></tr></tbody></table></div>
		<!-- <div xmlns="http://www.w3.org/1999/xhtml" style="position: absolute; top: 0px; left: 0px; width: 100%; height: 100%;"/> -->
            </foreignObject>
        </g><g p:type="Shape" p:def="Evolus.BasicWebElements:table" id="9eb57f46ec7d474b9864a64110a9522a" transform="matrix(1,0,0,1,283.0000042170286,366.0000053346157)"><p:metadata><p:property name="box">350,127</p:property><p:property name="useHtmlContent">false</p:property><p:property name="fixedHeaderHeight">true</p:property><p:property name="padding">0,4</p:property><p:property name="headerHeight">0,30</p:property><p:property name="h0">30,0</p:property><p:property name="h1">120,0</p:property><p:property name="h2">220,0</p:property><p:property name="h3">260,0</p:property><p:property name="h4">270,0</p:property><p:property name="h5">280,0</p:property><p:property name="h6">290,0</p:property><p:property name="h7">290,0</p:property><p:property name="h8">290,0</p:property><p:property name="h9">290,0</p:property><p:property name="h10">290,0</p:property><p:property name="h11">290,0</p:property><p:property name="content">ID | Description | Image Count
2 | LCD | 8
3 | NOISE | 6
5 | NOISE | 8</p:property><p:property name="textFont">"Liberation Sans",Arial,sans-serif|normal|normal|13px|none|0</p:property><p:property name="textAlign">0,1</p:property><p:property name="customStyle"/><p:property name="textColor">"Liberation Sans",Arial,sans-serif|normal|normal|13px|none|0</p:property><p:property name="fillColor">#00000000</p:property><p:property name="headerTextColor">#000000FF</p:property><p:property name="headerBackground">#CCCCCCFF</p:property><p:property name="strokeColor">#00000055</p:property><p:property name="strokeStyle">1|</p:property></p:metadata>
            <foreignObject x="0" y="0" width="351" height="128" p:name="htmlObject" id="fa5cc39c3694436bab31fbaf1ceeaa0e">
                <div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; width: 350px; height: 127px; color: rgb(0, 0, 0); font-family: &quot;Liberation Sans&quot;, Arial, sans-serif; font-size: 13px; font-weight: normal; font-style: normal; text-decoration: none;" p:name="textDiv" id="43408c8c1e0a4796afc7803b2d5923a8"><table style="border-collapse: collapse; background-color: rgba(0, 0, 0, 0); width: 349px; height: 126px; border: solid 1px rgba(0, 0, 0, 0.33); border-width: 0px 1px 1px 0px"><thead><tr style="height: 30px; min-height: 0px"><th style="border: solid 1px rgba(0, 0, 0, 0.33); border-width: 1px 0px 0px 1px; padding: 0px; background-color: rgba(204, 204, 204, 1); color: rgba(0, 0, 0, 1); text-align: left; vertical-align: middle; min-width: 0px; min-height: 0px; height: 30px; overflow: hidden; -moz-box-sizing: border-box; box-sizing: border-box; width: 28px"><div style="overflow: hidden; min-width: 0px; -moz-box-sizing: border-box; box-sizing: border-box; padding: 4px; width: 28px">ID </div></th><th style="border: solid 1px rgba(0, 0, 0, 0.33); border-width: 1px 0px 0px 1px; padding: 0px; background-color: rgba(204, 204, 204, 1); color: rgba(0, 0, 0, 1); text-align: left; vertical-align: middle; min-width: 0px; min-height: 0px; height: 30px; overflow: hidden; -moz-box-sizing: border-box; box-sizing: border-box; width: 88px"><div style="overflow: hidden; min-width: 0px; -moz-box-sizing: border-box; box-sizing: border-box; padding: 4px; width: 88px"> Description </div></th><th style="border: solid 1px rgba(0, 0, 0, 0.33); border-width: 1px 0px 0px 1px; padding: 0px; background-color: rgba(204, 204, 204, 1); color: rgba(0, 0, 0, 1); text-align: left; vertical-align: middle; min-width: 0px; min-height: 0px; height: 30px; overflow: hidden; -moz-box-sizing: border-box; box-sizing: border-box; width: 227px"><div style="overflow: hidden; min-width: 0px; -moz-box-sizing: border-box; box-sizing: border-box; padding: 4px; width: 227px"> Image Count</div></th></tr></thead><tbody><tr style="height: 20px; min-height: 0px"><td style="border: solid 1px rgba(0, 0, 0, 0.33); border-width: 1px 0px 0px 1px; padding: 0px; text-align: left; vertical-align: middle; min-width: 0px; min-height: 0px; height: undefinedpx; overflow: hidden; -moz-box-sizing: border-box; box-sizing: border-box; width: 28px"><div style="overflow: hidden; min-width: 0px; -moz-box-sizing: border-box; box-sizing: border-box; padding: 4px; width: 28px">2 </div></td><td style="border: solid 1px rgba(0, 0, 0, 0.33); border-width: 1px 0px 0px 1px; padding: 0px; text-align: left; vertical-align: middle; min-width: 0px; min-height: 0px; height: undefinedpx; overflow: hidden; -moz-box-sizing: border-box; box-sizing: border-box; width: 88px"><div style="overflow: hidden; min-width: 0px; -moz-box-sizing: border-box; box-sizing: border-box; padding: 4px; width: 88px"> LCD </div></td><td style="border: solid 1px rgba(0, 0, 0, 0.33); border-width: 1px 0px 0px 1px; padding: 0px; text-align: left; vertical-align: middle; min-width: 0px; min-height: 0px; height: undefinedpx; overflow: hidden; -moz-box-sizing: border-box; box-sizing: border-box; width: 227px"><div style="overflow: hidden; min-width: 0px; -moz-box-sizing: border-box; box-sizing: border-box; padding: 4px; width: 227px"> 8</div></td></tr><tr style="height: 20px; min-height: 0px"><td style="border: solid 1px rgba(0, 0, 0, 0.33); border-width: 1px 0px 0px 1px; padding: 0px; text-align: left; vertical-align: middle; min-width: 0px; min-height: 0px; height: undefinedpx; overflow: hidden; -moz-box-sizing: border-box; box-sizing: border-box; width: 28px"><div style="overflow: hidden; min-width: 0px; -moz-box-sizing: border-box; box-sizing: border-box; padding: 4px; width: 28px">3 </div></td><td style="border: solid 1px rgba(0, 0, 0, 0.33); border-width: 1px 0px 0px 1px; padding: 0px; text-align: left; vertical-align: middle; min-width: 0px; min-height: 0px; height: undefinedpx; overflow: hidden; -moz-box-sizing: border-box; box-sizing: border-box; width: 88px"><div style="overflow: hidden; min-width: 0px; -moz-box-sizing: border-box; box-sizing: border-box; padding: 4px; width: 88px"> NOISE </div></td><td style="border: solid 1px rgba(0, 0, 0, 0.33); border-width: 1px 0px 0px 1px; padding: 0px; text-align: left; vertical-align: middle; min-width: 0px; min-height: 0px; height: undefinedpx; overflow: hidden; -moz-box-sizing: border-box; box-sizing: border-box; width: 227px"><div style="overflow: hidden; min-width: 0px; -moz-box-sizing: border-box; box-sizing: border-box; padding: 4px; width: 227px"> 6</div></td></tr><tr style="height: 20px; min-height: 0px"><td style="border: solid 1px rgba(0, 0, 0, 0.33); border-width: 1px 0px 0px 1px; padding: 0px; text-align: left; vertical-align: middle; min-width: 0px; min-height: 0px; height: undefinedpx; overflow: hidden; -moz-box-sizing: border-box; box-sizing: border-box; width: 28px"><div style="overflow: hidden; min-width: 0px; -moz-box-sizing: border-box; box-sizing: border-box; padding: 4px; width: 28px">5 </div></td><td style="border: solid 1px rgba(0, 0, 0, 0.33); border-width: 1px 0px 0px 1px; padding: 0px; text-align: left; vertical-align: middle; min-width: 0px; min-height: 0px; height: undefinedpx; overflow: hidden; -moz-box-sizing: border-box; box-sizing: border-box; width: 88px"><div style="overflow: hidden; min-width: 0px; -moz-box-sizing: border-box; box-sizing: border-box; padding: 4px; width: 88px"> NOISE </div></td><td style="border: solid 1px rgba(0, 0, 0, 0.33); border-width: 1px 0px 0px 1px; padding: 0px; text-align: left; vertical-align: middle; min-width: 0px; min-height: 0px; height: undefinedpx; overflow: hidden; -moz-box-sizing: border-box; box-sizing: border-box; width: 227px"><div style="overflow: hidden; min-width: 0px; -moz-box-sizing: border-box; box-sizing: border-box; padding: 4px; width: 227px"> 8</div></td></tr></tbody></table></div>
		<!-- <div xmlns="http://www.w3.org/1999/xhtml" style="position: absolute; top: 0px; left: 0px; width: 100%; height: 100%;"/> -->
            </foreignObject>
        </g><g p:type="Shape" p:def="Evolus.Sketchy.GUI:lineV2" p:sc="Horizontal Line" id="bc557910abc544858a1952b2291d05b3" transform="matrix(1,0,0,1,26,55)"><p:metadata><p:property name="startLine">0,0</p:property><p:property name="endLine">76,0</p:property><p:property name="mode">horizontal</p:property><p:property name="strokeColor">#FF0000FF</p:property><p:property name="strokeStyle">2|</p:property></p:metadata>
            <g p:name="rect" id="4d33faa5224846eebfa07d758f235db9" transform="translate(0,0)" style="stroke: rgb(255, 0, 0); stroke-opacity: 1; stroke-width: 2;">
    			<path style="stroke: #FFFFFF; stroke-opacity: 0; stroke-width: 8px; fill: none;" p:name="bg" id="e675c3d8ed8845a8bba67c0a7dca3f3b" transform="translate(0,0)" d="M 0 0 C 25 0 51 0 76 0"/>
    			<path style="stroke-linejoin: round; fill: none;" p:name="line1" id="42818509bb9342ee86a2a91530a899ea" transform="translate(0,0)" d="M 0 0 C 25 -1 51 -1 76 0"/>
    		</g>
        </g></g></g></svg>