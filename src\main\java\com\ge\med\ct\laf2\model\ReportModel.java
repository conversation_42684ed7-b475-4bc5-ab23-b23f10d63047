package com.ge.med.ct.laf2.model;

import com.ge.med.ct.laf2.base.BaseModel;
import com.ge.med.ct.laf2.components.CTListView;

import java.util.List;

public class ReportModel extends BaseModel {
    private static final String PROP_SELECTED_ITEM = "selectedItem";
    private static final String PROP_REPORT_CONTENT = "reportContent";
    private static final String PROP_IMAGE_PATHS = "imagePaths";
    private static final String PROP_HAS_DATA = "hasData";
    private static final String PROP_LOADING = "loading";
    private static final String PROP_ERROR = "error";

    private CTListView.ListItem selectedItem;
    private List<String> imagePaths;

    private String reportContent;
    private String error;

    private boolean hasData;
    private boolean isLoading;

    public void setSelectedItem(CTListView.ListItem item) {
        CTListView.ListItem oldValue = this.selectedItem;
        this.selectedItem = item;
        firePropertyChange(PROP_SELECTED_ITEM, oldValue, item);

        if (item != null) {
            setImagePaths(item.getImagePaths());
            setReportContent(item.getReport());
        }
    }

    public CTListView.ListItem getSelectedItem() {
        return selectedItem;
    }

    public void setReportContent(String content) {
        String oldValue = this.reportContent;
        this.reportContent = content;
        firePropertyChange(PROP_REPORT_CONTENT, oldValue, content);
    }

    public String getReportContent() {
        return reportContent;
    }

    public void setImagePaths(List<String> paths) {
        List<String> oldValue = this.imagePaths;
        this.imagePaths = paths;
        firePropertyChange(PROP_IMAGE_PATHS, oldValue, paths);
    }

    public List<String> getImagePaths() {
        return imagePaths;
    }

    public void setHasData(boolean hasData) {
        boolean oldValue = this.hasData;
        this.hasData = hasData;
        firePropertyChange(PROP_HAS_DATA, oldValue, hasData);
    }

    public boolean hasData() {
        return hasData;
    }

    public void setLoading(boolean loading) {
        boolean oldValue = this.isLoading;
        this.isLoading = loading;
        firePropertyChange(PROP_LOADING, oldValue, loading);
    }

    public boolean isLoading() {
        return isLoading;
    }

    public void setError(String error) {
        String oldValue = this.error;
        this.error = error;
        firePropertyChange(PROP_ERROR, oldValue, error);
    }

    public String getError() {
        return error;
    }

    public void resetContents() {
        setSelectedItem(null);
        setReportContent("");
        setImagePaths(null);
        setHasData(false);
        setLoading(false);
        setError(null);
    }
}