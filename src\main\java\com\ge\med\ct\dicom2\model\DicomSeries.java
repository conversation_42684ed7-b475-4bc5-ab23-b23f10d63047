package com.ge.med.ct.dicom2.model;

import com.ge.med.ct.dicom2.service.DicomTagService;
import com.ge.med.ct.dicom2.tag.DicomTag;
import com.ge.med.ct.dicom2.tag.DicomTagConstants;
import com.ge.med.ct.exception.aspect.HandleException;
import com.ge.med.ct.exception.code.ErrorCode;
import com.ge.med.ct.exception.core.DicomException;
import com.ge.med.ct.exception.factory.ExceptionFactory;
import com.ge.med.ct.exception.message.DicomMessages;

import java.util.*;
import java.util.logging.Logger;

// DICOM序列类
public class DicomSeries {
    private static final Logger LOG = Logger.getLogger(DicomSeries.class.getName());

    private final String id;
    private final Map<String, DicomTag> tags;
    private final List<DicomImage> images;
    private DicomExam exam;
    private String seriesNumber;
    private String stationName;
    private String type;
    private int imageCount;

    public DicomSeries(String id) throws DicomException {
        if (id == null || id.trim().isEmpty()) {
            throw ExceptionFactory.createDicomException(ErrorCode.TAG_PROCESSING, DicomMessages.ID_EMPTY);
        }
        this.id = id;
        this.tags = new HashMap<>();
        this.images = new ArrayList<>();
    }

    public String getId() {
        return id;
    }

    public DicomTag getTag(String tagId) {
        return tags.get(tagId);
    }

    public void addTag(String tagId, DicomTag tag) {
        if (tagId != null && tag != null) {
            tags.put(tagId, tag);
            LOG.fine("添加标签 " + tagId + " 到序列 " + id);
        }
    }

    public String getTagValue(String tagId) {
        DicomTag tag = getTag(tagId);
        return tag != null ? tag.getValueAsString() : null;
    }

    // 序列标识符
    public String getSeriesInstanceUID() {
        return getTagValue(DicomTagConstants.Series.SERIES_INSTANCE_UID);
    }

    public void setSeriesInstanceUID(String uid) throws DicomException {
        setTagValue(DicomTagConstants.Series.SERIES_INSTANCE_UID, uid);
    }

    public String getSeriesNumber() {
        return seriesNumber;
    }

    @HandleException(errorCode = ErrorCode.DICOM_VALIDATION)
    public void setSeriesNumber(String seriesNumber) throws DicomException {
        if (seriesNumber == null) {
            throw ExceptionFactory.createDicomException(ErrorCode.DICOM_VALIDATION,
                    DicomMessages.VALIDATION_ERROR, "序列号不能为空");
        }
        this.seriesNumber = seriesNumber;
    }

    public String getSeriesDescription() {
        return getTagValue(DicomTagConstants.Series.SERIES_DESCRIPTION);
    }

    public void setSeriesDescription(String description) throws DicomException {
        setTagValue(DicomTagConstants.Series.SERIES_DESCRIPTION, description);
    }

    public String getModality() {
        return getTagValue(DicomTagConstants.Series.MODALITY);
    }

    public void setModality(String modality) throws DicomException {
        setTagValue(DicomTagConstants.Series.MODALITY, modality);
    }

    public String getSeriesDate() {
        return getTagValue(DicomTagConstants.Series.SERIES_DATE);
    }

    public void setSeriesDate(String date) throws DicomException {
        setTagValue(DicomTagConstants.Series.SERIES_DATE, date);
    }

    public String getSeriesTime() {
        return getTagValue(DicomTagConstants.Series.SERIES_TIME);
    }

    public void setSeriesTime(String time) throws DicomException {
        setTagValue(DicomTagConstants.Series.SERIES_TIME, time);
    }

    public String getSeriesStatus() {
        return getTagValue(DicomTagConstants.Series.SERIES_STATUS);
    }

    public void setSeriesStatus(String status) throws DicomException {
        setTagValue(DicomTagConstants.Series.SERIES_STATUS, status);
    }

    public String getSeriesPriority() {
        return getTagValue(DicomTagConstants.Series.SERIES_PRIORITY);
    }

    public void setSeriesPriority(String priority) throws DicomException {
        setTagValue(DicomTagConstants.Series.SERIES_PRIORITY, priority);
    }

    /**
     * 设置序列状态和优先级
     */
    public void setSeriesStatusAndPriority(String status, String priority) throws DicomException {
        setTagValue(DicomTagConstants.Series.SERIES_STATUS, status);
        setTagValue(DicomTagConstants.Series.SERIES_PRIORITY, priority);
    }

    public String getSeriesCompletionDate() {
        return getTagValue(DicomTagConstants.Series.SERIES_COMPLETION_DATE);
    }

    public void setSeriesCompletionDate(String date) throws DicomException {
        setTagValue(DicomTagConstants.Series.SERIES_COMPLETION_DATE, date);
    }

    public String getSeriesCompletionTime() {
        return getTagValue(DicomTagConstants.Series.SERIES_COMPLETION_TIME);
    }

    public void setSeriesCompletionTime(String time) throws DicomException {
        setTagValue(DicomTagConstants.Series.SERIES_COMPLETION_TIME, time);
    }

    /**
     * 设置序列完成日期和时间
     */
    public void setSeriesCompletion(String date, String time) throws DicomException {
        setTagValue(DicomTagConstants.Series.SERIES_COMPLETION_DATE, date);
        setTagValue(DicomTagConstants.Series.SERIES_COMPLETION_TIME, time);
    }

    public String getSeriesVerifiedDate() {
        return getTagValue(DicomTagConstants.Series.SERIES_VERIFIED_DATE);
    }

    public void setSeriesVerifiedDate(String date) throws DicomException {
        setTagValue(DicomTagConstants.Series.SERIES_VERIFIED_DATE, date);
    }

    public String getSeriesVerifiedTime() {
        return getTagValue(DicomTagConstants.Series.SERIES_VERIFIED_TIME);
    }

    public void setSeriesVerifiedTime(String time) throws DicomException {
        setTagValue(DicomTagConstants.Series.SERIES_VERIFIED_TIME, time);
    }

    /**
     * 设置序列验证日期和时间
     */
    public void setSeriesVerification(String date, String time) throws DicomException {
        setTagValue(DicomTagConstants.Series.SERIES_VERIFIED_DATE, date);
        setTagValue(DicomTagConstants.Series.SERIES_VERIFIED_TIME, time);
    }

    /**
     * 获取序列完成日期和时间的组合
     *
     * @return 格式化的日期时间字符串，如果任一值为空则返回空字符串
     */
    public String getSeriesCompletionDateTime() {
        String date = getSeriesCompletionDate();
        String time = getSeriesCompletionTime();
        if (date == null || time == null || date.isEmpty() || time.isEmpty()) {
            return "";
        }
        return formatDateTime(date, time);
    }

    /**
     * 获取序列验证日期和时间的组合
     *
     * @return 格式化的日期时间字符串，如果任一值为空则返回空字符串
     */
    public String getSeriesVerifiedDateTime() {
        String date = getSeriesVerifiedDate();
        String time = getSeriesVerifiedTime();
        if (date == null || time == null || date.isEmpty() || time.isEmpty()) {
            return "";
        }
        return formatDateTime(date, time);
    }

    /**
     * 格式化DICOM日期和时间为可读格式
     *
     * @param date DICOM日期格式 (YYYYMMDD)
     * @param time DICOM时间格式 (HHMMSS)
     * @return 格式化的日期时间字符串
     */
    private String formatDateTime(String date, String time) {
        if (date.length() >= 8 && time.length() >= 6) {
            return date.substring(0, 4) + "-" + date.substring(4, 6) + "-" + date.substring(6, 8) + " " +
                    time.substring(0, 2) + ":" + time.substring(2, 4) + ":" + time.substring(4, 6);
        }
        return date + " " + time;
    }

    @HandleException(errorCode = ErrorCode.TAG_PROCESSING)
    private void setTagValue(String tagId, String value) throws DicomException {
        DicomTag tag = getTag(tagId);
        if (tag != null) {
            tag = tag.withValue(value);
            tags.put(tagId, tag);
        } else {
            tag = new DicomTag(tagId, DicomTagService.getTagName(tagId), value,
                    DicomTagService.getTagVR(tagId), "动态标签 " + tagId, false);
            addTag(tagId, tag);
        }
    }

    public void addImage(DicomImage image) {
        if (image != null) {
            images.add(image);
            LOG.fine("添加图像 " + image.getId() + " 到序列 " + id);
        }
    }

    public List<DicomImage> getImages() {
        return Collections.unmodifiableList(images);
    }

    public int getImageCount() {
        return imageCount;
    }

    public DicomExam getExam() {
        return exam;
    }

    public void setExam(DicomExam exam) {
        if (this.exam != exam) {
            DicomExam oldExam = this.exam;
            this.exam = exam;

            if (oldExam != null) {
                oldExam.removeSeries(this);
            }

            if (exam != null && !exam.getSeries().contains(this)) {
                exam.addSeries(this);
            }
        }
    }

    public Map<String, DicomTag> getTags() {
        return Collections.unmodifiableMap(tags);
    }

    public String getStationName() {
        return stationName;
    }

    public void setStationName(String stationName) {
        this.stationName = stationName;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public void setImageCount(int imageCount) {
        this.imageCount = imageCount;
    }

    @Override
    public String toString() {
        return String.format(
                "%s[id=%s, seriesInstanceUID=%s, modality=%s, seriesNumber=%s, seriesDescription=%s, imageCount=%d, exam=%s]",
                getClass().getSimpleName(), id, getSeriesInstanceUID(), getModality(), getSeriesNumber(),
                getSeriesDescription(), imageCount, exam != null ? exam.getId() : "null");
    }

    @Override
    public boolean equals(Object o) {
        if (this == o)
            return true;
        if (o == null || getClass() != o.getClass())
            return false;
        DicomSeries that = (DicomSeries) o;
        return id.equals(that.id);
    }

    @Override
    public int hashCode() {
        return id.hashCode();
    }
}