package com.ge.med.ct.dicom2.utils;

import java.util.List;
import java.util.Map;

/**
 * 预扫描结果类
 */
public class PreScanResult {

    private final List<String> emptyExamFiles;
    private final List<String> invalidFiles;

    private final Map<String, List<String>> examToFilesMap;
    private final Map<String, Map<String, List<String>>> examToSeriesMap;

    public PreScanResult(
            Map<String, List<String>> examToFilesMap,
            Map<String, Map<String, List<String>>> examToSeriesMap,
            List<String> emptyExamFiles,
            List<String> invalidFiles) {
        this.examToFilesMap = examToFilesMap;
        this.examToSeriesMap = examToSeriesMap;
        this.emptyExamFiles = emptyExamFiles;
        this.invalidFiles = invalidFiles;
    }

    public Map<String, List<String>> getExamToFilesMap() {
        return examToFilesMap;
    }

    public Map<String, Map<String, List<String>>> getExamToSeriesMap() {
        return examToSeriesMap;
    }

    public List<String> getEmptyExamFiles() {
        return emptyExamFiles;
    }

    public List<String> getInvalidFiles() {
        return invalidFiles;
    }

    public int getExamCount() {
        return examToFilesMap.size();
    }

    public int getSeriesCount() {
        return examToSeriesMap.values().stream()
                .mapToInt(Map::size)
                .sum();
    }

    public int getTotalFileCount() {
        return examToFilesMap.values().stream()
                .mapToInt(List::size)
                .sum() + emptyExamFiles.size() + invalidFiles.size();
    }

    public int getValidFileCount() {
        return examToFilesMap.values().stream()
                .mapToInt(List::size)
                .sum();
    }
}
