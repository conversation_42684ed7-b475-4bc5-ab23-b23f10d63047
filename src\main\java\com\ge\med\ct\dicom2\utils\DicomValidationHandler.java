package com.ge.med.ct.dicom2.utils;

import com.ge.med.ct.cfg.ConfigManager;
import com.ge.med.ct.dicom2.model.DicomFileModel;
import com.ge.med.ct.dicom2.service.DicomFileValidator;
import com.ge.med.ct.exception.code.ErrorCode;
import com.ge.med.ct.exception.core.DicomException;
import com.ge.med.ct.exception.message.DicomMessages;
import com.ge.med.ct.service.LogManager;

import java.io.File;
import java.util.*;
import java.util.logging.Logger;

/**
 * DICOM验证处理器 - 负责文件验证、错误处理和报告生成
 */
public class DicomValidationHandler {
    private static final Logger LOG = LogManager.getInstance().getLogger(DicomValidationHandler.class);

    private final DicomFileValidator validationService;
    private final boolean skipInvalidFiles;
    private final boolean enableDetailedLogging;
    private final boolean enableGroupedReporting;
    private final int maxFileExamples;

    // 按错误类型分组的文件列表
    private final Map<String, List<String>> errorTypeFileMap = new HashMap<>();

    public DicomValidationHandler(ConfigManager configManager,
            DicomFileValidator validationService,
            boolean skipInvalidFiles) {
        this.validationService = validationService;
        this.skipInvalidFiles = skipInvalidFiles;
        this.enableDetailedLogging = configManager.getBoolean("dicom.validation.detailed_warnings", true);
        this.enableGroupedReporting = configManager.getBoolean("dicom.validation.group_warnings", true);
        this.maxFileExamples = configManager.getInt("dicom.validation.max_examples", 10);
    }

    /**
     * 验证DICOM文件
     *
     * @param filePath DICOM文件路径
     * @param model    DICOM文件模型
     * @throws DicomException 如果验证失败
     */
    public void validateFile(String filePath, DicomFileModel model) throws DicomException {
        // 基础验证
        DicomFileValidator.validateFilePath(filePath);

        if (model == null) {
            String fileName = new File(filePath).getName();
            addError(filePath, "READ_ERROR", "无法读取文件");
            throw new DicomException(ErrorCode.READ, DicomMessages.FILE_READ_ERROR, fileName);
        }

        // 执行DICOM验证
        try {
            ValidationResult result = validationService.validateFile(filePath, model);
            if (!result.isValid()) {
                onError(filePath, result);
            }
        } catch (DicomException e) {
            throw e;
        } catch (Exception e) {
            onException(filePath, e);
        }
    }

    /**
     * 添加错误记录
     */
    public void addError(String filePath, String errorType, String errorMessage) {
        String safeErrorType = errorType != null ? errorType : "UNKNOWN_ERROR";

        if (enableDetailedLogging) {
            String fileName = new File(filePath).getName();
            LOG.fine("验证记录: 文件 " + fileName + " 验证失败: " + safeErrorType);
        }

        if (enableGroupedReporting) {
            errorTypeFileMap.computeIfAbsent(safeErrorType, key -> new ArrayList<>()).add(filePath);
        }
    }

    /**
     * 添加警告记录
     */
    public void addWarning(String filePath, String warningType, String warningMessage) {
        String safeWarningType = warningType != null ? warningType : "UNKNOWN_WARNING";

        if (enableDetailedLogging) {
            String fileName = new File(filePath).getName();
            LOG.fine("验证记录: 文件 " + fileName + " 验证警告: " + safeWarningType);
        }

        if (enableGroupedReporting) {
            errorTypeFileMap.computeIfAbsent(safeWarningType, key -> new ArrayList<>()).add(filePath);
        }
    }

    /**
     * 处理验证错误
     */
    private void onError(String filePath, ValidationResult result) throws DicomException {
        String errorKey = result.getErrorMessage();
        String fileName = new File(filePath).getName();

        switch (errorKey) {
            case "EXAM_EMPTY":
                addWarning(filePath, "EXAM_EMPTY", "缺少检查信息 (StudyInstanceUID)");
                if (skipInvalidFiles) {
                    throw new DicomException(ErrorCode.DICOM_VALIDATION, DicomMessages.VALIDATION_ERROR,"缺少检查信息", fileName);
                }
                break;
            case "SERIES_EMPTY":
                addWarning(filePath, "SERIES_EMPTY", "缺少序列信息 (SeriesInstanceUID)");
                if (skipInvalidFiles) {
                    throw new DicomException(ErrorCode.DICOM_VALIDATION, DicomMessages.VALIDATION_ERROR,"缺少序列信息", fileName);
                }
                break;
            default:
                addError(filePath, errorKey, result.getErrorMessage());
                throw new DicomException(ErrorCode.DICOM_VALIDATION, DicomMessages.VALIDATION_ERROR,result.getErrorMessage(), fileName);
        }
    }

    /**
     * 处理验证异常
     */
    private void onException(String filePath, Exception e) throws DicomException {
        String fileName = new File(filePath).getName();
        addError(filePath, "VALIDATION_EXCEPTION", e.getMessage());
        throw new DicomException(ErrorCode.DICOM_VALIDATION, DicomMessages.VALIDATION_FAILED, e, fileName);
    }

    /**
     * 记录文件列表（通用方法）
     */
    public void logFileList(List<String> files, String description, String itemPrefix) {
        if (files.isEmpty()) {
            return;
        }

        LOG.warning(description + files.size() + " 个文件");

        files.stream()
                .limit(5)
                .map(path -> new File(path).getName())
                .forEach(name -> LOG.warning(itemPrefix + name));

        if (files.size() > 5) {
            LOG.warning("以及其他 " + (files.size() - 5) + " 个文件");
        }
    }

    /**
     * 记录缺少检查信息的文件
     */
    public void logEmptyExamFiles(List<String> emptyExamFiles) {
        logFileList(emptyExamFiles, "发现 ", "缺少检查信息的文件: ");
    }

    /**
     * 记录无效的DICOM文件
     */
    public void logInvalidFiles(List<String> invalidFiles) {
        logFileList(invalidFiles, "发现 ", "无效文件: ");
    }

    /**
     * 打印汇总报告
     */
    public void printSummaryReport() {
        if (!enableGroupedReporting || errorTypeFileMap.isEmpty()) {
            return;
        }

        LOG.warning("验证汇总报告:");

        for (Map.Entry<String, List<String>> entry : errorTypeFileMap.entrySet()) {
            String errorType = entry.getKey();
            List<String> files = entry.getValue();

            LOG.warning("- " + errorType + ": 影响了 " + files.size() + " 个文件");

            int count = 0;
            for (String filePath : files) {
                if (count++ < maxFileExamples) {
                    LOG.warning("    - " + new File(filePath).getName());
                } else {
                    LOG.warning("    - ... 以及其他 " + (files.size() - maxFileExamples) + " 个文件");
                    break;
                }
            }
        }
    }

    /**
     * 获取不同错误类型数量
     */
    public int getDistinctErrorTypeCount() {
        return errorTypeFileMap.size();
    }

    /**
     * 获取受影响文件数量
     */
    public int getAffectedFileCount() {
        return errorTypeFileMap.values().stream()
                .mapToInt(List::size)
                .sum();
    }

    /**
     * 重置错误记录
     */
    public void reset() {
        errorTypeFileMap.clear();
    }
}