package com.ge.med.ct.laf2.theming;

import java.awt.Color;
import java.awt.Font;
import java.awt.Dimension;

/**
 * 主题接口
 * 定义主题必须提供的颜色、字体和尺寸常量
 */
public interface Theme {
    // 主题信息
    String getName();
    String getDisplayName();
    
    // 应用程序常量
    String getAppName();
    String getAppVersion();
    
    // 颜色常量 - 基础
    Color getPrimaryColor();
    Color getSecondaryColor();
    Color getBackgroundColor();
    Color getHeaderBackgroundColor();
    Color getPanelBackgroundColor();
    Color getTextColor();
    Color getTextBrightColor();
    Color getTextLightColor();
    Color getBorderColor();
    
    // 颜色常量 - 状态
    Color getSuccessColor();
    Color getWarningColor();
    Color getErrorColor();
    
    // 颜色常量 - 表格
    Color getTableHeaderColor();
    Color getTableSelectedRowColor();
    Color getTableAlternateRowColor();
    
    // 颜色常量 - 界面元素
    Color getToolbarBackgroundColor();
    Color getMenubarColor();
    Color getButtonBackgroundColor();
    Color getButtonBorderColor();
    Color getFieldBackgroundColor();
    
    // 颜色常量 - 列表
    Color getListHeaderColor();
    Color getListSelectedRowColor();
    Color getListAlternateRowColor();
    Color getListBackgroundColor();
    
    // 字体常量
    Font getHeaderFont();
    Font getTitleFont();
    Font getNormalFont();
    Font getSmallFont();
    Font getDefaultFont();
    Font getLargeFont();
    Font getReportFont();
    Font getTableHeaderFont();
    Font getTableContentFont();
    Font getImageIndexFont();
    Font getListItemFont();
    
    // 尺寸常量
    int getDefaultMargin();
    int getDefaultPadding();
    int getDefaultBorderRadius();
    int getDefaultButtonHeight();
    int getDefaultComponentHeight();
    int getDefaultIconSize();
    Dimension getDefaultButtonSize();
    Dimension getSmallButtonSize();
    Dimension getLargeButtonSize();
    
    // 图标尺寸
    int getIconSmall();
    int getIconMedium();
    int getIconLarge();
    
    // 窗口尺寸
    Dimension getDefaultWindowSize();
    Dimension getDialogSize();
    
    // 动画常量
    int getAnimationDuration();
}
